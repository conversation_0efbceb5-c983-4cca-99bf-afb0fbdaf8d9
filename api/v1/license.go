package v1

// Server license status

type ServerLicenseStatus struct {
	Valid    bool  `json:"valid"`
	ExpireAt int64 `json:"expire_at"`
}

// Terminal status list

type TerminalStatusListRequest struct {
	PagingRequest
	Keyword string `json:"keyword" form:"keyword"`
}

type TerminalStatusItem struct {
	AliasName        string `json:"alias_name"`
	MacAddress       string `json:"mac_address"`
	Registered       bool   `json:"registered"`
	RegistrationCode string `json:"registration_code"`
	CreatedAt        int64  `json:"created_at"`
}

type TerminalStatusListResponse struct {
	Total int64                `json:"total"`
	List  []TerminalStatusItem `json:"list"`
}

// Generate server license

type GenerateServerLicenseRequest struct {
	MachineCode string `json:"machine_code" form:"machine_code"`
	ExpireAt    int64  `json:"expire_at"`
}

