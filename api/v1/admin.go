package v1

import (
	
"esop/internal/model"

)


type AdminData struct {
	ID          int    `json:"id"`            // 编号
	Name        string `json:"name"`          // 账号名称
	Account     string `json:"account"`       // 账号
	Level       int8   `json:"level"`         // 账号级别 0--管理员 1--用户
	SmVideoName int8   `json:"is_stop_using"` // 是否停用 0--否 1--是
	CreatedAt   int64  `json:"created_at"`    // 创建时间
	UpdatedAt   int64  `json:"updated_at"`    // 更新时间
	IsDeleted   int8   `json:"is_deleted"`    // 是否删除
}

type AdminListRequest struct {
	PagingRequest
	Name           string `json:"name" uri:"name" form:"name"`                                     //账号名称
	Account        string `json:"account" uri:"sm_name" form:"account"`                            //账号
	CreatedAtStart int64  `json:"created_at_start" uri:"created_at_start" form:"created_at_start"` // 创建时间
	CreatedAtEnd   int64  `json:"created_at_end" uri:"created_at_end" form:"created_at_end"`       // 更新时间

}

type UpdateAdminUsingRequest struct {
	Id          int  `json:"id" uri:"id" form:"id"`                                  //id
	IsStopUsing int8 `json:"is_stop_using" uri:"is_stop_using" form:"is_stop_using"` //是否停用账号  1--停止 0--正常
}

type SaveAdminListRequest struct {
	Name      string `json:"name"  form:"name"`             // 账号名称
	Account   string `json:"account"  form:"account"`       // 账号
	Password  string    `json:"password"  form:"password"`     // 密码
	CreatedAt int64  `json:"created_at"  form:"created_at"` // 创建时间
	UpdatedAt int64  `json:"updated_at"  form:"updated_at"` // 更新时间
	IsDeleted int8   `json:"is_deleted"  form:"is_deleted"` // 是否删除

}
type LoginRequest struct {
	Account string `form:"account" json:"account" binding:"required" example:"admin"`
	Password string `form:"password" json:"password" binding:"required" example:"admin"`
}
type LoginResponseData struct {
	AccessToken string `json:"accessToken"`
	// UserInfo
}
type AdminToken struct {
    Admin *model.Admin
    Token string
}
