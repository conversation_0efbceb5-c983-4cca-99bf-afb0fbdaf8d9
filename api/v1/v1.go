package v1

import (
	"errors"
	"net/http"

	"github.com/gin-gonic/gin"
)

type Response struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data"`
}

type PagingResponse struct {
	PagingData
	Data interface{} `json:"data"`
}
type PagingRequest struct {
	Page     int `uri:"page" form:"page" json:"page" default:"1"`
	PageSize int `uri:"pageSize" form:"pageSize" json:"pageSize" default:"20"`
}

// type HistoryRequest struct {
// 	Page     int `uri:"page" form:"page" json:"page"`
// 	PageSize int `uri:"pageSize" form:"pageSize" json:"pageSize"`
// 	AvmId   int `uri:"page" form:"page" json:"page"`
// }

type PagingData struct {
	Total       int64 `json:"total"`        // 数据总量
	CurrentPage int   `json:"current_page"` // 当前页码
	PerPage     int   `json:"per_page"`     // 每页数量
	LastPage    int   `json:"last_page"`    // 最后一页的页码
}

func HandleSuccess(ctx *gin.Context, msg error, data interface{}) {

	if data == nil {
		data = map[string]interface{}{}

	}

	if msg == nil {
		msg = ReponseSuccess
	}
	resp := Response{Code: errorCodeMap[msg], Message: msg.Error(), Data: data}
	if _, ok := errorCodeMap[msg]; !ok {
		resp = Response{Code: 0, Message: "", Data: data}
	}
	ctx.JSON(http.StatusOK, resp)
}

func HandleError(ctx *gin.Context, httpCode int, err error, data interface{}) {
	if data == nil {
		data = map[string]string{}
	}
	resp := Response{Code: errorCodeMap[err], Message: err.Error(), Data: data}
	if _, ok := errorCodeMap[ReponseSuccess]; !ok {
		resp = Response{Code: 500, Message: "unknown error", Data: data}
	}
	ctx.JSON(httpCode, resp)
}
func Paging(total int64, PagingRequest PagingRequest) *PagingData {
	if PagingRequest.PageSize == 0 {
		PagingRequest.PageSize = 20
	}
	lastpage := int(total) / PagingRequest.PageSize
	return &PagingData{
		Total:       total,
		CurrentPage: PagingRequest.Page,
		LastPage:    Pagingternary(int(total)%PagingRequest.PageSize == 0, int(total)/PagingRequest.PageSize, lastpage+1),
		PerPage:     PagingRequest.PageSize,
	}

}

// Pagingternary 三目运算的函数
func Pagingternary(a bool, b, c int) int {
	if a {
		return b
	}
	return c
}

type Error struct {
	Code    int
	Message string
}

var errorCodeMap = map[error]int{}

func NewError(code int, msg string) error {
	err := errors.New(msg)
	errorCodeMap[err] = code
	return err
}

func (e Error) Error() string {
	return e.Message
}
