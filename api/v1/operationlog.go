package v1

type OperationLogRequest struct {
	PagingRequest
	AdminName      string `json:"admin_name" uri:"name" form:"admin_name"`                         //操作人名称
	AdminAccount   string `json:"admin_account" uri:"sm_name" form:"admin_account"`                //操作人账号
	Module         string `json:"module" uri:"sm_name" form:"module"`                              // 操作模块
	ModuleId       int    `json:"module_id" uri:"module_id" form:"module_id"`                      // 模块Id
	CreatedAtStart int64  `json:"created_at_start" uri:"created_at_start" form:"created_at_start"` // 创建时间
	CreatedAtEnd   int64  `json:"created_at_end" uri:"created_at_end" form:"created_at_end"`       // 更新时间

}

type OperationLogData struct {
	ID           int    `json:"id"`            // 编号
	AdminName    string `json:"admin_name"`    // 操作人名称
	AdminAccount string `json:"admin_account"` // 操作人账号
	Module       string `json:"module"`        // 操作模块
	Action       string `json:"action"`        // 操作动作
	ModuleId     int    `json:"module_id"`     // 模块Id
	CreatedAt    int64  `json:"created_at"`    // 创建时间
	UpdatedAt    int64  `json:"updated_at"`    // 更新时间
}

const (
	// 操作动作常量
	ActionCreate = "新增"
	ActionEdit   = "编辑"
	ActionDelete = "删除"

	// 模块常量--test
	ModuleTemplate       = "模板"
	ModuleSourceMaterial = "素材"
	ModuleAdmin          = "账号"
	ModuleEquipment      = "设备"
	ModuleResourcePack   = "打包"
)
