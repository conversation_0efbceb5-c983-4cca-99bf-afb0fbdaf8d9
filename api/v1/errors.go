package v1

var (

	// common errors

	ErrBadRequest          = NewError(400, "Bad Request")
	ErrUnauthorized        = NewError(401, "Unauthorized")
	ErrNotFound            = NewError(404, "Not Found")
	ErrInternalServerError = NewError(500, "Internal Server Error")
	ErrPramaterError       = NewError(1002, "参数错误")
	ErrUpdateError         = NewError(1005, "更新失败")
	ErrAccountNotFound     = NewError(1006, "账号不存在")
	ErrPasswordError       = NewError(1007, "密码错误")
	ErrDirectoryNotFound   = NewError(1008, "目录不存在")
	ErrCreateFileFailed    = NewError(1011, "创建文件失败")
	ErrCopyFileFailed      = NewError(1012, "复制文件失败")
	ErrDataExists          = NewError(1003, "数据已存在")
	ErrInvalidGenerateKey  = NewError(1004, "激活码无效")
	ErrDataNotExists       = NewError(1005, "数据不存在")
)
