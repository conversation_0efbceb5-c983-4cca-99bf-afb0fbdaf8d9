package v1

// MaterialGroupListRequest 分组列表请求
type MaterialGroupListRequest struct {
	PagingRequest
	Name string `json:"name" uri:"name" form:"name"` // 分组名称
}

// MaterialGroupData 分组数据
type MaterialGroupData struct {
	ID          int    `json:"id"`          // 分组ID
	Name        string `json:"name"`        // 分组名称
	Description string `json:"description"` // 分组描述
	Color       string `json:"color"`       // 分组颜色
	SortOrder   int    `json:"sort_order"`  // 排序
	MaterialCount int  `json:"material_count"` // 素材数量
	CreatedAt   int64  `json:"created_at"`  // 创建时间
	UpdatedAt   int64  `json:"updated_at"`  // 更新时间
}

// SaveMaterialGroupRequest 保存分组请求
type SaveMaterialGroupRequest struct {
	Name        string `json:"name" binding:"required"`        // 分组名称
	Description string `json:"description"`                    // 分组描述
	Color       string `json:"color"`                          // 分组颜色
	SortOrder   int    `json:"sort_order"`                     // 排序
}

// MoveMaterialsToGroupRequest 移动素材到分组请求
type MoveMaterialsToGroupRequest struct {
	MaterialIds []int `json:"material_ids" binding:"required"` // 素材ID列表
	GroupId     int   `json:"group_id"`                        // 目标分组ID，0表示未分组
}