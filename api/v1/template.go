package v1

type TemplateData struct {
	ID               int                `json:"id"`   // 编号
	Name             string             `json:"name"` // 模板名称
	Type             int                `json:"type"`
	ResolutionRatio  string             `json:"resolution_ratio"`                         // 分辨率
	CanvasRatio      string             `json:"canvas_ratio"`                             // 画布尺寸
	Width            int                `json:"width"`                                    // 宽度
	Height           int                `json:"height"`                                   // 高度
	SwipterTime      int                `json:"swipter_time"`                             // 轮播时间
	CreatedAt        int64              `json:"created_at"`                               // 创建时间
	UpdatedAt        int64              `json:"updated_at"`                               // 更新时间
	IsDeleted        int8               `json:"is_deleted"`                               // 是否删除
	TemplateSmDetail []TemplateSmDetail `json:"template_sm" gorm:"foreignKey:TemplateId"` // 模板素材数据
}

type TemplateListRequest struct {
	PagingRequest
	Name           string `json:"name" uri:"name" form:"name"`
	CreatedAtStart int64  `json:"created_at_start" uri:"created_at_start" form:"created_at_start"` // 创建时间
	CreatedAtEnd   int64  `json:"created_at_end" uri:"created_at_end" form:"created_at_end"`       // 更新时间
}

type SaveTemplateListRequest struct {
	Name            string       `json:"name"`
	ResolutionRatio string       `json:"resolution_ratio"`
	CanvasRatio     string       `json:"canvas_ratio"`
	SwipterTime     int          `json:"swipter_time"`
	Type            int          `json:"type"`
	TemplateSm      []TemplateSm `json:"template_sm"`
}
type MultiFiles struct {
	ClientKey      int     `json:"clientKey"`
	Type           int     `json:"type"`
	TemplateSmType int     `json:"template_sm_type"`
	Path           string  `json:"path"`
	SmId           int     `json:"sm_id"`
	SmName         string  `json:"sm_name"`
	SourceWidth    float64 `json:"source_width"`
	SourceHeight   float64 `json:"source_height"`
	IntervalTime   int     `json:"interval_time"`
}

type TemplateSm struct {
	ClientKey         int          `json:"clientKey"`
	Type              int          `json:"type"`
	TemplateSmType    int          `json:"template_sm_type"`
	TemplateIndex     int          `json:"template_index"`
	Path              string       `json:"path"`
	SmId              int          `json:"sm_id"`
	SmName            string       `json:"sm_name"`
	XAxis             int          `json:"x_axis"`
	YAxis             int          `json:"y_axis"`
	Width             float64      `json:"width"`
	Height            float64      `json:"height"`
	SourceWidth       float64      `json:"source_width"`
	SourceHeight      float64      `json:"source_height"`
	TemplatePage      int          `json:"template_page"`
	BackgroundDisplay string       `json:"background_display"`
	MultiFiles        []MultiFiles `json:"multiFiles"`
	MarqueeText       string       `json:"marquee_text"`
	FontSize          int          `json:"font_size"`
	ScrollSpeed       int          `json:"scroll_speed"`
	FontColor         string       `json:"font_color"`
	BackgroundColor   string       `json:"background_color"`
}

type SavePackRequest struct {
	Id               int    `json:"id" uri:"id" form:"id"`
	Name             string `json:"name" form:"name"` // 别名
	HtmlContent      string `json:"html_content"  form:"html_content"`
	ResourcePackName string `json:"resource_pack_name"  form:"resource_pack_name"` // 包名称
	Type             int    `json:"type"  form:"type"`
}
type TemplateSmDetail struct {
	TemplateId        int          `gorm:"column:template_id" json:"template_id" form:"template_id"`
	SmId              int          `json:"sm_id" form:"sm_id"`
	Width             float64      `json:"width" form:"width"`
	Height            float64      `json:"height" form:"height"`
	SourceWidth       float64      `json:"source_width" form:"source_width"`
	SourceHeight      float64      `json:"source_height" form:"source_height"`
	XAxis             int          `json:"x_axis" form:"x_axis"`
	YAxis             int          `json:"y_axis" form:"y_axis"`
	SmName            string       `json:"sm_name" form:"sm_name"`
	Path              string       `json:"path" form:"path"`
	Url               string       `json:"url" form:"url"`
	Type              int          `json:"type" form:"type"`
	TemplateSmType    int8         `json:"template_sm_type"  form:"template_sm_type"` // 副表类型 1--素材表 2--时钟3--
	TemplatePage      int8         `json:"template_page"  form:"template_page"`
	BackgroundDisplay string       `json:"background_display" form:"background_display"`
	TemplateIndex     int          `json:"template_index"  form:"template_index"`
	IntervalTime      int          `json:"interval_time" form:"interval_time"`    // 轮播图间隔时间
	MultiFilesStr     string       `gorm:"column:multi_files" json:"-"`           // 数据库中的多文件JSON字符串
	MultiFiles        []MultiFiles `gorm:"-" json:"multiFiles" form:"multiFiles"` // 多文件
	// 文本跑马灯相关字段
	MarqueeText     string `json:"marquee_text" form:"marquee_text"`
	FontSize        int    `json:"font_size" form:"font_size"`
	ScrollSpeed     int    `json:"scroll_speed" form:"scroll_speed"`
	FontColor       string `json:"font_color" form:"font_color"`
	BackgroundColor string `json:"background_color" form:"background_color"`
}
