package v1

type SourceMaterialListRequest struct {
	PagingRequest
	Name           string `json:"name" uri:"name" form:"name"`                                     //名称
	Type           int    `json:"type" uri:"type" form:"type"`                                     //类型 1--图片 2--视频
	GroupId        *int   `json:"group_id" uri:"group_id" form:"group_id"`                         //分组ID，nil表示不筛选，0表示未分组
	CreatedAtStart int64  `json:"created_at_start" uri:"created_at_start" form:"created_at_start"` // 创建时间
	CreatedAtEnd   int64  `json:"created_at_end" uri:"created_at_end" form:"created_at_end"`       // 更新时间

}

type SourceMaterialData struct {
	ID           int    `json:"id"`            // 编号
	Name         string `json:"name"`          // 模板名称
	Type         int    `json:"type"`          //类型 1--图片 2--视频
	ContentType  string `json:"content_type"` // 素材类型
	Path         string `json:"path"`          // 素材路径
	SourceWidth  int    `json:"source_width"`  // 素材宽度
	SourceHeight int    `json:"source_height"` // 素材高度
	Size         int64  `json:"size"`          // 文件大小
	CreatedAt    int64  `json:"created_at"`    // 创建时间
	UpdatedAt    int64  `json:"updated_at"`    // 更新时间
	IsDeleted    int    `json:"is_deleted"`    // 是否删除
}

type SaveSourceMaterialRequest struct {
	ID           int    `json:"id"`            // 编号
	Name         string `json:"name"`          // 模板名称
	Type         int    `json:"type"`          //类型 1--图片 2--视频
	Path         string `json:"path"`          // 素材路径
	SourceWidth  int    `json:"source_width"`  // 源宽度
	SourceHeight int    `json:"source_height"` // 源高度
	ContentType  string `json:"content_type"`  // 内容类型
	Size         int64  `json:"size"`
	EquipmentId  int    `json:"equipment_id"`  // 设备ID
	CreatedAt    int64  `json:"created_at"`    // 创建时间
	UpdatedAt    int64  `json:"updated_at"`    // 更新时间
	IsDeleted    int    `json:"is_deleted"`    // 是否删除
}

type BatchUploadResponse struct {
	SuccessCount int      `json:"success_count"`
	FailureCount int      `json:"failure_count"`
	FailedFiles  []string `json:"failed_files"`
}

// BatchDeleteRequest defines the request body for batch deleting materials.
type BatchDeleteRequest struct {
	IDs []int64 `json:"ids"`
}
