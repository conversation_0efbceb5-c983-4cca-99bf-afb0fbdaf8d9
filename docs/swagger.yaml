definitions:
  github_com_go-nunu_nunu-layout-advanced_api_v1.GetProfileResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/github_com_go-nunu_nunu-layout-advanced_api_v1.GetProfileResponseData'
      message:
        type: string
    type: object
  github_com_go-nunu_nunu-layout-advanced_api_v1.GetProfileResponseData:
    properties:
      nickname:
        example: alan
        type: string
      userId:
        type: string
    type: object
  github_com_go-nunu_nunu-layout-advanced_api_v1.LoginRequest:
    properties:
      email:
        example: <EMAIL>
        type: string
      password:
        example: "123456"
        type: string
    required:
    - email
    - password
    type: object
  github_com_go-nunu_nunu-layout-advanced_api_v1.LoginResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/github_com_go-nunu_nunu-layout-advanced_api_v1.LoginResponseData'
      message:
        type: string
    type: object
  github_com_go-nunu_nunu-layout-advanced_api_v1.LoginResponseData:
    properties:
      accessToken:
        type: string
    type: object
  github_com_go-nunu_nunu-layout-advanced_api_v1.RegisterRequest:
    properties:
      email:
        example: <EMAIL>
        type: string
      password:
        example: "123456"
        type: string
    required:
    - email
    - password
    type: object
  github_com_go-nunu_nunu-layout-advanced_api_v1.Response:
    properties:
      code:
        type: integer
      data: {}
      message:
        type: string
    type: object
  github_com_go-nunu_nunu-layout-advanced_api_v1.UpdateProfileRequest:
    properties:
      email:
        example: <EMAIL>
        type: string
      nickname:
        example: alan
        type: string
    required:
    - email
    type: object
host: localhost:8000
info:
  contact:
    email: <EMAIL>
    name: API Support
    url: http://www.swagger.io/support
  description: This is a sample server celler server.
  license:
    name: Apache 2.0
    url: http://www.apache.org/licenses/LICENSE-2.0.html
  termsOfService: http://swagger.io/terms/
  title: Nunu Example API
  version: 1.0.0
paths:
  /login:
    post:
      consumes:
      - application/json
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/github_com_go-nunu_nunu-layout-advanced_api_v1.LoginRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/github_com_go-nunu_nunu-layout-advanced_api_v1.LoginResponse'
      summary: 账号登录
      tags:
      - 用户模块
  /register:
    post:
      consumes:
      - application/json
      description: 目前只支持邮箱登录
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/github_com_go-nunu_nunu-layout-advanced_api_v1.RegisterRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/github_com_go-nunu_nunu-layout-advanced_api_v1.Response'
      summary: 用户注册
      tags:
      - 用户模块
  /user:
    get:
      consumes:
      - application/json
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/github_com_go-nunu_nunu-layout-advanced_api_v1.GetProfileResponse'
      security:
      - Bearer: []
      summary: 获取用户信息
      tags:
      - 用户模块
    put:
      consumes:
      - application/json
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/github_com_go-nunu_nunu-layout-advanced_api_v1.UpdateProfileRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/github_com_go-nunu_nunu-layout-advanced_api_v1.Response'
      security:
      - Bearer: []
      summary: 修改用户信息
      tags:
      - 用户模块
securityDefinitions:
  Bearer:
    in: header
    name: Authorization
    type: apiKey
swagger: "2.0"
