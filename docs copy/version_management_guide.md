# 版本号管理指南

## 概述

本项目实现了一套完整的版本号管理系统，用于自动生成、管理和跟踪应用版本号。版本号会在设备注册时自动提交到服务器。

## 功能特性

- 🔄 自动生成版本号（基于日期格式：YYYY.MM.DD+构建号）
- 📱 跨平台支持（Android、iOS、Windows、macOS、Linux）
- 💾 本地持久化存储
- 🚀 自动递增构建号
- 📊 详细的版本信息收集
- 🔧 便捷的工具类接口
- ⚡ 自动更新机制

## 版本号格式

```
格式: YYYY.MM.DD+构建号
示例: 2025.01.19+5
```

- **YYYY.MM.DD**: 基于当前日期的主版本号
- **构建号**: 自动递增的构建编号

## 核心组件

### 1. VersionService (版本服务)

位置: `lib/services/version_service.dart`

主要功能:
- 生成和管理版本号
- 本地存储版本信息
- 获取设备信息
- 自动更新机制

### 2. VersionUtils (版本工具类)

位置: `lib/utils/version_utils.dart`

提供便捷的静态方法:
- `getCurrentVersion()`: 获取当前版本号
- `incrementVersion()`: 递增版本号
- `getFormattedVersion()`: 获取格式化版本信息
- `getApiVersionInfo()`: 获取API提交用的版本信息

## 使用方法

### 基础使用

```dart
import '../utils/version_utils.dart';

// 获取当前版本号
String version = await VersionUtils.getCurrentVersion();
print('当前版本: $version'); // 输出: 当前版本: 2025.01.19+5

// 递增版本号
String newVersion = await VersionUtils.incrementVersion();
print('新版本: $newVersion'); // 输出: 新版本: 2025.01.19+6

// 获取格式化版本信息
String formatted = await VersionUtils.getFormattedVersion();
print(formatted); // 输出: 版本 2025.01.19+6 (构建号: 6)
```

### 在设备注册中的应用

版本号已自动集成到设备注册流程中：

```dart
// 在 EquipmentApiService.addEquipment() 方法中
final appVersion = await _versionService.getCurrentVersion();

final body = {
  'name': deviceAlias,
  'mac_address': macAddress,
  'group_name': groupName,
  'alias_name': aliasName,
  'ip_addr': ipAddress,
  'registration_code': registrationCode,
  'app_version': appVersion, // 自动添加版本号
  ...deviceInfo,
};
```

### 获取详细版本信息

```dart
Map<String, dynamic> versionInfo = await VersionUtils.getVersionInfo();
print('详细信息: $versionInfo');

// 输出示例:
// {
//   "version": "2025.01.19+5",
//   "build_number": 5,
//   "last_update_time": "2025-01-19T10:30:45.123Z",
//   "device_info": {
//     "device_model": "SM-G991B",
//     "device_brand": "samsung",
//     "android_version": "11"
//   },
//   "platform": "android",
//   "generated_at": "2025-01-19T10:30:45.123Z"
// }
```

### 自动更新机制

```dart
// 检查是否需要更新版本号（默认1小时间隔）
bool shouldUpdate = await VersionUtils.shouldUpdateVersion();

// 自动更新（如果需要的话）
String version = await VersionUtils.autoUpdateIfNeeded();
```

### 手动管理

```dart
// 手动设置版本号
await VersionUtils.setVersion('2025.01.20+1');

// 重置版本号
await VersionUtils.resetVersion();
```

## 集成到UI中

参考示例文件: `lib/examples/version_usage_example.dart`

```dart
class MyVersionWidget extends StatefulWidget {
  @override
  _MyVersionWidgetState createState() => _MyVersionWidgetState();
}

class _MyVersionWidgetState extends State<MyVersionWidget> {
  String _version = '';

  @override
  void initState() {
    super.initState();
    _loadVersion();
  }

  Future<void> _loadVersion() async {
    final version = await VersionUtils.getCurrentVersion();
    setState(() {
      _version = version;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Text('版本: $_version');
  }
}
```

## API集成

获取用于API提交的版本信息：

```dart
Map<String, String> apiInfo = await VersionUtils.getApiVersionInfo();
// 返回:
// {
//   "app_version": "2025.01.19+5",
//   "build_number": "5",
//   "platform": "android",
//   "generated_at": "2025-01-19T10:30:45.123Z"
// }
```

## 配置选项

### 自动更新间隔

```dart
// 自定义更新间隔（默认1小时）
bool shouldUpdate = await _versionService.shouldUpdateVersion(
  interval: Duration(minutes: 30), // 30分钟间隔
);
```

### 版本号格式自定义

如需自定义版本号格式，可以修改 `VersionService._generateNewVersion()` 方法。

## 最佳实践

1. **应用启动时初始化**
   ```dart
   void main() async {
     WidgetsFlutterBinding.ensureInitialized();

     // 初始化版本号
     await VersionUtils.autoUpdateIfNeeded();

     runApp(MyApp());
   }
   ```

2. **在关键操作前更新版本号**
   ```dart
   // 在设备注册前
   await VersionUtils.autoUpdateIfNeeded();

   // 在重要API调用前
   await equipmentApiService.addEquipment(...);
   ```

3. **错误处理**
   ```dart
   try {
     String version = await VersionUtils.getCurrentVersion();
   } catch (e) {
     debugPrint('获取版本号失败: $e');
     // 使用默认版本号或其他处理逻辑
   }
   ```

4. **调试和日志**
   ```dart
   // 在调试模式下显示详细版本信息
   if (kDebugMode) {
     final versionInfo = await VersionUtils.getVersionInfo();
     debugPrint('版本信息: $versionInfo');
   }
   ```

## 故障排除

### 常见问题

1. **版本号获取失败**
   - 检查 SharedPreferences 权限
   - 确保设备信息获取正常

2. **版本号不更新**
   - 检查时间间隔设置
   - 验证本地存储是否正常

3. **设备信息获取失败**
   - 确保 device_info_plus 插件正常工作
   - 检查平台特定的权限设置

### 调试方法

```dart
// 启用详细日志
final versionInfo = await VersionUtils.getVersionInfo();
debugPrint('完整版本信息: $versionInfo');

// 检查存储状态
final prefs = await SharedPreferences.getInstance();
debugPrint('存储的版本号: ${prefs.getString('app_version')}');
debugPrint('存储的构建号: ${prefs.getInt('build_number')}');
```

## 更新日志

- **v1.0.0**: 初始版本，基础版本号管理功能
- **v1.1.0**: 添加自动更新机制和工具类
- **v1.2.0**: 集成到设备注册流程
- **v1.3.0**: 添加详细的设备信息收集

## 贡献

如需扩展版本号管理功能，请：

1. 在 `VersionService` 中添加核心逻辑
2. 在 `VersionUtils` 中添加便捷方法
3. 更新相关文档和示例
4. 添加适当的测试用例