# MQTT 客户端模块重构指南

本文档旨在说明新版 `MqttService` 模块的架构设计、核心功能、配置方法以及如何将其集成到现有项目中。

## 1. 架构设计

重构后的 `MqttService` 遵循高内聚、低耦合的原则，将所有 MQTT 相关逻辑封装在单一服务中。其核心设计思想是**自主管理**和**对上层透明**。

-   **自主管理**：`MqttService` 内部处理所有复杂的连接、断连、重连、订阅恢复和消息缓冲逻辑，无需外部干预。
-   **对上层透明**：业务层（如 `MqttProvider` 或其他服务）只需调用简洁的 API (`connect`, `disconnect`, `subscribe`, `publish`)，并通过 `Stream` 来响应连接状态变化和接收消息，无需关心底层实现的复杂性。

### 核心组件

-   **`MqttService`**: 核心服务类，管理 MQTT 客户端生命周期。
-   **`MqttProvider`**: 作为 `MqttService` 与 Flutter UI 层的桥梁，利用 `ChangeNotifier` 将连接状态和错误信息反应到视图上。
-   **`SettingsService` / `SettingsModel`**: 负责持久化存储 MQTT 的所有配置参数，包括服务器地址、端口、用户名、密码等。

## 2. 核心功能详解

### 2.1. 健壮的连接管理

-   **配置化**：所有连接参数（服务器、端口、客户端ID、用户名、密码）均从 `SettingsService` 动态加载，杜绝了任何硬编码。
-   **状态机**：通过 `MqttConnectionState` 枚举清晰地管理连接状态（`disconnected`, `connecting`, `connected`, `disconnecting`, `error`）。

### 2.2. 智能自动重连

-   **指数退避策略 (Exponential Backoff)**：当连接意外断开时，模块会自动尝试重连。重连的延迟时间会随着连续失败次数的增加而指数级增长（例如，2s, 4s, 8s, ...），直到达到设定的最大延迟（60秒）和最大尝试次数（10次），避免了在网络或服务器故障时对系统造成冲击。
-   **自动恢复**：一旦重连成功，`MqttService` 会自动完成两项关键任务：
    1.  **重新订阅**：自动订阅之前所有成功订阅过的主题。
    2.  **发送离线消息**：将断连期间缓存的所有 QoS > 0 的消息按顺序发送出去。

### 2.3. 消息持久化与 QoS 保证

-   **离线缓冲**：当客户端处于非连接状态时，任何 `publish` 调用（QoS > 0）都会被暂存到一个内存队列中。这确保了在网络不佳时，重要消息不会丢失。
-   **QoS 支持**：`publish` 和 `subscribe` 方法均支持设置 QoS 等级，确保消息的传输可靠性。

### 2.4. 错误处理与日志

-   **全面的错误捕获**：在 `connect` 等关键操作中使用了 `try-catch` 块来捕获异常。
-   **结构化日志**：使用 `debugPrint` 打印详细的运行时信息，包括连接状态、重连尝试、消息收发等，便于调试和问题排查。
-   **错误上报**：集成了 `ErrorHandlerService`，可将关键错误（如连接失败）上报到监控系统。

## 3. 如何配置

所有 MQTT 相关配置都存储在 `SettingsModel` 中，并通过 `SettingsService` 进行读写。

要修改配置，请使用 `SettingsService` 提供的相应方法。例如，在应用的设置页面：

```dart
// 示例：在设置页面更新 MQTT 服务器地址
final settingsService = SettingsService();
await settingsService.updateMqttServerAddress('new.mqtt.broker.com');
await settingsService.updateMqttPort('1884');
await settingsService.updateMqttUsername('newUser');
await settingsService.updateMqttPassword('newPassword');

// 修改后，需要重新触发 MqttService 的连接逻辑
mqttProvider.disconnect();
mqttProvider.connect();
```

### 可配置项

-   `mqttServerAddress`
-   `mqttPort`
-   `mqttUsername`
-   `mqttPassword`
-   `mqttTopic` (用于默认订阅)
-   `groupName`
-   `deviceAlias`

## 4. 如何集成

新模块的设计保持了对上层 API 的兼容性，集成过程非常简单。

### 4.1. 初始化

在应用启动时（例如，在 `main.dart` 或根 `Widget` 的 `initState` 中），初始化 `MqttProvider`。

```dart
// main.dart or a top-level provider setup
void main() {
  runApp(
    MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => MqttProvider()..initialize()),
        // ... other providers
      ],
      child: MyApp(),
    ),
  );
}
```

`MqttProvider` 的 `initialize()` 方法会自动监听连接状态变化并首次尝试连接。

### 4.2. 监听连接状态

在任何需要展示连接状态的 UI 组件中，使用 `Consumer` 或 `context.watch` 来获取 `MqttProvider` 的状态。

```dart
// home_screen.dart
Consumer<MqttProvider>(
  builder: (context, mqttProvider, child) {
    final isConnected = mqttProvider.isConnected;
    return Text(
      isConnected ? 'MQTT 已连接' : 'MQTT 连接断开',
      style: TextStyle(color: isConnected ? Colors.green : Colors.red),
    );
  },
)
```

### 4.3. 订阅与发布

从 `MqttProvider` 获取 `MqttService` 实例（或直接通过 `MqttService` 单例）来调用 `subscribe` 和 `publish`。

```dart
// 获取 MqttService
final mqttService = MqttService(); // Assuming it's a singleton or passed via DI

// 订阅主题
mqttService.subscribe('my/custom/topic', MqttQos.atLeastOnce);

// 发布消息
mqttService.publish('my/custom/topic', '{"key":"value"}', MqttQos.atLeastOnce);
```

### 4.4. 接收消息

通过 `MqttProvider` 监听消息流。

```dart
// In a stateful widget's initState
@override
void initState() {
  super.initState();
  final mqttProvider = Provider.of<MqttProvider>(context, listen: false);
  mqttProvider.listenForMessages((MqttMessageModel message) {
    // 在这里处理接收到的消息
    print('Received message: ${message.toJson()}');
  });
}
```

通过以上步骤，新的 MQTT 模块即可无缝集成到现有应用中，提供前所未有的稳定性和可靠性。