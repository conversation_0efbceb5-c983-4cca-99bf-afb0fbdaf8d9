# Stage 1: Build the frontend
FROM node:22.14.0-alpine AS node-builder
RUN set -eux && sed -i 's/dl-cdn.alpinelinux.org/mirrors.ustc.edu.cn/g' /etc/apk/repositories
WORKDIR /data/app/web
COPY ../../web /data/app/web
RUN npm install --registry=https://registry.npmmirror.com
RUN npm run build

# Stage 2: Build the backend
FROM golang:1.24.6-alpine3.22 AS builder
RUN set -eux && sed -i 's/dl-cdn.alpinelinux.org/mirrors.ustc.edu.cn/g' /etc/apk/repositories

ARG APP_RELATIVE_PATH
ARG APP_CONF
COPY ../../ /data/app
COPY --from=node-builder /data/app/web/dist /data/app/web/dist

WORKDIR /data/app
ENV GOOS=linux
ENV GOARCH=amd64
ENV CGO_ENABLED=1
ENV CGO_CFLAGS="-D_LARGEFILE64_SOURCE"
ENV GOPROXY=https://goproxy.cn,direct



RUN apk add --update gcc musl-dev
RUN apk --no-cache add build-base
RUN rm -rf /data/app/bin/
RUN go mod tidy
RUN GOOS=linux  GOARCH=amd64 CGO_ENABLED=1 go build -ldflags="-s -w" -o ./bin/server ${APP_RELATIVE_PATH}
RUN mv config /data/app/bin/


FROM alpine:3.19
RUN set -eux && sed -i 's/dl-cdn.alpinelinux.org/mirrors.ustc.edu.cn/g' /etc/apk/repositories


RUN apk add tzdata && cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime \
    && echo "Asia/Shanghai" > /etc/timezone \
    && apk del tzdata


ARG APP_ENV
ARG APP_CONF
ENV APP_ENV=${APP_ENV}
ENV APP_CONF=${APP_CONF}

COPY --from=builder /data/app/bin /data/app
COPY --from=builder /data/app/storage /data/app/storage
COPY --from=builder /data/app/assets /data/app/assets
COPY --from=builder /data/app/bin/config /data/app/config
# COPY --from=builder /data/app/db /data/app/db

WORKDIR /data/app
EXPOSE 8567
ENTRYPOINT [ "./server" ]
