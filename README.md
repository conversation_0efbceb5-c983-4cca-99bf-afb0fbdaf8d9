# 项目分析报告

## 1. 项目概述
项目名称: Nunu - Go语言应用程序构建工具
项目来源: 基于League of Legends游戏中的Nunu角色命名，寓意"站在巨人肩膀上"
项目定位: 一个CLI工具，用于构建Go应用程序

## 2. 项目架构
### 2.1 整体结构
- 使用流行的Go生态库组合构建
- 遵循分层架构设计
- 支持快速构建高效可靠的应用程序

### 2.2 目录结构
```
├── api/                # API接口定义
├── cmd/                # 命令行工具
├── internal/           # 项目内部模块
│   ├── handler/        # 请求处理
│   ├── middleware/     # 中间件
│   ├── model/          # 数据模型
│   ├── repository/     # 数据访问层
│   ├── server/         # 服务启动配置
│   └── service/        # 业务逻辑层
├── pkg/                # 公共库和工具包
│   ├── app/            # 应用基础组件
│   ├── config/         # 配置管理
│   ├── helper/         # 辅助工具
│   ├── jwt/            # JWT鉴权
│   ├── log/            # 日志处理
│   ├── server/         # 服务基础组件
│   ├── sid/            # SID处理
│   └── zapgorm2/       # 日志与ORM集成
```

## 3. 技术细节
### 3.1 技术栈
- Go语言: 使用Go 1.23.0版本
- Web框架: Gin
- 数据库: MySQL驱动(gorm.io/driver/mysql)，支持SQLCipher加密
- 依赖注入: Google Wire
- 日志: go.uber.org/zap, gopkg.in/natefinch/lumberjack.v2
- 配置管理: github.com/spf13/viper
- 单元测试: github.com/golang/mock
- 文档生成: swaggo/swag

### 3.2 功能特性
- 提供HTTP服务
- 支持数据库迁移
- 支持任务调度
- 提供API文档生成能力
- 支持Mock测试和单元测试
- 支持Docker部署
- 使用Wire进行依赖注入
- 支持Swagger API文档生成
- 提供多环境配置支持(local, prod)
- 支持跨域、JWT鉴权等中间件

## 4. 开发与部署
### 4.1 开发环境
- Go 1.23+
- Docker(用于部署)
- Make(用于构建流程)

### 4.2 构建与运行
- 构建命令: `make build`
- 本地开发: `make bootstrap`(启动Docker依赖并运行服务)
- 部署: `docker build` 和 `docker run` 命令用于容器化部署
- Windows交叉编译: `make windows`

## 5. 其他
- 许可证: MIT License
- 文档: 提供用户指南、架构说明、入门教程等文档
- 单元测试: 支持单元测试和Mock测试

## 6. 前端分析
### 6.1 项目概述
项目名称: esop管理后台
项目定位: 一个基于Vue.js的管理后台，用于与后端服务进行交互

### 6.2 技术栈
- Vue.js: 使用Vue 2.6.14版本
- 路由: vue-router 3.6.5
- 状态管理: vuex 3.6.2
- UI框架: element-ui 2.15.14
- HTTP客户端: axios 1.7.2
- 国际化: vue-i18n 8.28.2
- 构建工具: vue-cli-service 5.0.0

### 6.3 功能特性
- 提供用户登录认证
- 支持多语言国际化
- 包含多个管理界面（模板、素材、账户、设备中心、资源、操作日志）
- 使用Element UI组件库构建用户界面
- 支持路由懒加载
- 实现导航守卫进行路由权限控制

### 6.4 目录结构
```
├── api/                # API接口定义
├── assets/             # 静态资源
├── components/         # 可复用的Vue组件
├── layouts/            # 页面布局组件
├── router/             # 路由配置
├── store/              # Vuex状态管理
├── utils/              # 工具函数
├── views/              # 页面视图组件
├── App.vue             # 根组件
├── main.js             # 入口文件
├── settings.js         # 配置文件
```

### 6.5 开发与构建
- 开发环境启动: `pnpm serve`
- 生产环境构建: `pnpm build`
- 代码检查: `pnpm lint`



---
## mqtt信息格式事例：


### 指定设备接收指令
```
{
  "type": 1, // 1: 指定设备接收指令，2: 全部接收指令，3: 规则接收指令，4: 设备重启/关机指令，5: OTA升级指令
  "group_name": "scx2", //指定设备组名
  "list": [
    {
      "download_file": "assets/zippack/123.zip", //下载文件地址
      "file_type": "pdf", // 通过判断file_type 选择使用哪种方式打开 file_type格式有 pdf、image、ppt、excel、word
      "equipment_alias_name": "1号",  //指定设备别名（匹配才执行下载文件）
      "hash": ""
    }
  ]
}
```

### 全部接收指令
```
{
  "type": 2,
  "list": [
    {
      "download_file": "assets/zippack/123.zip",
      "file_type": "pdf",
      "equipment_alias_name": "1号",
      "hash": ""
    }
  ]
}
```

### 规则接收指令
```
{
  "type": 3,
  "group_name": "scx2",
  "list": [
    {
      "download_file": "assets/zippack/123.zip",
      "file_type": "pdf",
      "equipment_alias_name": "1号",
      "hash": ""
    }
  ]
}
```

### 下发设备重启/关机指令
```
{
  "type": 4, //设备控制指令
  "group_name": "scx2",  //指定设备组名
  "equipment_alias_name": "1号", //指定设备别名（匹配才执行）
  "command":"reboot" // reboot 重启，shutdown 关机
}
```

### 下发ota升级指令
```
{
  "type": 5, // OTA升级指令
  "group_name": "scx2",  //指定设备组名
  "equipment_alias_name": "1号",  //指定设备别名（匹配才执行）
  "version":"1.0.0",  //匹配版本号
  "ota_url":"http://*************:8080/ota/1.0.0.apk" //ota升级地址
}
```

---
## 服务器授权

### 授权校验规则

系统的授权机制基于对服务器许可证的校验，包含以下两个核心规则：

1.  **机器码（Machine Code）匹配**:
    *   系统会根据当前服务器的硬件信息（如 CPU、主板、网卡MAC地址等）生成一个唯一的机器码。
    *   导入的许可证文件中必须包含与当前服务器完全一致的机器码，否则将被视为无效许可证。
    *   此举确保了许可证是颁发给特定服务器的，防止随意拷贝使用。

2.  **有效期（Expiration）检查**:
    *   许可证文件中包含一个 `expire_at` 字段，用于定义其生命周期。
    *   **永久有效**: 如果 `expire_at` 的值为 `0`，则该许可证被视为永久有效，永不过期。
    *   **限时有效**: 如果 `expire_at` 的值为一个具体的时间戳，系统会将其与当前服务器时间进行比较。只有在当前时间早于该时间戳时，许可证才处于有效状态。

### 授权流程

1.  **导出机器码**: 管理员首先从目标服务器上导出包含其唯一机器码的 `server_machine_code_xxx.json` 文件。
2.  **生成许可证**: 使用导出的机器码文件，联系服务提供商生成对应的服务器许可证 `server_license_xxx.json`。在生成时可以指定许可证的有效期。
3.  **导入许可证**: 管理员将获取到的许可证文件导入到服务器中。系统会立即根据上述【授权校验规则】进行验证。
4.  **状态检查**: 导入成功后，系统会定期或在关键操作前检查许可证的有效性，确保服务持续获得授权。
