env: local
http:
  host: 0.0.0.0
  # host: 127.0.0.1
  port: 8090
security:
  api_sign:
    app_key: 123456
    app_security: 123456
  jwt:
    key: QQYnRFerJTSEcrfB89fw8prOaObmrch8
data:
  db:
    driver: mysql
    dsn: esop:kDn3xZZkwrxnN5we@tcp(test.corp.chaolian360.com:8922)/esop?charset=utf8mb4&parseTime=True&loc=Local
  #    user:
  #      driver: postgres
  #      dsn: host=localhost user=gorm password=gorm dbname=gorm port=9920 sslmode=disable TimeZone=Asia/Shanghai
  redis:
    addr: 127.0.0.1:6350
    password: ""
    db: 0
    read_timeout: 0.2s
    write_timeout: 0.2s

log:
  log_level: debug
  encoding: console # json or console
  log_file_name: "./storage/logs/server.log"
  max_backups: 30
  max_age: 7
  max_size: 1024
  compress: true

mqtt_server:
  tcp_host: 0.0.0.0
  tcp_port: 1884
  authentication:
    enabled: true
    allow_anonymous: false
