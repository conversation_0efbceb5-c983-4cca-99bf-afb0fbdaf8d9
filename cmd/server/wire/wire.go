//go:build wireinject
// +build wireinject

package wire

import (
	"esop/internal/handler"
	"esop/internal/mqtt"
	"esop/internal/repository"
	"esop/internal/server"
	"esop/internal/service"
	"esop/pkg/app"
	"esop/pkg/jwt"
	"esop/pkg/server/http"

	"github.com/google/wire"
	"github.com/spf13/viper"
	"go.uber.org/zap"
)

var ServerSet = wire.NewSet(
	server.NewServerHTTP,
	server.NewTask,
	mqtt.NewMqttServer,
	mqtt.NewMqttServerAdapter,
)

var RepositorySet = wire.NewSet(
	repository.NewDb,
	repository.NewRepository,
	repository.NewTemplateRepository,
	repository.NewEquipmentRepository,
	repository.NewAdminRepository,
	repository.NewSourceMaterialRepository,
	repository.NewResourcePackRepository,
	repository.NewOperationLogRepository,
	repository.NewMaterialGroupRepository,
	repository.NewInternalMqttClient,
	repository.NewLicenseRepository,
)

var ServiceSet = wire.NewSet(
	service.NewService,
	service.NewTemplateService,
	service.NewEquipmentService,
	service.NewAdminService,
	service.NewSourceMaterialService,
	service.NewResourcePackService,
	service.NewOperationLogService,
	service.NewMaterialGroupService,
	service.NewLicenseService,
)

var HandlerSet = wire.NewSet(
	handler.NewHandler,
	handler.NewTemplateHandler,
	handler.NewEquipmentHandler,
	handler.NewAdminHandler,
	handler.NewSourceMaterialHandler,
	handler.NewResourcePackHandler,
	handler.NewOperationLogHandler,
	handler.NewMaterialGroupHandler,
	handler.NewLicenseHandler,
)

func newApp(httpServer *http.Server, task *server.Task, mqttAdapter *mqtt.MqttServerAdapter) *app.App {
	return app.NewApp(
		app.WithServer(httpServer, task, mqttAdapter),
		app.WithName("system-server"),
	)
}
func NewWire(*viper.Viper, *zap.Logger) (*app.App, func(), error) {
	panic(wire.Build(
		ServerSet,
		RepositorySet,
		ServiceSet,
		jwt.NewJwt,
		HandlerSet,
		newApp,
	))
}
