//go:build wireinject
// +build wireinject

package wire

import (
	"esop/internal/mqtt"
	"esop/internal/repository"
	"esop/internal/server"
	"esop/internal/service"
	"esop/pkg/app"
	"esop/pkg/jwt"

	"github.com/google/wire"
	"github.com/spf13/viper"
	"go.uber.org/zap"
)

var RepositorySet = wire.NewSet(
	repository.NewDb,
	repository.NewRepository,
	repository.NewEquipmentRepository,
)

var ServiceSet = wire.NewSet(
	service.NewService,
	service.NewEquipmentService,
)

var serverSet = wire.NewSet(
	server.NewTask,
)

// NewNilMqttClient provides a nil mqtt.InternalClient for the task application,
// as it does not require a direct connection to the MQTT server broker.
func NewNilMqttClient() mqtt.InternalClient {
	return nil
}

// build App
func newApp(
	task *server.Task,
) *app.App {
	return app.NewApp(
		app.WithServer(task),
		app.WithName("esop-task"),
	)
}

func NewWire(*viper.Viper, *zap.Logger) (*app.App, func(), error) {
	panic(wire.Build(
		RepositorySet,
		ServiceSet,
		serverSet,
		jwt.NewJwt,
		NewNilMqttClient,
		newApp,
	))
}
