// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package wire

import (
	"esop/internal/mqtt"
	"esop/internal/repository"
	"esop/internal/server"
	"esop/internal/service"
	"esop/pkg/app"
	"esop/pkg/jwt"
	"github.com/google/wire"
	"github.com/spf13/viper"
	"go.uber.org/zap"
)

// Injectors from wire.go:

func NewWire(viperViper *viper.Viper, logger *zap.Logger) (*app.App, func(), error) {
	jwtJWT := jwt.NewJwt(viperViper)
	internalClient := NewNilMqttClient()
	serviceService := service.NewService(logger, jwtJWT, internalClient)
	db := repository.NewDb(viperViper, logger)
	repositoryRepository := repository.NewRepository(viperViper, logger, db, internalClient)
	equipmentRepository := repository.NewEquipmentRepository(repositoryRepository)
	equipmentService := service.NewEquipmentService(serviceService, equipmentRepository, internalClient)
	task := server.NewTask(logger, equipmentService)
	appApp := newApp(task)
	return appApp, func() {
	}, nil
}

// wire.go:

var RepositorySet = wire.NewSet(repository.NewDb, repository.NewRepository, repository.NewEquipmentRepository)

var ServiceSet = wire.NewSet(service.NewService, service.NewEquipmentService)

var serverSet = wire.NewSet(server.NewTask)

// NewNilMqttClient provides a nil mqtt.InternalClient for the task application,
// as it does not require a direct connection to the MQTT server broker.
func NewNilMqttClient() mqtt.InternalClient {
	return nil
}

// build App
func newApp(
	task *server.Task,
) *app.App {
	return app.NewApp(app.WithServer(task), app.WithName("esop-task"))
}
