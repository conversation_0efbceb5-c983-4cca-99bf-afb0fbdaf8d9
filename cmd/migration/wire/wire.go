//go:build wireinject
// +build wireinject

package wire

import (
	"esop/internal/repository"
	"esop/internal/server"
	"esop/pkg/app"

	"github.com/google/wire"
	"github.com/spf13/viper"
	"go.uber.org/zap"
)

var repositorySet = wire.NewSet(
	repository.NewDb,
	//repository.NewRedis,
	repository.NewRepository,
)
var serverSet = wire.NewSet(
	server.NewMigrate,
)

// build App
func newApp(
	migrate *server.Migrate,
) *app.App {
	return app.NewApp(
		app.WithServer(migrate),
		app.WithName("esop-migrate"),
	)
}

func NewWire(*viper.Viper, *zap.Logger) (*app.App, func(), error) {
	panic(wire.Build(
		repositorySet,
		serverSet,
		newApp,
	))
}
