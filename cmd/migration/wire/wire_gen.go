// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package wire

import (
	"esop/internal/repository"
	"esop/internal/server"
	"esop/pkg/app"
	"github.com/google/wire"
	"github.com/spf13/viper"
	"go.uber.org/zap"
)

// Injectors from wire.go:

func NewWire(viperViper *viper.Viper, logger *zap.Logger) (*app.App, func(), error) {
	db := repository.NewDb(viperViper, logger)
	migrate := server.NewMigrate(db, logger)
	appApp := newApp(migrate)
	return appApp, func() {
	}, nil
}

// wire.go:

var repositorySet = wire.NewSet(repository.NewDb, repository.NewRepository)

var serverSet = wire.NewSet(server.NewMigrate)

// build App
func newApp(
	migrate *server.Migrate,
) *app.App {
	return app.NewApp(app.WithServer(migrate), app.WithName("esop-migrate"))
}
