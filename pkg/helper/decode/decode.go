package decode

import (
	"crypto/aes"
	"crypto/cipher"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
)

// LicenseData 结构对应授权文件中的JSON
type LicenseData struct {
	MachineCode string `json:"machine_code"`
	Reg         string `json:"reg"`
}

// DecryptAES 使用 AES/CBC/PKCS7 解密数据
func DecryptAES(base64Ciphertext string, key []byte, iv []byte) (string, error) {
	ciphertext, err := base64.StdEncoding.DecodeString(base64Ciphertext)
	if err != nil { return "", err }
	block, err := aes.NewCipher(key)
	if err != nil { return "", err }
	if len(ciphertext) < aes.BlockSize { return "", errors.New("ciphertext too short") }
	if len(ciphertext)%aes.BlockSize != 0 { return "", errors.New("ciphertext is not a multiple of the block size") }
	mode := cipher.NewCBCDecrypter(block, iv)
	decrypted := make([]byte, len(ciphertext))
	mode.CryptBlocks(decrypted, ciphertext)
	decrypted, err = pkcs7Unpad(decrypted)
	if err != nil { return "", err }
	return string(decrypted), nil
}

// pkcs7Unpad 移除PKCS7填充
func pkcs7Unpad(data []byte) ([]byte, error) {
	length := len(data)
	if length == 0 {
		return nil, errors.New("pkcs7: data is empty")
	}

	unpadding := int(data[length-1])
	if unpadding > length || unpadding > aes.BlockSize || unpadding == 0 {
		return nil, errors.New("pkcs7: invalid padding size")
	}

	// 检查所有填充字节是否具有相同的值
	pad := data[length-unpadding:]
	for i := 0; i < unpadding; i++ {
		if pad[i] != byte(unpadding) {
			return nil, errors.New("pkcs7: invalid padding byte")
		}
	}

	return data[:(length - unpadding)], nil
}

// VerifyServerLicense 校验服务器授权文件内容
func VerifyServerLicense(licenseFileContent string) ([]string,bool, error) {

	key := []byte("%FsMufong666666%")

	// 1. Base64解码文件内容
	jsonBytes, err := base64.StdEncoding.DecodeString(licenseFileContent)
	if err != nil {
		return []string{},false, fmt.Errorf("无法对文件内容进行Base64解码: %w", err)
	}

	// 2. 解析JSON
	var data LicenseData
	if err := json.Unmarshal(jsonBytes, &data); err != nil {
		return []string{},false, fmt.Errorf("无法解析JSON: %w", err)
	}

	// 3. 解密 reg
	decryptedPayload, err := DecryptAES(data.Reg, key, key)
	if err != nil {
		return []string{},false, fmt.Errorf("注册码 (reg) 解密失败: %w", err)
	}


	// 4. 解析Payload
	parts := strings.Split(decryptedPayload, "#")
	if len(parts) != 2 {
		return []string{},false, errors.New("解密后的Payload格式不正确")
	}
	decryptedMachineCode := parts[0]


	// 5. 校验机器码
	if decryptedMachineCode != data.MachineCode {
		return []string{},false, fmt.Errorf("机器码不匹配: 预期 %s, 得到 %s", data.MachineCode, decryptedMachineCode)
	}

	fmt.Printf("授权有效! 机器码: %s\n", data.MachineCode)
	return parts,true, nil
}
