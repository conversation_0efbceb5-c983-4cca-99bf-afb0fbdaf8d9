package decode

import (
	"crypto/aes"
	"crypto/cipher"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
)

// TerminalDevice 定义了终端设备信息的结构 (重构后)
type TerminalDevice struct {
	AliasName    string `json:"alias_name"`
	<PERSON><PERSON>dd<PERSON>   string `json:"mac_address"`
	SerialNumber string `json:"serial_number"`
	Reg          string `json:"reg"`
}

// TerminalDecryptAES 使用 AES/CBC/PKCS7 解密数据 (公共函数)
func TerminalDecryptAES(base64Ciphertext string, key []byte, iv []byte) (string, error) {
	ciphertext, err := base64.StdEncoding.DecodeString(base64Ciphertext)
	if err != nil { return "", err }
	block, err := aes.NewCipher(key)
	if err != nil { return "", err }
	if len(ciphertext) < aes.BlockSize { return "", errors.New("ciphertext too short") }
	if len(ciphertext)%aes.BlockSize != 0 { return "", errors.New("ciphertext is not a multiple of the block size") }
	mode := cipher.NewCBCDecrypter(block, iv)
	decrypted := make([]byte, len(ciphertext))
	mode.CryptBlocks(decrypted, ciphertext)
	decrypted, err = Terminalpkcs7Unpad(decrypted)
	if err != nil { return "", err }
	return string(decrypted), nil
}

func CheckTerminalDecryptAES(base64Ciphertext string) (string, error) {

	terminalRegKey := []byte("FsMufong95279527")
	ciphertext, err := base64.StdEncoding.DecodeString(base64Ciphertext)

	if err != nil { return "", err }
	block, err := aes.NewCipher(terminalRegKey)

	if err != nil { return "", err }
	if len(ciphertext) < aes.BlockSize { return "", errors.New("ciphertext too short") }
	if len(ciphertext)%aes.BlockSize != 0 { return "", errors.New("ciphertext is not a multiple of the block size") }
	mode := cipher.NewCBCDecrypter(block, terminalRegKey)
	decrypted := make([]byte, len(ciphertext))

	mode.CryptBlocks(decrypted, ciphertext)
	decrypted, err = Terminalpkcs7Unpad(decrypted)
	if err != nil { return "", err }
	return string(decrypted), nil
}

// Terminalpkcs7Unpad 移除PKCS7填充 (公共函数)
func Terminalpkcs7Unpad(data []byte) ([]byte, error) {
	length := len(data)
	if length == 0 { return nil, errors.New("pkcs7: data is empty") }
	unpadding := int(data[length-1])
	if unpadding == 0 || unpadding > length { return nil, errors.New("pkcs7: invalid padding size") }
	pad := data[length-unpadding:]
	for i := 0; i < unpadding; i++ {
		if pad[i] != byte(unpadding) { return nil, errors.New("pkcs7: invalid padding byte") }
	}
	return data[:(length - unpadding)], nil
}

// VerifyAndParseTerminalLicense 校验并解析终端设备授权文件
func VerifyAndParseTerminalLicense(licenseFileContent string) ([]TerminalDevice, error) {


	defaultKey := []byte("%FsMufong666666%")
	terminalRegKey := []byte("FsMufong95279527")

	// 1. 使用默认密钥解密第一层
	jsonPayload, err := TerminalDecryptAES(licenseFileContent, defaultKey, defaultKey) // IV is same as Key
	if err != nil {
		return nil, fmt.Errorf("无法解密授权文件 (第一层): %w", err)
	}

	// 2. 解析设备列表JSON
	var devices []TerminalDevice
	if err := json.Unmarshal([]byte(jsonPayload), &devices); err != nil {
		return nil, fmt.Errorf("无法解析设备列表JSON: %w", err)
	}

	// 3. (可选但推荐) 遍历并校验每个设备的 'reg'
	for _, device := range devices {
		fmt.Println(device.Reg,"device.Regdevice.Regdevice.Regdevice.Reg")
		// a. 准备期望的 payload
		expectedPayload := fmt.Sprintf("%s#%s", device.MacAddress, device.SerialNumber)

		// b. 使用终端专用密钥解密 'reg'
		decryptedReg, err := TerminalDecryptAES(device.Reg, terminalRegKey, terminalRegKey) // IV is same as Key
		if err != nil {
			return nil, fmt.Errorf("导入注册码失败")
		}

		// c. 验证内容
		if decryptedReg != expectedPayload {
			return nil, fmt.Errorf("导入注册码失败")
		}
	}

	return devices, nil
}