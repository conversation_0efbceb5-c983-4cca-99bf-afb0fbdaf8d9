package generatekey

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"fmt"
)

func GetGenerateKey(macAddress string) string {
	secretKey := "aaa-bbb-ccc"
	mac := hmac.New(sha256.New, []byte(secretKey))
	mac.Write([]byte(macAddress))
	hash := mac.Sum(nil)
	base64Key := base64.StdEncoding.EncodeToString(hash)

	// 格式化为 xxxx-xxxx-xxxx-xxxx
	formattedKey := fmt.Sprintf("%s-%s-%s-%s",
		base64Key[:4], base64Key[4:8], base64Key[8:12], base64Key[12:16])
	return formattedKey
}
