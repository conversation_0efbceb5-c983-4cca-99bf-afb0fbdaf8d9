import axios from 'axios'
// import FileSaver from 'file-saver'
import { getToken } from '@/utils/auth'
import { Message } from 'element-ui';
// import moment from 'moment-timezone';


//  全局修改弹出提示显示位置
//定义一个新的Message方法，多传入一个offset参数
export const $message = options => {
  return Message({
    ...options,
    offset: 80
  });
};
//重写方法,将offset写入options
['success', 'warning', 'info', 'error'].forEach(type => {
  $message[type] = options => {
    if (typeof options === 'string') {
      options = {
        message: options,
        offset: 80
      };
    }
    options.type = type;
    return Message(options);
  };
});


/**
 * 删除对象中的空值
 * @param {object} obj
 * @returns {Object}
 */
export function deleteEmptyObj(obj) {
  let newObj = obj;
  for (var key in newObj) {
    if (newObj[key] === '' || newObj[key] === null || newObj[key] === undefined) {
      delete newObj[key]
    }
  }
  return newObj;
}

/**
 * 
 * @param {*} array 要查询的数组
 * @param {*} attr 要查询的字段
 * @param {*} val 要查询的字段值
 * @returns 
 */
export function findElem(array, attr, val) {
  if (!array || !array.length) {
    return -1
  }
  for (var i = 0; i < array.length; i++) {
    if (array[i][attr] == val) {
      return i; //返回当前索引值
    }
  }
  return -1;
}


/**
 * 生成带参数的链接
 * @param {String} url
 * @param {Object} params
 * @returns
 */
export function createParamsUrl(url, params) {
  if (typeof url === 'undefined' || url == null || url == '') {
    return ''
  }
  if (typeof params === 'undefined' || params == null || typeof params !== 'object') {
    return ''
  }
  url += url.indexOf('?') != -1 ? '' : '?'
  for (var k in params) {
    url += (url.indexOf('=') != -1 ? '&' : '') + k + '=' + encodeURI(params[k])
  }
  return url
}

/**
 * 导出功能
 * @param {*} obj
 * @returns
 */
export function downloadFiles(url, params = {}) {
  params['x-token'] = getToken()
  const newParams = deleteEmptyObj(params)
  const newUrl = createParamsUrl(url, newParams)
  console.log(process.env.VUE_APP_BASE_API + newUrl)
  window.open(process.env.VUE_APP_BASE_API + newUrl)
}

export function downloadFiles2(url, params = {}, name) {
  const newParams = deleteEmptyObj(params)
  const newUrl = createParamsUrl(url, newParams)

  axios({
    url: process.env.VUE_APP_BASE_API + newUrl,
    method: 'get',
    responseType: 'blob',
    headers: {
      'x-token': getToken()
    }
  }).then(res => {
    var blob = new Blob([res.data], {
      type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document;charset=utf-8'
    })
    var filename = name + '.xlsx'
    var downloadElement = document.createElement('a')
    var href = window.URL.createObjectURL(blob) // 创建下载的链接
    downloadElement.style.display = 'none'
    downloadElement.href = href
    downloadElement.download = filename // 下载后文件名
    document.body.appendChild(downloadElement)
    downloadElement.click() // 点击下载
    document.body.removeChild(downloadElement) // 下载完成移除元素
    window.URL.revokeObjectURL(href) // 释放掉blob对象
  })

  // params['x-token'] = getToken()
  // const newParams = deleteEmptyObj(params)
  // const newUrl = createParamsUrl(url, newParams)
  // console.log(process.env.VUE_APP_BASE_API + newUrl)
  // window.open(process.env.VUE_APP_BASE_API + newUrl)
}

/**
 * 导入功能
 * @param {*} url
 * @param {*} formData
 */
export async function handleImport(url, formData) {
  const Result = await new Promise((resolve, reject) => {
    axios
      .post(
        process.env.VUE_APP_BASE_API + url,
        formData,

        {
          headers: {
            'Content-Type': 'multipart/form-data',
            'x-token': getToken()
          }
        }
      )
      .then(res => {
        resolve(res.data)
      })
      .catch(err => {
        reject(err)
      })
  })
  return Result
}
/**
 * 导入有返回二进制文件
 * @param {*} url
 * @param {*} formData
 */
export async function handleImportTwo(url, formData) {
  const Result = await new Promise((resolve, reject) => {
    axios
      .post(
        process.env.VUE_APP_BASE_API + url,
        formData,

        {
          responseType: 'arraybuffer',
          headers: {
            'Content-Type': 'multipart/form-data',
            'x-token': getToken()
          }
        }
      )
      .then(res => {
        resolve(res.data)
      })
      .catch(err => {
        reject(err)
      })
  })
  return Result
}

/**
 * 重置时间格式
 * @param {*} date
 * @param {*} type
 */
export function resetDateFormat(date, type) {
  let newDate = ''
  if (!date) {
    return ''
  }
  // type=1: yyyy-MM-dd 转 yyyy-MM-dd HH:mm:ss
  if (type == 1 || !type) {
    newDate = date + ' 00:00:00'
  }
}

/**
 * 若文档中已有命名dateFormat，可用dFormat()调用
 * 年(Y) 可用1-4个占位符
 * 月(m)、日(d)、小时(H)、分(M)、秒(S) 可用1-2个占位符
 * 星期(W) 可用1-3个占位符
 * 季度(q为阿拉伯数字，Q为中文数字)可用1或4个占位符
 *
 * let date = new Date()
 * dateFormat("YYYY-mm-dd HH:MM:SS", date)           2020-02-09 14:04:23
 * dateFormat("YYYY-mm-dd HH:MM:SS Q", date)         2020-02-09 14:09:03 一
 * dateFormat("YYYY-mm-dd HH:MM:SS WWW", date)       2020-02-09 14:45:12 星期日
 * dateFormat("YYYY-mm-dd HH:MM:SS QQQQ", date)      2020-02-09 14:09:36 第一季度
 * dateFormat("YYYY-mm-dd HH:MM:SS WWW QQQQ", date)  2020-02-09 14:46:12 星期日 第一季度
 */
export function dateFormat(format, date) {
  const we = date.getDay() // 星期
  const qut = Math.floor((date.getMonth() + 3) / 3).toString() // 季度
  const opt = {
    'Y+': date.getFullYear().toString(), // 年
    'm+': (date.getMonth() + 1).toString(), // 月(月份从0开始，要+1)
    'd+': date.getDate().toString(), // 日
    'H+': date.getHours().toString(), // 时
    'M+': date.getMinutes().toString(), // 分
    'S+': date.getSeconds().toString(), // 秒
    'q+': qut // 季度
  }
  const week = {
    // 中文数字 (星期)
    0: '日',
    1: '一',
    2: '二',
    3: '三',
    4: '四',
    5: '五',
    6: '六'
  }
  const quarter = {
    // 中文数字（季度）
    1: '一',
    2: '二',
    3: '三',
    4: '四'
  }
  if (/(W+)/.test(format)) {
    format = format.replace(
      RegExp.$1,
      RegExp.$1.length > 1 ? (RegExp.$1.length > 2 ? '星期' + week[we] : '周' + week[we]) : week[we]
    )
  }
  if (/(Q+)/.test(format)) {
    // 输入一个Q，只输出一个中文数字，输入4个Q，则拼接上字符串
    format = format.replace(RegExp.$1, RegExp.$1.length == 4 ? '第' + quarter[qut] + '季度' : quarter[qut])
  }
  for (const k in opt) {
    const r = new RegExp('(' + k + ')').exec(format)
    if (r) {
      // 若输入的长度不为1，则前面补零
      format = format.replace(r[1], RegExp.$1.length == 1 ? opt[k] : opt[k].padStart(RegExp.$1.length, '0'))
    }
  }
  return format
}

/**
 * @param url {string} pdf地址
 * @param fileName {string} pdf名称
 */
// export function downloadPdf(url, fileName) {
//   axios({
//     method: 'get',
//     url,
//     responseType: 'blob'
//   }).then(res => {
//     const file = new Blob([res.data], {
//       type: 'application/pdf'
//     })
//     FileSaver(file, fileName)
//   })
// }

export function changeNumberMoneyToChinese(money) {
  // 接收数字或者字符串数字
  if (typeof money === 'string') {
    if (money === '') return ''
    if (isNaN(parseFloat(money))) {
      throw Error(`参数有误：${money}，请输入数字或字符串数字`)
    } else {
      // 去掉分隔符(,)
      money = money.replace(/,/g, '')
    }
  } else if (typeof money === 'number') {
    // 去掉分隔符(,)
    money = money.toString().replace(/,/g, '')
  } else {
    throw Error(`参数有误：${money}，请输入数字或字符串数字`)
  }
  // 汉字的数字
  const cnNums = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖']
  // 基本单位
  const cnIntRadice = ['', '拾', '佰', '仟']
  // 对应整数部分扩展单位
  const cnIntUnits = ['', '万', '亿', '兆']
  // 对应小数部分单位
  const cnDecUnits = ['角', '分', '毫', '厘']
  // 整数金额时后面跟的字符
  const cnInteger = '整'
  // 整型完以后的单位
  const cnIntLast = '元'
  // 金额整数部分
  let IntegerNum
  // 金额小数部分
  let DecimalNum
  // 输出的中文金额字符串
  let ChineseStr = ''
  // 正负值标记
  let Symbol = ''
  // 转成浮点数
  money = parseFloat(money)
  // 如果是0直接返回结果
  if (money === 0) {
    ChineseStr = cnNums[0] + cnIntLast + cnInteger
    return ChineseStr
  }
  // 如果小于0，则将Symbol标记为负，并转为正数
  if (money < 0) {
    money = -money
    Symbol = '负 '
  }
  // 转换为字符串
  money = money.toString()
  // 将整数部分和小数部分分别存入IntegerNum和DecimalNum
  if (money.indexOf('.') === -1) {
    IntegerNum = money
    DecimalNum = ''
  } else {
    const moneyArr = money.split('.')
    IntegerNum = moneyArr[0]
    DecimalNum = moneyArr[1].substr(0, 4)
  }
  // 获取整型部分转换
  if (parseInt(IntegerNum, 10) > 0) {
    let zeroCount = 0
    let IntLen = IntegerNum.length
    for (let i = 0; i < IntLen; i++) {
      // 获取整数的每一项
      let term = IntegerNum.substr(i, 1)
      // 剩余待处理的数量
      let surplus = IntLen - i - 1
      // 用于获取整数部分的扩展单位
      // 剩余数量除以4，比如12345，term为1时，expandUnit则为1，
      // cnIntUnits[expandUnit]对应得到的单位为万
      let expandUnit = surplus / 4
      // 用于获取整数部分的基本单位
      // 剩余数量取余4，比如123，那么第一遍遍历term为1，surplus为2，baseUnit则为2，
      // 所以cnIntRadice[baseUnit]对应得到的基本单位为'佰'
      let baseUnit = surplus % 4
      if (term === '0') {
        zeroCount++
      } else {
        // 连续存在多个0的时候需要补'零'
        if (zeroCount > 0) {
          ChineseStr += cnNums[0]
        }
        // 归零
        zeroCount = 0
        /*
   cnNums是汉字的零到玖组成的数组，term则是阿拉伯0-9，
   直接将阿拉伯数字作为下标获取中文数字
   例如term是0则cnNums[parseInt(term)]取的就是'零'，9取的就是'玖'
   最后加上单位就转换成功了！
   这里只加十百千的单位
   */
        ChineseStr += cnNums[parseInt(term)] + cnIntRadice[baseUnit]
      }
      /*
   如果baseUnit为0，意味着当前项和下一项隔了一个节权位即隔了一个逗号
   扩展单位只有大单位进阶才需要，判断是否大单位进阶，则通过zeroCount判断
   baseUnit === 0即存在逗号，baseUnit === 0 && zeroCount < 4 意为大单位进阶
 */
      if (baseUnit === 0 && zeroCount < 4) {
        ChineseStr += cnIntUnits[expandUnit]
      }
    }
    ChineseStr += cnIntLast
  }
  // 小数部分转换
  if (DecimalNum !== '') {
    let decLen = DecimalNum.length
    for (let i = 0; i < decLen; i++) {
      // 同理，参考整数部分
      let term = DecimalNum.substr(i, 1)
      if (term !== '0') {
        ChineseStr += cnNums[Number(term)] + cnDecUnits[i]
      }
    }
  }
  ChineseStr = Symbol + ChineseStr
  return ChineseStr
}

// 区分单击事件和双击事件
export function clickBG(millisecond) {
  this.timer = null
  this.click = callback => {
    clearTimeout(this.timer)
    this.timer = setTimeout(function () {
      clearTimeout(this.timer)
      callback && callback()
    }, millisecond)
  }
  this.dblClick = callback => {
    clearTimeout(this.timer)
    callback && callback()
  }
}

// 获取地址栏参数
export function getUrlParam() {
  let getqyinfo = window.location.href.split('?')[1] || ''
  let getqys = getqyinfo.split('&')
  let obj = {} //创建空对象，接收截取的参数
  for (let i = 0; i < getqys.length; i++) {
    let item = getqys[i].split('=')
    let key = item[0]
    let value = item[1]
    obj[key] = value
  }
  return obj
}

export function arrQC(list, qz = { label: '', value: '' }) {
  var obj = {}
  for (var item of list) {
    obj[item[qz?.value || 'value']] = item
  }
  return Object.keys(obj).map(key => {
    return {
      data: obj[key],
      value: key,
      label: obj[key][qz?.label || 'label']
    }
  })
}

// export function listToTree(list, parentIdAttribute) {
//   const map = {};
//   const roots = [];

//   list.forEach(item => {
//       map[item.id] = { ...item, children: [] };
//   });

//   Object.values(map).forEach(item => {
//       const parentId = item[parentIdAttribute];
//       if (parentId !== null && map[parentId]) {
//           map[parentId].children.push(item);
//       } else {
//           roots.push(item);
//       }
//   });

//   return roots;
// }

function setShowElement(name, row) {
  if (typeof name == 'function') {
    return name(row || {})
  } else {
    return !!name
  }
}

export function listToTree(list, parentIdAttribute, conditions = true) {
  const map = {};
  const roots = [];

  list.forEach(item => {
      map[item.id] = { ...item, children: [] };
  });

  Object.values(map).forEach(item => {
      const parentId = item[parentIdAttribute];
      if (parentId !== null && map[parentId]) {
          map[parentId].children.push(item);
      } else if(setShowElement(conditions, item)){
          roots.push(item);
      }
  });

  // 递归函数，对每个节点的 children 属性进行排序
  function sortChildren(node) {
      if (node.children.length > 0) {
          node.children.sort((a, b) => Number(a.sort) - Number(b.sort)); // 按 sort 字段排序
          node.children.forEach(child => sortChildren(child)); // 递归调用，对每个子节点进行排序
      }
  }

  // 对根节点进行排序
  roots.sort((a, b) => Number(a.sort) - Number(b.sort));

  // 对每个根节点的子节点进行排序
  roots.forEach(root => sortChildren(root));

  return roots;
}

export function formatTimeStamp(timeStamp) {
  const time = timeStamp.toString().length<13?timeStamp*1000:timeStamp
  let date = new Date(time);
  let year = date.getFullYear();
  let month = date.getMonth() + 1;
  let day = date.getDate();
  let hour = date.getHours();
  let minute = date.getMinutes();
  let second = date.getSeconds();

  month = month < 10 ? "0" + month : month;
  day = day < 10 ? "0" + day : day;
  hour = hour < 10 ? "0" + hour : hour;
  minute = minute < 10 ? "0" + minute : minute;
  second = second < 10 ? "0" + second : second;

  return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
}
