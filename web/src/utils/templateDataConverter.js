/**
 * 模板数据转换工具
 * 用于处理 API 返回的数据格式转换
 */

/**
 * 将 API 返回的模板数据转换为前端格式
 * @param {Object} apiData - API 返回的原始数据
 * @returns {Object} 转换后的前端数据
 */
export function convertTemplateData (apiData) {
    if (!apiData || !apiData.template_sm) {
        console.warn('convertTemplateData: 无效的 API 数据');
        return apiData;
    }

    const convertedData = { ...apiData };
    convertedData.template_sm = apiData.template_sm.map(item => {
        try {
            const convertedItem = { ...item };

            // 处理字段名映射：multi_files -> multiFiles
            if (convertedItem.multi_files !== undefined) {
                convertedItem.multiFiles = convertedItem.multi_files;
                delete convertedItem.multi_files;
            }

            // 处理 multiFiles 数据
            if (convertedItem.multiFiles && Array.isArray(convertedItem.multiFiles)) {
                convertedItem.multiFiles = convertedItem.multiFiles.map(file => ({
                    clientKey: file.clientKey || generateClientKey(),
                    type: file.type || 1,
                    template_sm_type: file.template_sm_type || 1,
                    path: file.path || '',
                    sm_id: file.sm_id || 0,
                    sm_name: file.sm_name || '未命名文件',
                    source_width: file.source_width || 0,
                    source_height: file.source_height || 0,
                    interval_time: file.interval_time || 5
                }));
            } else {
                convertedItem.multiFiles = [];
            }

            return convertedItem;
        } catch (error) {
            console.error('转换模板素材数据失败:', error, item);
            return item; // 返回原始数据
        }
    });

    return convertedData;
}

/**
 * 验证模板数据的完整性
 * @param {Object} data - 模板数据
 * @returns {Object} 验证结果 {isValid: boolean, errors: string[]}
 */
export function validateTemplateData (data) {
    const errors = [];

    if (!data) {
        errors.push('数据为空');
        return { isValid: false, errors };
    }

    if (!data.template_sm || !Array.isArray(data.template_sm)) {
        errors.push('缺少 template_sm 字段或不是数组');
        return { isValid: false, errors };
    }

    data.template_sm.forEach((item, index) => {
        if (!item.sm_name) {
            errors.push(`第 ${index + 1} 个素材缺少名称`);
        }
        if (!item.path && (!item.multiFiles || item.multiFiles.length === 0)) {
            errors.push(`第 ${index + 1} 个素材缺少路径信息且没有多文件`);
        }
        if (item.multiFiles && Array.isArray(item.multiFiles)) {
            item.multiFiles.forEach((file, fileIndex) => {
                if (!file.path) {
                    errors.push(`第 ${index + 1} 个素材的第 ${fileIndex + 1} 个文件缺少路径`);
                }
            });
        }
    });

    return {
        isValid: errors.length === 0,
        errors
    };
}

/**
 * 生成客户端唯一键
 * @returns {number} 唯一键
 */
function generateClientKey () {
    return Date.now() + Math.random();
}

/**
 * 调试函数：打印模板数据结构
 * @param {Object} data - 模板数据
 */
export function debugTemplateData (data) {
    console.group('模板数据调试信息');
    console.log('原始数据:', data);

    if (data && data.template_sm) {
        console.log('素材数量:', data.template_sm.length);
        data.template_sm.forEach((item, index) => {
            console.log(`素材 ${index + 1}:`, {
                name: item.sm_name,
                path: item.path,
                type: item.type,
                hasMultiFiles: item.multiFiles && item.multiFiles.length > 0,
                multiFilesCount: item.multiFiles ? item.multiFiles.length : 0
            });
        });
    }

    console.groupEnd();
}

export default {
    convertTemplateData,
    validateTemplateData,
    debugTemplateData
};