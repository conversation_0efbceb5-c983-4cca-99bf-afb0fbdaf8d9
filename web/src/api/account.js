import request, { postBlob, getBlob, handleImport } from '@/utils/request'

export function getList(params) {
  return request({
    url: `/admin/admin/getList`,
    method: 'get',
    params
  })
}

export function add(data) {
  return request({
    url: `/admin/admin/addAdmin`,
    method: 'post',
    data
  })
}

export function edit(data,id) {
  return request({
    url: `/admin/admin/editAdmin/`+id,
    method: 'put',
    data
  })
}

export function del(id) {
  return request({
    url: `/admin/admin/delete/` + id,
    method: 'delete'
  })
}