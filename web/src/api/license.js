import request, { getBlob, handleImport } from '@/utils/request'

// 服务器端：导出机器码、导入注册码
export function exportServerMachineCode () {
  return getBlob({
    url: `/admin/license/server/machine_code/export`,
    name: 'server_machine_code.json',
    params: {}
  })
}

export function importServerLicense (formData) {
  // formData: FormData with field `file`
  return handleImport(`/admin/license/server/license/import`, formData)
}

// 可选：获取服务器授权状态
export function getServerLicenseStatus () {
  return request({
    url: `/admin/license/server/status`,
    method: 'get'
  })
}

// 终端设备：导出终端机器码、导出未注册终端机器码、导入终端注册码、终端注册情况列表
export function exportAllTerminalMachineCodes () {
  return getBlob({
    url: `/admin/license/terminal/machine_codes/export`,
    name: 'terminal_machine_codes_all',
    params: {}
  })
}

export function exportUnregisteredTerminalMachineCodes () {
  return getBlob({
    url: `/admin/license/terminal/machine_codes/export?only_unregistered=1`,
    name: 'terminal_machine_codes_unregistered',
    params: {}
  })
}

export function importTerminalRegistrationCodes (formData) {
  // formData: FormData with field `file`
  return handleImport(`/admin/license/terminal/registration_codes/import`, formData)
}

export function getTerminalRegistrationStatus (params) {
  // params: { page, page_size, keyword? }
  return request({
    url: `/admin/license/terminal/status`,
    method: 'get',
    params
  })
}

