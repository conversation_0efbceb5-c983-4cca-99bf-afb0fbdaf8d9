import request, { postBlob, getBlob, handleImport } from '@/utils/request'

export function getList (params) {
  return request({
    url: `/admin/sourcematerial/getList`,
    method: 'get',
    params
  })
}

export function add (data) {
  return request({
    url: `/admin/sourcematerial/addSourceMaterial`,
    method: 'post',
    data
  })
}

export function edit (data, id) {
  return request({
    url: `/admin/sourcematerial/editSourceMaterial/` + id,
    method: 'put',
    data
  })
}

export function del (id) {
  return batchDelete({ ids: [id] })
}

// 新的批量上传素材接口
export function batchUploadMaterials (formData, config = {}) {
  return request({
    url: `/admin/sourcematerial/batchUploadNew`,
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    ...config
  })
}

// 分组管理相关API
export function getMaterialGroupList (params) {
  return request({
    url: '/admin/materialgroup/getList',
    method: 'get',
    params
  })
}

export function getAllMaterialGroups () {
  return request({
    url: '/admin/materialgroup/getAllGroups',
    method: 'get'
  })
}

export function getMaterialGroupDetail (id) {
  return request({
    url: '/admin/materialgroup/getDetail',
    method: 'get',
    params: { id }
  })
}

export function createMaterialGroup (data) {
  return request({
    url: '/admin/materialgroup/create',
    method: 'post',
    data
  })
}

export function editMaterialGroup (id, data) {
  return request({
    url: `/admin/materialgroup/edit/${id}`,
    method: 'put',
    data
  })
}

export function deleteMaterialGroup (id) {
  return request({
    url: `/admin/materialgroup/delete/${id}`,
    method: 'delete'
  })
}

export function moveMaterialsToGroup (data) {
  return request({
    url: '/admin/materialgroup/moveMaterials',
    method: 'post',
    data
  })
}

// 带分组的批量上传
export function batchUploadMaterialsWithGroup (formData, config = {}) {
  return request({
    url: '/admin/sourcematerial/batchUploadNewWithGroup',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    ...config
  })
}

// 批量更新素材分组
export function updateMaterialsGroup (data) {
  return request({
    url: '/admin/sourcematerial/updateMaterialsGroup',
    method: 'put',
    data
  })
}
// 批量删除素材
export function batchDelete (data) {
  return request({
    url: '/admin/sourcematerial/delete',
    method: 'delete',
    data
  })
}