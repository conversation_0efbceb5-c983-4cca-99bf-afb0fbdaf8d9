import request, { postBlob, getBlob, handleImport } from '@/utils/request'

export function getList () {
  return request({
    url: '/record/list',
    method: 'get'
  })
}

export function getCompanyList () {
  return request({
    url: '/record/company/list',
    method: 'get'
  })
}
export function recordAmityList2 (data) {
  return request({
    url: `/record/amity/list2`,
    method: 'post',
    data
  })
}

// 登录
export function login (params) {
  return request({
    url: '/v1/login',
    method: 'post',
    params
  })
}

// 获取用户信息
export function getInfo (userid) {
  return request({
    url: '/admin/user/detail',
    method: 'get',
    params: { adminUserId: userid }
  })
}

// 动态路由
export function getRouter (params) {
  return request({
    url: `/admin/user/module/list`,
    method: 'get',
    params
  })
}

// 退出登录
export function logout () {
  return request({
    url: '/admin/user/logout',
    method: 'post'
  })
}

// 动态路由
export function getCode () {
  return request({
    url: '/admin/user/imageVerification',
    method: 'get',
  })
}