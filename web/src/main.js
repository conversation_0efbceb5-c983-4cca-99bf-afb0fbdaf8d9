import Vue from 'vue'
import App from './App'
import router from './router'
import store from './store'
import i18n from '../i18n'
import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'

import { formatTimeStamp } from '@/utils/util.js'

Vue.prototype.$formatTimeStamp = formatTimeStamp

Vue.use(ElementUI)
Vue.prototype.$confirm = ElementUI.MessageBox.confirm

Vue.config.productionTip = false

new Vue({
  el: '#app',
  router,
  store,
  i18n,
  render: h => h(App)
})
