<template>
    <div class="resizable-splitter" :class="{ 'is-dragging': isDragging }" @mousedown="startDrag">
        <div class="splitter-handle">
            <div class="splitter-line"></div>
            <div class="splitter-grip">
                <div class="grip-dot"></div>
                <div class="grip-dot"></div>
                <div class="grip-dot"></div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'ResizableSplitter',
    props: {
        minWidth: {
            type: Number,
            default: 200
        },
        maxWidth: {
            type: Number,
            default: 400
        },
        initialWidth: {
            type: Number,
            default: 240
        },
        direction: {
            type: String,
            default: 'right', // 'right' for left panel, 'left' for right panel
            validator: value => ['left', 'right'].includes(value)
        }
    },
    data () {
        return {
            isDragging: false,
            startX: 0,
            startWidth: 0
        }
    },
    mounted () {
        document.addEventListener('mousemove', this.onDrag)
        document.addEventListener('mouseup', this.endDrag)
    },
    beforeDestroy () {
        document.removeEventListener('mousemove', this.onDrag)
        document.removeEventListener('mouseup', this.endDrag)
    },
    methods: {
        startDrag (e) {
            this.isDragging = true
            this.startX = e.clientX
            this.startWidth = this.initialWidth

            // 添加拖拽时的样式
            document.body.style.cursor = 'col-resize'
            document.body.style.userSelect = 'none'

            this.$emit('drag-start', this.startWidth)
        },

        onDrag (e) {
            if (!this.isDragging) return

            const deltaX = e.clientX - this.startX
            let newWidth

            // 根据方向调整宽度计算
            if (this.direction === 'right') {
                console.log(deltaX, "deltaXdeltaXdeltaXdeltaX");
                // 左侧面板：向右拖拽增加宽度
                newWidth = this.startWidth + deltaX
            } else {


                // 右侧面板：向右拖拽减少宽度
                newWidth = this.startWidth - deltaX
            }

            // 限制宽度范围
            newWidth = Math.max(this.minWidth, Math.min(this.maxWidth, newWidth))

            this.$emit('dragging', newWidth)
        },

        endDrag () {
            if (!this.isDragging) return

            this.isDragging = false

            // 恢复样式
            document.body.style.cursor = ''
            document.body.style.userSelect = ''

            this.$emit('drag-end', this.startWidth)
        }
    }
}
</script>

<style scoped lang="scss">
.resizable-splitter {
    width: 8px;
    height: 100%;
    background: transparent;
    position: relative;
    cursor: col-resize;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s ease;

    &:hover {
        background: rgba(64, 158, 255, 0.1);
    }

    &.is-dragging {
        background: rgba(64, 158, 255, 0.2);

        .splitter-handle {
            background: #409eff;
        }
    }
}

.splitter-handle {
    width: 4px;
    height: 60px;
    background: #e0e0e0;
    border-radius: 2px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s ease;
}

.splitter-line {
    width: 2px;
    height: 40px;
    background: #409eff;
    border-radius: 1px;
    opacity: 0.6;
}

.splitter-grip {
    display: flex;
    flex-direction: column;
    gap: 2px;
    margin-top: 4px;
}

.grip-dot {
    width: 2px;
    height: 2px;
    background: #409eff;
    border-radius: 50%;
    opacity: 0.8;
}
</style>