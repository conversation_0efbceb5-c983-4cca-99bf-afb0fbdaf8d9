<template>
    <el-dialog :title="$t('equipmentCenter.uploadDialog.title', { device: equipment.alias_name || equipment.label })"
        :visible.sync="visible" width="800px" :close-on-click-modal="false" :close-on-press-escape="false"
        @close="handleClose">
        <div class="upload-container">

            <!-- 拖拽上传区域 -->
            <div class="drag-upload-area" :class="{ 'drag-over': isDragOver }" @drop="handleDrop"
                @dragover="handleDragOver" @dragleave="handleDragLeave" @click="selectFiles">
                <i class="el-icon-upload"></i>
                <div class="drag-text">
                    <p v-html="$t('equipmentCenter.uploadDialog.dragText')"></p>
                    <p class="tip">{{ $t('equipmentCenter.uploadDialog.supportedFormats') }}</p>
                </div>
            </div>

            <!-- 隐藏的文件输入框 -->
            <input ref="fileInput" type="file" multiple
                accept="image/*,video/*,.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt" style="display: none"
                @change="handleFileSelect" />

            <!-- 文件列表 -->
            <div class="file-list" v-if="fileList.length > 0">
                <div class="list-header">
                    <span>{{ $t('equipmentCenter.uploadDialog.fileList', { count: fileList.length }) }}</span>
                    <el-button type="text" @click="clearAll" size="small">{{ $t('equipmentCenter.uploadDialog.clearAll')
                    }}</el-button>
                </div>

                <div class="file-items">
                    <div v-for="(file, index) in fileList" :key="index" class="file-item"
                        :class="{ 'uploading': file.uploading, 'success': file.success, 'error': file.error }">
                        <!-- 文件预览 -->
                        <div class="file-preview">
                            <img v-if="file.isImage && file.preview" :src="file.preview"
                                :alt="$t('equipmentCenter.uploadDialog.preview')" />
                            <video v-else-if="file.isVideo && file.preview" :src="file.preview" muted></video>
                            <i v-else :class="getFileIcon(file.type)" class="file-icon"></i>
                        </div>

                        <!-- 文件信息 -->
                        <div class="file-info">
                            <div class="file-name" :title="file.name">{{ file.name }}</div>
                            <div class="file-details">
                                <span class="file-size">{{ formatFileSize(file.size) }}</span>
                                <span v-if="file.dimensions" class="file-dimensions">
                                    {{ file.dimensions.width }} × {{ file.dimensions.height }}
                                </span>
                                <span class="file-type">{{ getFileTypeText(file.detectedType) }}</span>
                            </div>
                            <div class="auto-detected" v-if="file.autoName">
                                <span class="label">{{ $t('equipmentCenter.uploadDialog.autoDetectedName') }}</span>
                                <span class="value">{{ file.autoName }}</span>
                            </div>
                        </div>

                        <!-- 上传状态 -->
                        <div class="file-status">
                            <el-progress v-if="file.uploading" :percentage="file.progress" :stroke-width="4"
                                status="success"></el-progress>
                            <i v-else-if="file.success" class="el-icon-circle-check success-icon"></i>
                            <i v-else-if="file.error" class="el-icon-circle-close error-icon"></i>
                            <el-button v-else type="text" @click="removeFile(index)" class="remove-btn">
                                <i class="el-icon-delete"></i>
                            </el-button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 上传统计 -->
            <div class="upload-stats" v-if="fileList.length > 0">
                <div class="stats-item">
                    <span class="label">{{ $t('equipmentCenter.uploadDialog.totalFiles') }}</span>
                    <span class="value">{{ fileList.length }}</span>
                </div>
                <div class="stats-item">
                    <span class="label">{{ $t('equipmentCenter.uploadDialog.totalSize') }}</span>
                    <span class="value">{{ formatFileSize(totalSize) }}</span>
                </div>
                <div class="stats-item" v-if="uploadedCount > 0">
                    <span class="label">{{ $t('equipmentCenter.uploadDialog.uploaded') }}</span>
                    <span class="value">{{ uploadedCount }}/{{ fileList.length }}</span>
                </div>
            </div>
        </div>

        <div slot="footer" class="dialog-footer">
            <el-button @click="handleClose" :disabled="isUploading">{{ $t('equipmentCenter.uploadDialog.cancel')
                }}</el-button>
            <el-button type="primary" @click="startUpload" :loading="isUploading" :disabled="fileList.length === 0">
                {{ isUploading ? $t('equipmentCenter.uploadDialog.uploading') :
                    $t('equipmentCenter.uploadDialog.startUpload') }}
            </el-button>
        </div>

    </el-dialog>
</template>

<script>
import { uploadEquipmentMaterial } from '@/api/equipmentCenter'

export default {
    name: 'EquipmentMaterialUploadDialog',
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        equipment: {
            type: Object,
            required: true
        }
    },
    data () {
        return {
            isDragOver: false,
            fileList: [],
            isUploading: false,
            uploadedCount: 0
        }
    },
    computed: {
        totalSize () {
            return this.fileList.reduce((total, file) => total + file.size, 0)
        }
    },
    methods: {
        // 处理拖拽事件
        handleDragOver (e) {
            e.preventDefault()
            this.isDragOver = true
        },

        handleDragLeave (e) {
            e.preventDefault()
            this.isDragOver = false
        },

        handleDrop (e) {
            e.preventDefault()
            this.isDragOver = false
            const files = Array.from(e.dataTransfer.files)
            this.processFiles(files)
        },

        // 选择文件
        selectFiles () {
            this.$refs.fileInput.click()
        },

        handleFileSelect (e) {
            const files = Array.from(e.target.files)
            this.processFiles(files)
            // 清空input值，允许重复选择同一文件
            e.target.value = ''
        },

        // 处理文件
        async processFiles (files) {
            const validFiles = files.filter(file => this.isValidFile(file))

            for (const file of validFiles) {
                // 检查是否已存在
                if (this.fileList.some(f => f.name === file.name && f.size === file.size)) {
                    continue
                }

                const fileItem = {
                    file,
                    name: file.name,
                    size: file.size,
                    type: file.type,
                    isImage: file.type.startsWith('image/'),
                    isVideo: file.type.startsWith('video/'),
                    preview: null,
                    dimensions: null,
                    detectedType: this.detectFileType(file),
                    autoName: this.extractFileName(file.name),
                    uploading: false,
                    success: false,
                    error: false,
                    progress: 0
                }

                // 生成预览和获取尺寸
                await this.generatePreview(fileItem)

                this.fileList.push(fileItem)
            }
        },

        // 验证文件类型
        isValidFile (file) {
            const validImageTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp']
            const validVideoTypes = ['video/mp4', 'video/avi', 'video/mov', 'video/webm', 'video/ogg']
            const validDocTypes = [
                'application/pdf',
                'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // .doc, .docx
                'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xls, .xlsx
                'application/vnd.ms-powerpoint', 'application/vnd.openxmlformats-officedocument.presentationml.presentation', // .ppt, .pptx
                'text/plain' // .txt
            ]

            const allValidTypes = [...validImageTypes, ...validVideoTypes, ...validDocTypes]

            if (!allValidTypes.includes(file.type)) {
                this.$message.warning(this.$t('equipmentCenter.uploadDialog.unsupportedFileType', { file: file.name }))
                return false
            }

            // 文件大小限制 (1GB)
            if (file.size > 1024 * 1024 * 1024) {
                this.$message.warning(this.$t('equipmentCenter.uploadDialog.fileTooLarge', { file: file.name }))
                return false
            }

            return true
        },

        // 自动检测文件类型
        detectFileType (file) {
            const type = file.type
            if (type.startsWith('image/')) return 1 // 图片
            if (type.startsWith('video/')) return 2 // 视频
            if (type === 'application/pdf') return 3 // PDF
            if (type.includes('powerpoint') || type.includes('presentationml')) return 4 // PPT
            if (type.includes('msword') || type.includes('wordprocessingml')) return 5 // Word
            if (type.includes('ms-excel') || type.includes('spreadsheetml')) return 6 // Excel
            if (type === 'text/plain') return 7 // TXT
            return 8 // 其他
        },

        // 提取文件名（去除扩展名）
        extractFileName (filename) {
            const lastDotIndex = filename.lastIndexOf('.')
            return lastDotIndex > 0 ? filename.substring(0, lastDotIndex) : filename
        },

        // 生成预览和获取尺寸
        async generatePreview (fileItem) {
            return new Promise((resolve) => {
                if (fileItem.isImage) {
                    const img = new Image()
                    img.onload = () => {
                        fileItem.dimensions = {
                            width: img.width,
                            height: img.height
                        }
                        fileItem.preview = URL.createObjectURL(fileItem.file)
                        resolve()
                    }
                    img.onerror = () => resolve()
                    img.src = URL.createObjectURL(fileItem.file)
                } else if (fileItem.isVideo) {
                    const video = document.createElement('video')
                    video.onloadedmetadata = () => {
                        fileItem.dimensions = {
                            width: video.videoWidth,
                            height: video.videoHeight
                        }
                        fileItem.preview = URL.createObjectURL(fileItem.file)
                        resolve()
                    }
                    video.onerror = () => resolve()
                    video.src = URL.createObjectURL(fileItem.file)
                } else {
                    resolve()
                }
            })
        },

        // 获取文件图标
        getFileIcon (type) {
            if (type.startsWith('image/')) return 'el-icon-picture'
            if (type.startsWith('video/')) return 'el-icon-video-camera'
            if (type === 'application/pdf') return 'el-icon-tickets'
            if (type.includes('powerpoint') || type.includes('presentationml')) return 'el-icon-monitor'
            if (type.includes('ms-excel') || type.includes('spreadsheetml')) return 'el-icon-data-analysis'
            if (type.includes('msword') || type.includes('wordprocessingml')) return 'el-icon-document'
            if (type === 'text/plain') return 'el-icon-document'
            return 'el-icon-document'
        },

        // 获取文件类型文本
        getFileTypeText (type) {
            const types = {
                1: this.$t('equipmentCenter.uploadDialog.fileTypes.image'),
                2: this.$t('equipmentCenter.uploadDialog.fileTypes.video'),
                3: this.$t('equipmentCenter.uploadDialog.fileTypes.pdf'),
                4: this.$t('equipmentCenter.uploadDialog.fileTypes.ppt'),
                5: this.$t('equipmentCenter.uploadDialog.fileTypes.word'),
                6: this.$t('equipmentCenter.uploadDialog.fileTypes.excel'),
                7: this.$t('equipmentCenter.uploadDialog.fileTypes.txt'),
                8: this.$t('equipmentCenter.uploadDialog.fileTypes.other')
            }
            return types[type] || this.$t('equipmentCenter.uploadDialog.fileTypes.unknown')
        },

        // 格式化文件大小
        formatFileSize (bytes) {
            if (bytes === 0) return '0 B'
            const k = 1024
            const sizes = ['B', 'KB', 'MB', 'GB']
            const i = Math.floor(Math.log(bytes) / Math.log(k))
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
        },

        // 移除文件
        removeFile (index) {
            const file = this.fileList[index]
            if (file.preview) {
                URL.revokeObjectURL(file.preview)
            }
            this.fileList.splice(index, 1)
        },

        // 清空所有文件
        clearAll () {
            this.fileList.forEach(file => {
                if (file.preview) {
                    URL.revokeObjectURL(file.preview)
                }
            })
            this.fileList = []
            this.uploadedCount = 0
        },

        // 开始上传
        async startUpload () {
            if (this.fileList.length === 0) return

            this.isUploading = true
            this.uploadedCount = 0

            try {
                // 准备上传数据
                const formData = new FormData()
                const sourceWidths = []
                const sourceHeights = []
                const fileSizes = []

                this.fileList.forEach((fileItem, index) => {
                    formData.append('files', fileItem.file, fileItem.name)
                    sourceWidths.push(fileItem.dimensions?.width || 0)
                    sourceHeights.push(fileItem.dimensions?.height || 0)
                    fileSizes.push(fileItem.size || 0)
                    fileItem.uploading = true
                    fileItem.progress = 0
                })

                // 添加尺寸和大小数据
                sourceWidths.forEach(width => formData.append('source_widths', width))
                sourceHeights.forEach(height => formData.append('source_heights', height))
                fileSizes.forEach(size => formData.append('sizes', size))

                // 添加设备ID
                formData.append('equipment_id', this.equipment.id)

                const response = await uploadEquipmentMaterial(formData, {
                    onUploadProgress: (progressEvent) => {
                        const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
                        this.fileList.forEach(file => {
                            if (file.uploading) {
                                file.progress = progress
                            }
                        })
                    }
                })

                if (response.code === 0) {
                    // 标记所有文件为成功
                    this.fileList.forEach(file => {
                        file.uploading = false
                        file.success = true
                        file.progress = 100
                    })
                    this.uploadedCount = this.fileList.length

                    this.$message.success(this.$t('equipmentCenter.uploadDialog.uploadSuccess', { count: response.data.success_count }))

                    if (response.data.failure_count > 0) {
                        this.$message.warning(this.$t('equipmentCenter.uploadDialog.uploadPartialFailure', { count: response.data.failure_count }))
                    }

                    // 通知父组件刷新列表
                    this.$emit('upload-success')

                    // 延迟关闭弹窗
                    setTimeout(() => {
                        this.handleClose()
                    }, 1500)
                }
            } catch (error) {
                console.error('上传失败:', error)
                this.fileList.forEach(file => {
                    if (file.uploading) {
                        file.uploading = false
                        file.error = true
                    }
                })
                this.$message.error(this.$t('equipmentCenter.uploadDialog.uploadFailed'))
            } finally {
                this.isUploading = false
            }
        },

        // 关闭弹窗
        handleClose () {
            if (this.isUploading) return

            this.clearAll()
            this.$emit('update:visible', false)
        },

    },

    watch: {
        visible (newVal) {
            if (!newVal) {
                this.clearAll()
            }
        }
    },

    beforeDestroy () {
        // 清理预览URL
        this.fileList.forEach(file => {
            if (file.preview) {
                URL.revokeObjectURL(file.preview)
            }
        })
    }
}
</script>

<style lang="scss" scoped>
.upload-container {
    .group-selector {
        margin-bottom: 20px;
        padding: 15px;
        background: #f8f9fa;
        border-radius: 4px;
        border: 1px solid #e9ecef;

        .el-form-item {
            margin-bottom: 0;
            display: flex;
            align-items: center;

            ::v-deep .el-form-item__label {
                margin-right: 10px;
                font-weight: 500;
                color: #303133;
            }

            ::v-deep .el-form-item__content {
                display: flex;
                align-items: center;
            }
        }
    }

    .drag-upload-area {
        border: 2px dashed #d9d9d9;
        border-radius: 6px;
        background: #fafafa;
        text-align: center;
        padding: 40px 20px;
        cursor: pointer;
        transition: all 0.3s;

        &:hover,
        &.drag-over {
            border-color: #409EFF;
            background: #f0f9ff;
        }

        .el-icon-upload {
            font-size: 48px;
            color: #c0c4cc;
            margin-bottom: 16px;
        }

        .drag-text {
            p {
                margin: 8px 0;
                color: #606266;

                .click-text {
                    color: #409EFF;
                }
            }

            .tip {
                font-size: 12px;
                color: #909399;
            }
        }
    }

    .file-list {
        margin-top: 20px;

        .list-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #ebeef5;

            span {
                font-weight: 500;
                color: #303133;
            }
        }

        .file-items {
            max-height: 300px;
            overflow-y: auto;

            .file-item {
                display: flex;
                align-items: center;
                padding: 12px 0;
                border-bottom: 1px solid #f5f7fa;

                &.uploading {
                    background: #f0f9ff;
                }

                &.success {
                    background: #f0f9f0;
                }

                &.error {
                    background: #fef0f0;
                }

                .file-preview {
                    width: 60px;
                    height: 60px;
                    margin-right: 12px;
                    border-radius: 4px;
                    overflow: hidden;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    background: #f5f7fa;

                    img,
                    video {
                        width: 100%;
                        height: 100%;
                        object-fit: cover;
                    }

                    .file-icon {
                        font-size: 24px;
                        color: #909399;
                    }
                }

                .file-info {
                    flex: 1;
                    min-width: 0;

                    .file-name {
                        font-weight: 500;
                        color: #303133;
                        margin-bottom: 4px;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                    }

                    .file-details {
                        display: flex;
                        gap: 12px;
                        font-size: 12px;
                        color: #909399;
                        margin-bottom: 4px;

                        .file-size {
                            color: #606266;
                        }

                        .file-dimensions {
                            color: #409EFF;
                        }

                        .file-type {
                            background: #e1f3d8;
                            color: #67c23a;
                            padding: 2px 6px;
                            border-radius: 2px;
                        }
                    }

                    .auto-detected {
                        font-size: 12px;

                        .label {
                            color: #909399;
                        }

                        .value {
                            color: #409EFF;
                            font-weight: 500;
                        }
                    }
                }

                .file-status {
                    width: 120px;
                    text-align: right;

                    .success-icon {
                        color: #67c23a;
                        font-size: 18px;
                    }

                    .error-icon {
                        color: #f56c6c;
                        font-size: 18px;
                    }

                    .remove-btn {
                        color: #f56c6c;

                        &:hover {
                            background: #fef0f0;
                        }
                    }
                }
            }
        }
    }

    .upload-stats {
        display: flex;
        gap: 20px;
        padding: 15px 0;
        background: #f8f9fa;
        border-radius: 4px;
        margin-top: 15px;
        padding-left: 15px;

        .stats-item {
            .label {
                color: #909399;
                margin-right: 5px;
            }

            .value {
                color: #303133;
                font-weight: 500;
            }
        }
    }
}

.dialog-footer {
    text-align: right;
}
</style>