<template>
    <el-dialog :title="$t('material.groupDialog.title')" :visible.sync="visible" width="600px"
        :close-on-click-modal="false" :close-on-press-escape="false" @close="handleClose">
        <div class="group-management">
            <!-- 新增分组 -->
            <div class="add-group-section">
                <el-form :model="newGroupForm" :rules="groupRules" ref="newGroupForm" inline>
                    <el-form-item prop="name">
                        <el-input v-model="newGroupForm.name" :placeholder="$t('material.groupDialog.enterGroupName')"
                            style="width: 200px;">
                        </el-input>
                    </el-form-item>
                    <el-form-item prop="description">
                        <el-input v-model="newGroupForm.description"
                            :placeholder="$t('material.groupDialog.groupDescription')" style="width: 250px;">
                        </el-input>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="createGroup" :loading="creating">
                            <i class="el-icon-plus"></i> {{ $t('material.groupDialog.addGroup') }}
                        </el-button>
                    </el-form-item>
                </el-form>
            </div>

            <!-- 分组列表 -->
            <div class="group-list-section">
                <div class="section-header">
                    <span>{{ $t('material.groupDialog.existingGroups') }}</span>
                    <el-button type="text" @click="loadGroupList" :loading="loading">
                        <i class="el-icon-refresh"></i> {{ $t('material.groupDialog.refresh') }}
                    </el-button>
                </div>

                <div class="group-list" v-loading="loading">
                    <div v-if="groupList.length === 0" class="empty-state">
                        <i class="el-icon-folder-opened"></i>
                        <p>{{ $t('material.groupDialog.noGroups') }}</p>
                    </div>

                    <div v-else class="group-items">
                        <div v-for="group in groupList" :key="group.id" class="group-item">
                            <div class="group-info">
                                <div class="group-name">
                                    <i class="el-icon-folder"></i>
                                    {{ group.name }}
                                </div>
                                <div class="group-description" v-if="group.description">
                                    {{ group.description }}
                                </div>
                                <div class="group-meta">
                                    <span class="create-time">{{ $t('material.groupDialog.createTime') }}{{
                                        formatTime(group.created_at) }}</span>
                                </div>
                            </div>
                            <div class="group-actions">
                                <el-button type="text" @click="editGroup(group)" size="small">
                                    <i class="el-icon-edit"></i> {{ $t('material.groupDialog.edit') }}
                                </el-button>
                                <el-button type="text" @click="deleteGroup(group)" size="small" class="delete-btn">
                                    <i class="el-icon-delete"></i> {{ $t('material.groupDialog.delete') }}
                                </el-button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div slot="footer" class="dialog-footer">
            <el-button @click="handleClose">{{ $t('material.groupDialog.close') }}</el-button>
        </div>

        <!-- 编辑分组弹窗 -->
        <el-dialog :title="$t('material.groupDialog.editGroup')" :visible.sync="editDialogVisible" width="400px"
            append-to-body>
            <el-form :model="editGroupForm" :rules="groupRules" ref="editGroupForm">
                <el-form-item :label="$t('material.groupDialog.groupName')" prop="name">
                    <el-input v-model="editGroupForm.name"
                        :placeholder="$t('material.groupDialog.enterGroupName')"></el-input>
                </el-form-item>
                <el-form-item :label="$t('material.groupDialog.groupDescriptionLabel')" prop="description">
                    <el-input v-model="editGroupForm.description"
                        :placeholder="$t('material.groupDialog.groupDescription')" type="textarea" rows="3">
                    </el-input>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="editDialogVisible = false">{{ $t('material.groupDialog.cancel') }}</el-button>
                <el-button type="primary" @click="updateGroup" :loading="updating">{{ $t('material.groupDialog.confirm')
                    }}</el-button>
            </div>
        </el-dialog>
    </el-dialog>
</template>

<script>
import { getAllMaterialGroups, createMaterialGroup, editMaterialGroup, deleteMaterialGroup } from '@/api/material'

export default {
    name: 'MaterialGroupDialog',
    props: {
        visible: {
            type: Boolean,
            default: false
        }
    },
    data () {
        return {
            loading: false,
            creating: false,
            updating: false,
            groupList: [],
            newGroupForm: {
                name: '',
                description: ''
            },
            editGroupForm: {
                id: null,
                name: '',
                description: ''
            },
            editDialogVisible: false,
            groupRules: {
                name: [
                    { required: true, message: this.$t('material.groupDialog.enterGroupNameRequired'), trigger: 'blur' },
                    { min: 1, max: 50, message: this.$t('material.groupDialog.groupNameLengthError'), trigger: 'blur' }
                ],
                description: [
                    { max: 200, message: this.$t('material.groupDialog.groupDescriptionLengthError'), trigger: 'blur' }
                ]
            }
        }
    },
    watch: {
        visible (newVal) {
            if (newVal) {
                this.loadGroupList()
            }
        }
    },
    methods: {
        // 加载分组列表
        async loadGroupList () {
            this.loading = true
            try {
                const response = await getAllMaterialGroups()
                if (response.code === 0) {
                    this.groupList = response.data || []
                }
            } catch (error) {
                console.error('获取分组列表失败:', error)
                this.$message.error(this.$t('material.groupDialog.loadGroupsFailed'))
            } finally {
                this.loading = false
            }
        },

        // 创建分组
        async createGroup () {
            this.$refs.newGroupForm.validate(async (valid) => {
                if (!valid) return

                this.creating = true
                try {
                    const response = await createMaterialGroup(this.newGroupForm)
                    if (response.code === 0) {
                        this.$message.success(this.$t('material.groupDialog.groupCreatedSuccess'))
                        this.resetNewGroupForm()
                        this.loadGroupList()
                        // 通知父组件刷新分组列表
                        this.$emit('group-changed')
                    } else {
                        this.$message.error(response.msg || this.$t('material.groupDialog.createGroupFailed'))
                    }
                } catch (error) {
                    console.error('创建分组失败:', error)
                    this.$message.error(this.$t('material.groupDialog.createGroupFailed'))
                } finally {
                    this.creating = false
                }
            })
        },

        // 编辑分组
        editGroup (group) {
            this.editGroupForm = {
                id: group.id,
                name: group.name,
                description: group.description || ''
            }
            this.editDialogVisible = true
        },

        // 更新分组
        async updateGroup () {
            this.$refs.editGroupForm.validate(async (valid) => {
                if (!valid) return

                this.updating = true
                try {
                    const { id, ...updateData } = this.editGroupForm
                    const response = await editMaterialGroup(id, updateData)
                    if (response.code === 0) {
                        this.$message.success(this.$t('material.groupDialog.groupUpdatedSuccess'))
                        this.editDialogVisible = false
                        this.loadGroupList()
                        // 通知父组件刷新分组列表
                        this.$emit('group-changed')
                    } else {
                        this.$message.error(response.msg || this.$t('material.groupDialog.updateGroupFailed'))
                    }
                } catch (error) {
                    console.error('更新分组失败:', error)
                    this.$message.error(this.$t('material.groupDialog.updateGroupFailed'))
                } finally {
                    this.updating = false
                }
            })
        },

        // 删除分组
        deleteGroup (group) {
            this.$confirm(this.$t('material.groupDialog.confirmDeleteGroup', { name: group.name }), this.$t('material.groupDialog.confirmTitle'), {
                confirmButtonText: this.$t('material.groupDialog.confirm'),
                cancelButtonText: this.$t('material.groupDialog.cancel'),
                type: 'warning'
            }).then(async () => {
                try {
                    const response = await deleteMaterialGroup(group.id)
                    if (response.code === 0) {
                        this.$message.success(this.$t('material.groupDialog.groupDeletedSuccess'))
                        this.loadGroupList()
                        // 通知父组件刷新分组列表
                        this.$emit('group-changed')
                    } else {
                        this.$message.error(response.msg || this.$t('material.groupDialog.deleteGroupFailed'))
                    }
                } catch (error) {
                    console.error('删除分组失败:', error)
                    this.$message.error(this.$t('material.groupDialog.deleteGroupFailed'))
                }
            }).catch(() => {
                // 用户取消删除
            })
        },

        // 重置新增分组表单
        resetNewGroupForm () {
            this.newGroupForm = {
                name: '',
                description: ''
            }
            this.$refs.newGroupForm.resetFields()
        },

        // 格式化时间
        formatTime (timestamp) {
            if (!timestamp) return '-'
            const date = new Date(timestamp * 1000)
            return date.toLocaleString('zh-CN')
        },

        // 关闭弹窗
        handleClose () {
            this.resetNewGroupForm()
            this.editDialogVisible = false
            this.$emit('update:visible', false)
        }
    }
}
</script>

<style lang="scss" scoped>
.group-management {
    .add-group-section {
        padding: 20px;
        background: #f8f9fa;
        border-radius: 4px;
        margin-bottom: 20px;

        .el-form {
            margin-bottom: 0;
        }
    }

    .group-list-section {
        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #ebeef5;
            margin-bottom: 15px;

            span {
                font-weight: 500;
                color: #303133;
            }
        }

        .group-list {
            min-height: 200px;

            .empty-state {
                text-align: center;
                padding: 40px 0;
                color: #909399;

                i {
                    font-size: 48px;
                    margin-bottom: 16px;
                    display: block;
                }

                p {
                    margin: 0;
                    font-size: 14px;
                }
            }

            .group-items {
                .group-item {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 15px;
                    border: 1px solid #ebeef5;
                    border-radius: 4px;
                    margin-bottom: 10px;
                    transition: all 0.3s;

                    &:hover {
                        border-color: #409EFF;
                        background: #f0f9ff;
                    }

                    .group-info {
                        flex: 1;

                        .group-name {
                            font-weight: 500;
                            color: #303133;
                            margin-bottom: 5px;

                            i {
                                color: #409EFF;
                                margin-right: 8px;
                            }
                        }

                        .group-description {
                            color: #606266;
                            font-size: 13px;
                            margin-bottom: 5px;
                        }

                        .group-meta {
                            font-size: 12px;
                            color: #909399;

                            .create-time {
                                margin-right: 15px;
                            }
                        }
                    }

                    .group-actions {
                        display: flex;
                        gap: 5px;

                        .delete-btn {
                            color: #f56c6c;

                            &:hover {
                                background: #fef0f0;
                            }
                        }
                    }
                }
            }
        }
    }
}

.dialog-footer {
    text-align: right;
}
</style>