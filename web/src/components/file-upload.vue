<template>
  <div>
    <el-upload ref="uploadRef" :action="upload_host" name="uploadFile" :headers="headers"
      :multiple="uploadNum > 1 ? true : false" :show-file-list="false" :before-upload="beforeUpload"
      :on-remove="handleRemove" :on-progress="handleUploadProgress" :on-success="handleUploadSuccess"
      :disabled="!isDisabled" :class="name" :accept="acceptFileType || defaultAcceptType" class="avatar-uploader2"
      :data="uploadData">
    </el-upload>
    <div class="images">
      <div class="item" v-for="(item, index) in fileList" :key="index">
        <div class="img" @mouseover="item.hover = true" @mouseout="item.hover = false" id="preview-item">
          <el-image @click.stop="handleClickItem" ref="img" v-if="isImageUrl(item.url)" :src="imageUrl + item.url"
            :preview-src-list="previewImages()" style="width: 120px; height: 120px" fit="cover"></el-image>
          <video v-else-if="isVideoUrl(item.url)" controls style="width: 120px; height: 120px; display: block">
            <source :src="imageUrl + item.url" type="video/avi">
          </video>
          <div v-else class="file-preview">
            <i :class="getFileIconClass(item.url)"></i>
            <div class="file-name">{{ item.name || getFileNameFromUrl(item.url) }}</div>
          </div>
          <div class="mask" v-show="item.hover">
            <i class="el-icon-zoom-in" @click="previewFile(item)" v-if="canPreview(item.url)"></i>
            <i class="el-icon-download" @click="downloadFile(item.url)" v-else></i>
            <i class="el-icon-upload2" @click="uploadImage(item.url)" v-if="!isDisabled"></i>
            <i class="el-icon-delete" @click="deleteImage(item.url)" v-if="!isDisabled"></i>
          </div>
        </div>
      </div>
      <div class="add" v-if="fileList.length < uploadNum" @click="uploadImage()">
        <i class="el-icon-plus"></i>
      </div>
    </div>
  </div>
</template>

<script>
import request from "@/utils/request";
import { findElem } from "@/utils/util.js";
import store from "@/store";

export default {
  props: {
    isDisabled: Boolean,
    fileType: String,
    name: String,
    acceptFileType: {
      type: String,
      default: "",
    },
    fileList: Array,
    uploadNum: {
      type: Number,
      default: 1,
    },
    index: {
      type: Number,
      default: 0,
    },
    equipmentId: {
      type: Number,
      default: null,
    },
  },
  data () {
    return {



      upload_host: process.env.VUE_APP_BASE_API + `admin/sourcematerial/upload/equipment`,
      imageUrl: process.env.VUE_APP_BASE_API + "assets/media/",
      headers: {
        Authorization: store.getters.token,
      },
      uploadData: {
        source_width: 0,
        source_height: 0,
        equipment_id: this.equipmentId,
        size: 0,
      },
      showImage: "",
      dialogVisible: false,
      uploadImageId: "",
      list: [],
    };
  },
  computed: {
    defaultAcceptType () {
      switch (this.fileType) {
        case "image":
          return "image/*";
        case "video":
          return "video/*";
        case "image/video":
          return "image/*,video/*";
        case "document":
          return ".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx";
        case "file":
          return "image/*,video/*,.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx";
        default:
          return "";
      }
    },
    showFileList: {
      get: function () {
        return this.fileList && this.fileList.length > 0;
      },
      set: function (newValue) { },
    },
  },
  methods: {
    emitInput (val) {
      this.$emit("input", val);
    },
    handleClickItem () {
      setTimeout(() => {
        let domImageMask = document.querySelector(".el-image-viewer__wrapper");
        if (!domImageMask) return;

        domImageMask.addEventListener("click", (e) => {
          if (e.target.parentNode.className == "el-image-viewer__actions__inner") {
            return;
          }
          document.querySelector(".el-image-viewer__close").click();
        });
      }, 300);
    },
    handleRemove (file, fileList) {
      if (fileList.length === 0) {
        this.fileList = [];
      } else {
        this.fileList = fileList;
      }
    },
    getUUID () {
      return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, (c) => {
        return (c === "x" ? (Math.random() * 16) | 0 : "r&0x3" | "0x8").toString(16);
      });
    },
    beforeUpload (file) {
      const isValidType = this.checkFileType(file);
      if (!isValidType) {
        this.handleInvalidType(file);
        return false;
      }

      this.list.push(file.name);
      if (this.list.length > this.uploadNum) {
        this.$message.warning(this.$t("upload.maxFileCount", { count: this.uploadNum }));
        this.resetUploadState();
        return false;
      }

      const setDimensions = (width, height) => {
        this.uploadData.source_width = width;
        this.uploadData.source_height = height;
        this.uploadData.size = file.size;
      };

      if (file.type.includes("image")) {
        return new Promise((resolve) => {
          const img = new Image();
          img.src = URL.createObjectURL(file);
          img.onload = () => {
            setDimensions(img.width, img.height);
            this.$emit("fileData", { width: img.width, height: img.height });
            URL.revokeObjectURL(img.src);
            resolve(true);
          };
          img.onerror = () => resolve(false);
        });
      } else if (file.type.includes("video")) {
        return new Promise((resolve) => {
          const video = document.createElement('video');
          video.preload = 'metadata';
          video.src = URL.createObjectURL(file);
          video.onloadedmetadata = () => {
            setDimensions(video.videoWidth, video.videoHeight);
            this.$emit("fileData", { width: video.videoWidth, height: video.videoHeight });
            URL.revokeObjectURL(video.src);
            resolve(true);
          };
          video.onerror = () => resolve(false);
        });
      }

      return true;
    },
    checkFileType (file) {
      console.log(this.fileType)
      if (this.fileType === "image") {
        return file.type.includes("image");
      } else if (this.fileType === "video") {
        return file.type.includes("video");
      } else if (this.fileType === "image/video") {
        return file.type.includes("image") || file.type.includes("video");
      } else if (this.fileType === "document") {
        const documentTypes = [
          "application/pdf",
          "application/msword",
          "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
          "application/vnd.ms-powerpoint",
          "application/vnd.openxmlformats-officedocument.presentationml.presentation",
          "application/vnd.ms-excel",
          "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        ];
        return documentTypes.includes(file.type);
      } else if (this.fileType === "file") {
        // 支持所有类型的文件（图片、视频、文档）
        const allowedTypes = [
          // 图片类型
          "image/jpeg",
          "image/jpg",
          "image/png",
          "image/gif",
          "image/webp",
          // 视频类型
          "video/mp4",
          "video/ogg",
          "video/avi",
          "video/quicktime",
          "video/webm",
          // 文档类型
          "application/pdf",
          "application/msword",
          "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
          "application/vnd.ms-powerpoint",
          "application/vnd.openxmlformats-officedocument.presentationml.presentation",
          "application/vnd.ms-excel",
          "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        ];
        return allowedTypes.includes(file.type);
      } else if (this.fileType === "custom") {
        // 使用 acceptFileType prop 来进行校验，使其更具通用性
        const allowedTypes = this.acceptFileType.split(',').map(item => item.trim());
        return allowedTypes.includes(file.type);
      }
      return true;
    },
    handleInvalidType (file) {
      const warningMsg = this.getWarningMessage();
      this.$message.warning(warningMsg);
      this.resetUploadState();
    },
    getWarningMessage () {
      switch (this.fileType) {
        case "image":
          return this.$t("upload.onlyImage");
        case "video":
          return this.$t("upload.onlyVideo");
        case "image/video":
          return this.$t("upload.onlyVideoOrImageAgain");
        case "document":
          return this.$t("upload.onlyDocument");
        case "file":
          return this.$t("upload.onlyFileTypes");
        case "pdf":
          return this.$t("material.dialog.form.onlyPDF");
        default:
          return "";
      }
    },
    resetUploadState () {
      this.$refs.uploadRef.clearFiles();
      this.list = [];
      this.fileList = [...this.fileList];
    },
    handleUploadProgress () {
      this.$emit("uploadStatus", true);
    },
    handleUploadSuccess (res, file, fileList) {
      this.$emit("uploadStatus", false);
      this.fileList.push({
        name: file.name,
        hover: false,
        url: file.response.data.file_name,
      });

      this.$emit(
        "editUrl",
        {
          name: file.name,
          hover: false,
          url: file.response.data.file_name,
        },
        this.name,
        this.index
      );
    },
    previewImages () {
      let images = [];
      if (this.fileList && this.fileList.length > 0) {
        this.fileList.forEach((item) => {
          if (this.isImageUrl(item.url)) {
            const fullUrl = this.imageUrl + item.url;
            images.push(fullUrl);
          }
        });
      }
      return images;
    },
    previewImage (url) {
      let images = [];
      this.fileList.forEach((item) => {
        if (this.isImageUrl(item.url)) {
          images.push(item);
        }
      });
      let index = findElem(images, "url", url);
      if (index !== -1 && this.$refs.img && this.$refs.img[index]) {
        this.$refs.img[index].showViewer = true;
      }
    },
    previewVideo (url) {
      // 保留原视频预览逻辑
    },
    previewFile (item) {
      if (this.isImageUrl(item.url)) {
        this.previewImage(item.url);
      } else if (this.isVideoUrl(item.url)) {
        this.previewVideo(item.url);
      } else if (this.isDocumentUrl(item.url)) {
        // 对于 PDF/PPT/Excel/Word 文档，直接在新窗口打开
        window.open(this.imageUrl + item.url, "_blank");
      } else {
        // 对于其他文件，也提供下载预览
        window.open(this.imageUrl + item.url, "_blank");
      }
    },
    isImageUrl (url) {
      if (!url) return false;
      const fileSuffix = url.substring(url.lastIndexOf(".") + 1).toLowerCase();
      const whiteList = ["jpg", "jpeg", "png", "gif", "webp"];
      return whiteList.includes(fileSuffix);
    },
    isVideoUrl (url) {
      if (!url) return false;
      const videoSuffix = ["mp4", "ogg", "mov", "webm", "avi"];
      const suffix = url.substring(url.lastIndexOf(".") + 1).toLowerCase();
      return videoSuffix.includes(suffix);
    },
    isDocumentUrl (url) {
      if (!url) return false;
      const documentSuffix = ["pdf", "doc", "docx", "xls", "xlsx", "ppt", "pptx"];
      const suffix = url.substring(url.lastIndexOf(".") + 1).toLowerCase();
      return documentSuffix.includes(suffix);
    },
    canPreview (url) {
      return this.isImageUrl(url) || this.isVideoUrl(url) || this.isDocumentUrl(url);
    },
    downloadFile (url) {
      window.open(this.imageUrl + url, "_blank");
    },
    uploadImage (id) {
      this.list = [];
      this.uploadImageId = id;
      // 使用 $refs 替代 document.querySelector，确保组件内部元素被正确访问
      if (this.$refs.uploadRef) {
        this.$refs.uploadRef.$el.querySelector('input').click();
      }
    },
    deleteImage (url) {
      const index = findElem(this.fileList, "url", url);
      if (index !== -1) {
        this.fileList.splice(index, 1);
      }
    },
    submitUpload () {
      if (this.$refs.uploadRef) {
        this.$refs.uploadRef.submit();
      }
    },
  },
};
</script>

<style type="text/css" lang="scss" scoped>
.images {
  display: flex;
  flex-wrap: wrap;

  .item {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 120px;
    margin-right: 20px;
    margin-bottom: 15px;

    .img {
      border: 1px dashed #eaeaea;
      border-radius: 5px;
      overflow: hidden;
      position: relative;

      .el-image {
        display: block;
      }

      .mask {
        position: absolute;
        left: 0;
        top: 0;
        width: 120px;
        height: 120px;
        background: rgba($color: #000000, $alpha: 0.3);
        display: flex;
        align-items: center;
        justify-content: center;

        i {
          font-size: 20px;
          color: #ffffff;
          cursor: pointer;
          margin: 0 8px;
        }
      }
    }

    .add {
      border: 1px solid #dddddd;
      border-radius: 5px;
      cursor: pointer;
    }

    .text {
      font-size: 14px;
      color: #666666;
    }
  }

  .add {
    width: 120px;
    height: 120px;
    border: 1px solid #dddddd;
    border-radius: 5px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;

    i {
      font-size: 30px;
      color: #999;
    }
  }
}

.avatar-uploader2 {
  height: 0;
}

.file-preview {
  width: 120px;
  height: 120px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 1px dashed #eaeaea;
  border-radius: 5px;

  i {
    font-size: 40px;
    color: #409EFF;
    margin-bottom: 10px;
  }

  .file-name {
    max-width: 100px;
    text-align: center;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 12px;
    color: #606266;
  }
}
</style>