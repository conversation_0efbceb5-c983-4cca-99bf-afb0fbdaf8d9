<template>
    <div class="common-materials-panel">
        <div class="panel-header">
            <h4>{{ $t('commonMaterials.title') }}</h4>
            <div class="header-controls">
                <el-button-group>
                    <el-button size="mini" :type="viewMode === 'list' ? 'primary' : 'default'"
                        @click="viewMode = 'list'" icon="el-icon-list">
                        {{ $t('commonMaterials.viewMode.list') }}
                    </el-button>
                    <el-button size="mini" :type="viewMode === 'grid' ? 'primary' : 'default'"
                        @click="viewMode = 'grid'" icon="el-icon-menu">
                        {{ $t('commonMaterials.viewMode.grid') }}
                    </el-button>
                </el-button-group>
                <el-input v-model="searchTerm" :placeholder="$t('commonMaterials.searchPlaceholder')" size="mini"
                    clearable @input="filterMaterials" class="search-input">
                    <i slot="prefix" class="el-input__icon el-icon-search"></i>
                </el-input>
            </div>
        </div>
        <el-tabs v-model="activeTab" @tab-click="handleTabClick" class="material-tabs">
            <el-tab-pane :label="$t('commonMaterials.tabs.all')" name="all"></el-tab-pane>
            <el-tab-pane :label="$t('commonMaterials.tabs.image')" name="image"></el-tab-pane>
            <el-tab-pane :label="$t('commonMaterials.tabs.video')" name="video"></el-tab-pane>
        </el-tabs>
        <div v-loading="isLoading" :class="['materials-grid', `view-${viewMode}`]">
            <el-card v-for="material in filteredMaterials" :key="material.id" shadow="hover"
                :class="['material-card', `card-${viewMode}`]" draggable="true"
                @dragstart.native="handleDragStart($event, material)">
                <div class="card-preview">
                    <el-image v-if="material.type === 1" :src="imageUrl + material.path" fit="cover"
                        class="preview-media"></el-image>
                    <video v-else-if="material.type === 2" :src="imageUrl + material.path" class="preview-media"
                        muted></video>
                    <div class="media-overlay">
                        <i :class="material.type === 1 ? 'el-icon-picture-outline' : 'el-icon-video-camera'"></i>
                    </div>
                </div>
                <div class="card-info">
                    <p class="card-name" :title="material.name">
                        <span class="file-name">{{ material.name }}</span>
                        <span class="file-extension">{{ getFileExtension(material.path) }}</span>
                    </p>
                    <div class="material-size" v-if="material.type === 1 || material.type === 2">
                        <span class="size-text">
                            <template v-if="material.type === 1">
                                {{ material.source_width || 200 }} × {{ material.source_height || 200 }}
                            </template>
                            <template v-else-if="material.type === 2">
                                {{ material.source_width || 240 }} × {{ material.source_height || 50 }}
                            </template>
                        </span>
                    </div>
                </div>
            </el-card>
            <el-empty v-if="!isLoading && filteredMaterials.length === 0" :description="$t('commonMaterials.empty')"
                :image-size="80"></el-empty>
        </div>
        <el-pagination v-if="total > pageSize" small background layout="prev, pager, next" :total="total"
            :page-size.sync="pageSize" :current-page.sync="pageNum" @current-change="fetchMaterials"
            class="pagination-footer">
        </el-pagination>
    </div>
</template>

<script>
import { getMaterialList } from "@/api/template.js";

export default {
    name: "CommonMaterialsPanel",
    data () {
        return {
            isLoading: false,
            activeTab: "all",
            searchTerm: "",
            viewMode: "list", // 默认列表模式
            allMaterials: [],
            filteredMaterials: [],
            pageNum: 1,
            pageSize: 15,
            total: 0,
            imageUrl: process.env.VUE_APP_BASE_API + "assets/media/",
        };
    },
    created () {
        this.fetchMaterials();
    },
    methods: {
        fetchMaterials () {
            this.isLoading = true;
            let type = 0;
            if (this.activeTab === "image") type = 1;
            if (this.activeTab === "video") type = 2;

            getMaterialList({
                page: this.pageNum,
                pageSize: this.pageSize,
                type: type || null, // `0` or `null` for all
            })
                .then((res) => {
                    if (res.code === 0) {
                        this.allMaterials = res.data.data;
                        this.total = res.data.total;
                        this.filterMaterials();
                    }
                })
                .finally(() => {
                    this.isLoading = false;
                });
        },
        handleTabClick () {
            this.pageNum = 1;
            this.fetchMaterials();
        },
        filterMaterials () {
            if (this.searchTerm) {
                this.filteredMaterials = this.allMaterials.filter((m) =>
                    m.name.toLowerCase().includes(this.searchTerm.toLowerCase())
                );
            } else {
                this.filteredMaterials = this.allMaterials;
            }
        },
        // handleMaterialClick (material) {
        //     this.$emit("add-material-to-canvas", material);
        // },
        handleDragStart (event, material) {
            event.dataTransfer.setData("application/json", JSON.stringify(material));
            event.dataTransfer.effectAllowed = "copy";
        },
        getFileExtension (filePath) {
            if (!filePath) return '';
            const extension = filePath.split('.').pop().toLowerCase();
            return extension ? `.${extension}` : '';
        },
    },
};
</script>

<style scoped lang="scss">
.common-materials-panel {
    width: 100%;
    height: 720px;
    display: flex;
    flex-direction: column;
    background-color: #ffffff;
    border-right: 1px solid #e0e0e0;
    padding: 10px;
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

.panel-header {
    margin-bottom: 10px;
    padding-bottom: 8px;
    border-bottom: 1px solid #f0f0f0;

    h4 {
        margin: 0 0 8px 0;
        font-size: 13px;
        font-weight: 600;
        color: #303133;
        display: flex;
        align-items: center;

        &::before {
            content: '';
            display: inline-block;
            width: 3px;
            height: 13px;
            background: linear-gradient(135deg, #409eff, #67c23a);
            border-radius: 2px;
            margin-right: 6px;
        }
    }

    .header-controls {
        display: flex;
        align-items: center;
        gap: 8px;
        flex-wrap: wrap;

        .search-input {
            flex: 1;
            min-width: 120px;
        }
    }
}

.material-tabs {
    margin-bottom: 12px;

    :deep(.el-tabs__header) {
        margin: 0;
    }

    :deep(.el-tabs__nav-wrap::after) {
        display: none;
    }

    :deep(.el-tabs__item) {
        font-size: 11px;
        padding: 0 12px;
        height: 28px;
        line-height: 28px;
        color: #606266;
        transition: all 0.3s ease;

        &.is-active {
            color: #409eff;
            font-weight: 500;
        }

        &:hover {
            color: #409eff;
            background-color: rgba(64, 158, 255, 0.05);
        }
    }

    :deep(.el-tabs__active-bar) {
        background-color: #409eff;
        height: 2px;
        border-radius: 1px;
    }
}

.materials-grid {
    overflow-y: auto;
    max-height: calc(720px - 140px);
    /* 减去头部和分页区域的高度 */

    &.view-list {
        display: grid;
        grid-template-columns: 1fr;
        gap: 4px;
        padding: 2px 4px 2px 0;
    }

    &.view-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
        gap: 12px;
        padding: 4px;
    }
}

.material-card {
    cursor: pointer;
    border: 1px solid #e4e7ed;
    border-radius: 2px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    background: #ffffff;

    &:hover {
        border-color: #409eff;
        box-shadow: 0 4px 20px rgba(64, 158, 255, 0.15);
        transform: translateY(-2px);
    }

    .card-preview {
        position: relative;
        overflow: hidden;
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);

        .preview-media {
            width: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .media-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(64, 158, 255, 0.8) 0%, rgba(103, 194, 58, 0.8) 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            opacity: 0;
            transition: all 0.3s ease;
            backdrop-filter: blur(2px);
        }
    }

    &:hover .media-overlay {
        opacity: 1;
    }

    &:hover .preview-media {
        transform: scale(1.05);
    }

    .card-info {
        background: #ffffff;

        .card-name {
            color: #303133;
            margin: 0;
            font-weight: 500;
            line-height: 1.4;
            display: flex;
            align-items: center;
            gap: 4px;

            .file-name {
                flex: 1;
                display: -webkit-box;
                -webkit-line-clamp: 1;
                -webkit-box-orient: vertical;
                overflow: hidden;
                text-overflow: ellipsis;
            }

            .file-extension {
                font-size: 10px;
                color: #909399;
                background: #f5f7fa;
                padding: 1px 4px;
                border-radius: 3px;
                border: 1px solid #e4e7ed;
                font-weight: 600;
                white-space: nowrap;
                flex-shrink: 0;
            }
        }

        .material-size {
            display: flex;
            align-items: center;
            justify-content: center;

            .size-text {
                font-size: 10px;
                padding: 2px 6px;
                border-radius: 4px;
                font-weight: 500;
                white-space: nowrap;
                border: 1px solid;

                /* 图片尺寸样式 */
                :deep(.material-size:has(+ .material-size .size-text)) & {
                    color: #409eff;
                    background: rgba(64, 158, 255, 0.1);
                    border-color: rgba(64, 158, 255, 0.3);
                }

                /* 视频尺寸样式 */
                :deep(.material-size:last-child .size-text) {
                    color: #67c23a;
                    background: rgba(103, 194, 58, 0.1);
                    border-color: rgba(103, 194, 58, 0.3);
                }
            }
        }
    }

    /* 列表模式样式 */
    &.card-list {
        min-height: 6px;

        .card-preview {
            display: none;
            /* 隐藏预览图 */
        }

        .card-info {
            padding: 2px 5px;

            .card-name {
                font-size: 12px;
                margin-bottom: 0;
                -webkit-line-clamp: 1;
                /* 只显示一行 */
            }

            .material-size {
                display: none;
                /* 隐藏尺寸信息 */
            }
        }

        &:hover {
            background: linear-gradient(135deg, rgba(64, 158, 255, 0.05) 0%, rgba(103, 194, 58, 0.05) 100%);
            border-color: #409eff;
            transform: none;
            /* 列表模式不使用上浮效果 */
        }
    }

    /* 缩略图模式样式 */
    &.card-grid {
        min-height: 160px;

        .card-preview {
            height: 100px;

            .preview-media {
                height: 100%;
            }

            .media-overlay {
                font-size: 18px;
            }
        }

        .card-info {
            padding: 8px;

            .card-name {
                font-size: 10px;
                margin-bottom: 4px;

                .file-name {
                    display: -webkit-box;
                    -webkit-line-clamp: 2;
                    -webkit-box-orient: vertical;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }

                .file-extension {
                    font-size: 9px;
                    padding: 1px 3px;
                }
            }
        }
    }
}

.pagination-footer {
    margin-top: 8px;
    text-align: center;
}

.search-input {
    :deep(.el-input__inner) {
        border-radius: 6px;
        border: 1px solid #dcdfe6;
        font-size: 11px;

        &:focus {
            border-color: #409eff;
            box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
        }

        &::placeholder {
            color: #a8abb2;
        }
    }

    :deep(.el-input__prefix) {
        color: #c0c4cc;
    }
}
</style>
