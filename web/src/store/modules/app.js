import Cookies from 'js-cookie'
// import { getUnreadNotice } from '@/api/notificationCenter'

const state = {
  sidebar: {
    opened: Cookies.get('sidebarStatus') ? !!+Cookies.get('sidebarStatus') : true,
    withoutAnimation: false
  },
  device: 'desktop',
  l1Path: '',
  allUnreadNum: 0,
  show: null
}

const mutations = {
  SET_L1_PATH: (state, newPath) => {
    state.l1Path = newPath
  },
  TOGGLE_SIDEBAR: state => {
    state.sidebar.opened = !state.sidebar.opened
    state.sidebar.withoutAnimation = false
    if (state.sidebar.opened) {
      Cookies.set('sidebarStatus', 1)
    } else {
      Cookies.set('sidebarStatus', 0)
    }
  },
  CLOSE_SIDEBAR: (state, withoutAnimation) => {
    Cookies.set('sidebarStatus', 0)
    state.sidebar.opened = false
    state.sidebar.withoutAnimation = withoutAnimation
  },
  TOGGLE_DEVICE: (state, device) => {
    state.device = device
  },
  SET_UNREAD_NOTICE: (state, num) => {
    state.allUnreadNum = num
  },
  SET_SHOW:(state,bool)=>{
    state.show = bool
  }
}

const actions = {
  toggleSideBar({ commit }) {
    commit('TOGGLE_SIDEBAR')
  },
  closeSideBar({ commit }, { withoutAnimation }) {
    commit('CLOSE_SIDEBAR', withoutAnimation)
  },
  toggleDevice({ commit }, device) {
    commit('TOGGLE_DEVICE', device)
  },
  getUnreadNum({ commit }) {
    // getUnreadNotice({ noticeType: '', readFlag: 'NO' }).then(res => {
    //   console.log('获取未读数量')
    //   commit('SET_UNREAD_NOTICE', res.data)
    // })
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
