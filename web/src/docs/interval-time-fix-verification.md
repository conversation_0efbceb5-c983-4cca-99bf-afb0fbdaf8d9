# 定时时间修复验证

## 修复的关键点

### 问题场景
1. 用户添加多个图片到同一个区域（形成 multiFiles）
2. 为每个图片设置不同的定时时间（如 7秒、10秒、5秒）
3. 删除其中一个图片
4. **问题**：剩余图片的定时时间变为 0

### 根本原因
当删除素材后只剩一个文件时，代码会：
1. 将 `multiFiles` 模式转换为单文件模式
2. 只复制 `path`、`sm_name`、`sm_id` 属性
3. **遗漏了 `interval_time` 属性**

### 修复方案
1. **保留 interval_time**：在转换为单文件模式时，明确保留 `interval_time` 属性
2. **修复计算属性**：确保 `multiFileMaterials` 在单文件模式下返回正确的数据

## 验证步骤

### 测试数据流
```javascript
// 初始状态：multiFiles 模式
selectedMaterial = {
  clientKey: 123,
  multiFiles: [
    { clientKey: 1, path: 'img1.jpg', interval_time: 7 },
    { clientKey: 2, path: 'img2.jpg', interval_time: 10 }
  ]
}

// 删除第二个文件后
selectedMaterial = {
  clientKey: 123,
  path: 'img1.jpg',           // ✅ 正确复制
  sm_name: 'img1.jpg',        // ✅ 正确复制  
  sm_id: 1,                   // ✅ 正确复制
  interval_time: 7            // ✅ 现在会正确保留
  // multiFiles 属性被删除
}
```

### 验证 multiFileMaterials 计算属性
```javascript
// 修复前：可能返回错误的数据
multiFileMaterials() {
  // 单文件模式下，使用 fallback 逻辑
  // 返回 currentPageMaterials 中的素材，可能没有正确的 interval_time
}

// 修复后：返回正确的数据
multiFileMaterials() {
  // 单文件模式下，直接返回 selectedMaterial
  // 确保包含正确的 interval_time
  return [this.selectedMaterial];
}
```

## 预期结果

### 修复前
- 删除素材后，剩余素材的定时时间显示为 0
- 用户需要重新设置定时时间

### 修复后  
- 删除素材后，剩余素材的定时时间保持原来的值（如 7秒）
- 用户不需要重新设置

## 快速测试方法

1. 添加 2 个图片到同一区域
2. 设置第一个图片定时时间为 7 秒
3. 设置第二个图片定时时间为 10 秒
4. 删除第二个图片
5. **验证**：第一个图片的定时时间应该仍然是 7 秒

## 调试信息

修复后的代码会输出以下调试信息：
```
Converted to single file mode, preserved interval_time: 7
Returning single material with interval_time: 7
```

如果看到这些信息，说明修复正在工作。
