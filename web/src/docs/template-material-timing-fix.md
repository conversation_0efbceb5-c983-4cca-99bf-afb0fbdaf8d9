# 模板素材定时时间重置问题修复

## 问题描述

在模板编辑功能中，当删除一个素材后，剩余素材的定时时间（interval_time）会从原来设置的值（如7秒）重置为0秒。

## 问题根本原因

问题有两个根本原因：

### 1. 数据丢失问题（主要原因）
当删除素材后只剩一个文件时，代码会将 `multiFiles` 模式转换为单文件模式，但在转换过程中**没有保留 `interval_time` 属性**，导致定时时间丢失。

### 2. 组件重新渲染问题（次要原因）
过度使用 `$forceUpdate()` 方法会导致：
1. **输入框状态丢失**：`el-input-number` 组件被重新创建，丢失了用户输入的定时时间值
2. **数据不一致**：虽然底层数据可能是正确的，但UI组件重新渲染时显示了默认值

### 具体触发路径

1. 用户删除素材 → `deleteMaterialByClientKey()` 方法被调用
2. 删除素材后只剩一个文件 → 转换为单文件模式
3. **关键问题**：转换时只复制了 `path`、`sm_name`、`sm_id`，但**没有复制 `interval_time`**
4. `multiFileMaterials` 计算属性返回的素材缺少 `interval_time` → 显示默认值0

## 修复方案

### 1. 保留 `interval_time` 属性（主要修复）

在 `deleteMaterialByClientKey` 方法中，当转换为单文件模式时保留 `interval_time`：

```javascript
// 修复前
if (this.selectedMaterial.multiFiles.length === 1) {
    const lastFile = this.selectedMaterial.multiFiles[0];
    this.$set(this.selectedMaterial, 'path', lastFile.path);
    this.$set(this.selectedMaterial, 'sm_name', lastFile.sm_name);
    this.$set(this.selectedMaterial, 'sm_id', lastFile.sm_id);
    this.$delete(this.selectedMaterial, 'multiFiles');
}

// 修复后
if (this.selectedMaterial.multiFiles.length === 1) {
    const lastFile = this.selectedMaterial.multiFiles[0];
    this.$set(this.selectedMaterial, 'path', lastFile.path);
    this.$set(this.selectedMaterial, 'sm_name', lastFile.sm_name);
    this.$set(this.selectedMaterial, 'sm_id', lastFile.sm_id);
    // 重要：保留interval_time属性
    if (lastFile.interval_time !== undefined) {
        this.$set(this.selectedMaterial, 'interval_time', lastFile.interval_time);
    }
    this.$delete(this.selectedMaterial, 'multiFiles');
}
```

### 2. 修复 `multiFileMaterials` 计算属性

确保单文件模式下返回正确的素材数据：

```javascript
// 修复前
if (this.selectedMaterial.multiFiles && Array.isArray(this.selectedMaterial.multiFiles) && this.selectedMaterial.multiFiles.length > 0) {
    return [...this.selectedMaterial.multiFiles];
}
// 直接使用fallback逻辑，可能丢失interval_time

// 修复后
if (this.selectedMaterial.multiFiles && Array.isArray(this.selectedMaterial.multiFiles) && this.selectedMaterial.multiFiles.length > 0) {
    return [...this.selectedMaterial.multiFiles];
}
// 如果是单文件模式，返回当前选中的素材本身（确保包含正确的interval_time）
if (this.selectedMaterial.type === 1 || this.selectedMaterial.type === 2) {
    return [this.selectedMaterial];
}
```

### 3. 移除不必要的 `$forceUpdate()` 调用

在 `TemplatePropertiesPanel.vue` 中：

```javascript
// 修复前
watch: {
    currentPageMaterials: {
        handler () {
            this.refreshTrigger++;
            this.$nextTick(() => {
                this.$forceUpdate(); // 移除这行
            });
        },
        deep: true,
        immediate: false
    }
}

// 修复后
watch: {
    currentPageMaterials: {
        handler () {
            this.refreshTrigger++;
            // 移除 $forceUpdate() 避免重置输入框状态
        },
        deep: true,
        immediate: false
    }
}
```

### 2. 优化 `updateIntervalTime` 方法

```javascript
// 修复前
updateIntervalTime (material, newTime) {
    this.$set(this.selectedMaterial.multiFiles[fileIndex], 'interval_time', newTime);
    this.$forceUpdate(); // 移除
    this.$nextTick(() => {
        this.$forceUpdate(); // 移除
    });
}

// 修复后
updateIntervalTime (material, newTime) {
    this.$set(this.selectedMaterial.multiFiles[fileIndex], 'interval_time', newTime);
    this.refreshTrigger++; // 使用refreshTrigger替代$forceUpdate
}
```

### 3. 依赖响应式系统

修复后的方案依赖Vue的响应式系统：
- 使用 `refreshTrigger` 触发计算属性重新计算
- 使用 `$set` 确保数据变化被正确检测
- 避免强制重新渲染整个组件

### 4. 确保新建素材包含 `interval_time` 属性

在创建新素材时添加默认的 `interval_time`：

```javascript
// 在 addImageArea、addVideoArea、confirmMaterial 等方法中
const newMaterial = {
  // ... 其他属性
  interval_time: 5, // 默认间隔时间5秒
};
```

## 修复效果

修复后：
1. **定时时间保持不变**：删除素材后，剩余素材的定时时间不会被重置
2. **数据完整性**：所有素材都包含 `interval_time` 属性
3. **更好的性能**：减少不必要的组件重新渲染
4. **更稳定的用户体验**：输入框状态得到保持

## 测试步骤

1. 打开模板编辑页面
2. 添加多个图片素材到同一个区域
3. 设置每个素材的定时时间为不同的值（如7秒、10秒等）
4. 删除其中一个素材
5. **验证**：剩余素材的定时时间应该保持原来设置的值，不会变为0

## 技术要点

1. **避免过度使用 `$forceUpdate()`**：只在必要时使用，优先依赖Vue的响应式系统
2. **使用 `refreshTrigger` 模式**：通过响应式变量触发计算属性重新计算
3. **保持数据一致性**：确保UI显示与底层数据保持一致

## 相关文件

- `web/src/components/TemplatePropertiesPanel.vue`
- `web/src/views/Template.vue`
