# 模板素材定时时间修复测试指南

## 测试目标

验证删除素材后，剩余素材的定时时间不会被重置为0的问题已经修复。

## 测试前准备

1. 确保已应用修复补丁
2. 重新启动开发服务器
3. 清除浏览器缓存

## 详细测试步骤

### 测试场景1：图片轮播定时时间保持

1. **创建新模板**
   - 打开模板编辑页面
   - 创建一个新的模板

2. **添加图片区域**
   - 从左侧面板拖拽一个"图片区域"到画布上

3. **添加多个图片素材**
   - 点击图片区域，在右侧属性面板点击"添加素材"
   - 选择3-4个不同的图片素材
   - 确认添加

4. **设置不同的定时时间**
   - 在右侧属性面板的"当前区域素材"列表中
   - 为第一个图片设置定时时间为 `7` 秒
   - 为第二个图片设置定时时间为 `10` 秒  
   - 为第三个图片设置定时时间为 `5` 秒
   - 为第四个图片设置定时时间为 `12` 秒

5. **验证设置成功**
   - 确认每个素材的定时时间显示正确
   - 观察画布上的轮播效果（如果有预览功能）

6. **删除中间的素材**
   - 删除第二个图片素材（原本设置为10秒的那个）
   - 观察剩余素材的定时时间

7. **验证修复效果**
   - ✅ 第一个图片的定时时间应该仍然是 `7` 秒
   - ✅ 第三个图片的定时时间应该仍然是 `5` 秒  
   - ✅ 第四个图片的定时时间应该仍然是 `12` 秒
   - ❌ 如果任何定时时间变为 `0`，说明问题未修复

### 测试场景2：连续删除操作

1. **继续上面的测试**
   - 现在应该还有3个图片素材

2. **再次删除素材**
   - 删除第一个素材（原本7秒的）

3. **验证剩余素材**
   - ✅ 剩余两个素材的定时时间应该保持 `5` 秒和 `12` 秒
   - ✅ 不应该有任何素材的定时时间变为 `0`

### 测试场景3：修改定时时间后删除

1. **修改现有素材的定时时间**
   - 将第一个素材的定时时间改为 `15` 秒
   - 将第二个素材的定时时间改为 `8` 秒

2. **删除一个素材**
   - 删除第一个素材

3. **验证修改后的时间保持**
   - ✅ 剩余素材的定时时间应该是 `8` 秒
   - ✅ 不应该重置为默认值

## 预期结果

### 修复成功的表现
- 删除素材后，剩余素材的定时时间保持用户设置的值
- 输入框中显示的数值与实际设置一致
- 轮播功能按照设置的时间间隔正常工作

### 问题未修复的表现
- 删除素材后，某些素材的定时时间变为 `0`
- 输入框显示 `0` 但实际轮播时间可能不是0
- 用户需要重新设置定时时间

## 额外测试点

1. **页面刷新测试**
   - 设置定时时间后刷新页面
   - 验证时间是否正确保存和加载

2. **切换页面测试**
   - 在多页模板中切换页面
   - 验证每页的定时时间设置独立保持

3. **保存加载测试**
   - 保存模板后重新加载
   - 验证定时时间设置是否持久化

## 故障排除

如果测试失败：

1. **检查浏览器控制台**
   - 查看是否有JavaScript错误
   - 检查Vue响应式更新相关的警告

2. **检查网络请求**
   - 验证保存操作是否成功
   - 检查数据是否正确发送到后端

3. **清除缓存重试**
   - 清除浏览器缓存
   - 重新启动开发服务器

## 报告问题

如果发现问题，请记录：
- 具体的操作步骤
- 预期结果 vs 实际结果
- 浏览器控制台的错误信息
- 复现问题的最小步骤
