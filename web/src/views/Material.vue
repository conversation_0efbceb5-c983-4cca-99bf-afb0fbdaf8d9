<template>
  <div class="material-page">
    <el-container class="material-layout">
      <!-- 左侧分组栏 -->
      <el-aside width="240px" class="group-aside">
        <div class="group-header">
          <h3>{{ $t('material.group.title') }}</h3>
          <el-button type="primary" icon="el-icon-plus" size="mini" circle @click="showGroupManagement"></el-button>
        </div>
        <el-menu :default-active="String(selectedGroupId)" class="group-menu" @select="handleGroupSelect">
          <el-menu-item index="0">
            <i class="el-icon-files"></i>
            <span slot="title">{{ $t('material.group.all') }}</span>
          </el-menu-item>
          <el-menu-item v-for="group in groupList" :key="group.id" :index="String(group.id)">
            <i class="el-icon-folder"></i>
            <span slot="title">{{ group.name }}</span>
          </el-menu-item>
        </el-menu>
      </el-aside>

      <!-- 主内容区 -->
      <el-main class="content-main">
        <!-- 顶部操作栏 -->
        <div class="action-bar">
          <div class="actions-left">
            <el-button type="primary" icon="el-icon-upload" @click="openBatchUpload">{{ $t('material.button.upload')
              }}</el-button>
            <el-button @click="toggleSelectAll">{{
              isAllSelected ? $t('public.cancel') : $t('material.button.selectAllCurrent')
              }}</el-button>
            <el-button type="danger" icon="el-icon-delete" :disabled="selectedMaterials.length === 0"
              @click="batchDelete">{{ $t('material.button.batchDelete') }}</el-button>
          </div>
          <div class="actions-right">
            <el-input v-model="searchKeyword" :placeholder="$t('public.search')" clearable @clear="getList"
              @keyup.enter.native="getList" class="search-input">
              <el-button slot="append" icon="el-icon-search" @click="getList"></el-button>
            </el-input>
            <el-radio-group v-model="viewMode" size="small">
              <el-radio-button label="grid">
                <i class="el-icon-s-grid"></i>
              </el-radio-button>
              <el-radio-button label="list">
                <i class="el-icon-s-fold"></i>
              </el-radio-button>
            </el-radio-group>
          </div>
        </div>

        <!-- 素材展示区 -->
        <div v-loading="isLoading" class="material-grid-container">
          <el-row :gutter="20">
            <el-col v-for="item in dataList" :key="item.id" :xs="12" :sm="8" :md="6" :lg="4" :xl="4">
              <el-card shadow="hover" class="material-card" :class="{ selected: isSelected(item.id) }"
                @click.native="toggleSelection(item.id)">
                <div class="card-preview">
                  <el-image v-if="item.type === 1" :src="imageUrl + item.path" fit="cover" lazy></el-image>
                  <video v-else-if="item.type === 2" :src="imageUrl + item.path" muted></video>
                  <div v-else class="file-icon-wrapper">
                    <i :class="getFileIcon(item.path)"></i>
                  </div>
                </div>
                <div class="card-info">
                  <p class="card-name" :title="item.name">{{ item.name }}</p>
                  <div class="card-details">
                    <span :class="['type-tag', `type-${item.type}`]">{{ getTypeName(item.type) }}</span>
                    <span>{{ formatFileSize(item.size) }}</span>
                    <span v-if="item.source_width && item.source_height">
                      {{ item.source_width }} x {{ item.source_height }}
                    </span>
                  </div>
                  <time class="card-time">{{
                    $formatTimeStamp(item.created_at, "YYYY-MM-DD HH:mm")
                    }}</time>
                </div>
                <div class="card-actions">
                  <el-dropdown trigger="click" @command="handleCommand($event, item)">
                    <span class="el-dropdown-link">
                      <i class="el-icon-more"></i>
                    </span>
                    <el-dropdown-menu slot="dropdown">
                      <el-dropdown-item command="edit" icon="el-icon-edit">{{ $t('public.edit') }}</el-dropdown-item>
                      <el-dropdown-item command="delete" icon="el-icon-delete">{{ $t('public.delete')
                      }}</el-dropdown-item>
                    </el-dropdown-menu>
                  </el-dropdown>
                </div>
              </el-card>
            </el-col>
          </el-row>
          <el-empty v-if="!isLoading && dataList.length === 0"
            :description="$t('material.empty.noMaterial')"></el-empty>
        </div>

        <!-- 分页 -->
        <el-pagination v-if="total > 0" background layout="total, sizes, prev, pager, next, jumper" :total="total"
          :page-size.sync="pageSize" :current-page.sync="pageNum" @size-change="getList" @current-change="getList"
          class="pagination-footer">
        </el-pagination>
      </el-main>
    </el-container>

    <!-- 弹窗 -->
    <MaterialUploadDialog :visible.sync="showBatchUpload" :group-id="Number(selectedGroupId)"
      @upload-success="handleUploadSuccess" />
    <MaterialGroupDialog :visible.sync="showGroupDialog" @group-changed="handleGroupChanged" />
    <!-- 编辑弹窗 -->
    <el-dialog :title="$t('public.edit')" :visible.sync="showEditDialog" width="500px">
      <el-form :model="editForm" label-width="80px">
        <el-form-item :label="$t('material.form.name')">
          <el-input v-model="editForm.name"></el-input>
        </el-form-item>
        <el-form-item :label="$t('material.form.group')">
          <el-select v-model="editForm.group_id" :placeholder="$t('material.form.groupPlaceholder')">
            <el-option :label="$t('material.form.noGroup')" :value="0"></el-option>
            <el-option v-for="group in groupList" :key="group.id" :label="group.name" :value="group.id"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="showEditDialog = false">{{ $t('public.cancel') }}</el-button>
        <el-button type="primary" @click="saveEdit">{{ $t('public.confirm') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getList, edit, del, batchDelete } from "@/api/material.js";
import { getAllMaterialGroups } from "@/api/material.js";
import MaterialUploadDialog from "@/components/material-upload-dialog.vue";
import MaterialGroupDialog from "@/components/material-group-dialog.vue";

export default {
  name: "Material",
  components: { MaterialUploadDialog, MaterialGroupDialog },
  data () {
    return {
      isLoading: false,
      dataList: [],
      groupList: [],
      selectedGroupId: "0",
      searchKeyword: "",
      viewMode: "grid", // 'grid' or 'list'

      pageNum: 1,
      pageSize: 12,
      total: 0,

      selectedMaterials: [],

      showBatchUpload: false,
      showGroupDialog: false,
      showEditDialog: false,

      editForm: {
        id: null,
        name: "",
        group_id: 0,
      },

      imageUrl: process.env.VUE_APP_BASE_API + "assets/media/",
    };
  },
  computed: {
    isAllSelected () {
      if (this.dataList.length === 0) {
        return false;
      }
      const currentPageIds = this.dataList.map(item => item.id);
      return currentPageIds.every(id => this.selectedMaterials.includes(id));
    },
  },
  created () {
    this.loadGroupList();
    this.getList();
  },
  methods: {
    toggleSelectAll () {
      const currentPageIds = this.dataList.map(item => item.id);
      if (this.isAllSelected) {
        // Deselect all on current page
        this.selectedMaterials = this.selectedMaterials.filter(
          id => !currentPageIds.includes(id)
        );
      } else {
        // Select all on current page
        const newSelection = new Set([
          ...this.selectedMaterials,
          ...currentPageIds,
        ]);
        this.selectedMaterials = Array.from(newSelection);
      }
    },
    async loadGroupList () {
      try {
        const res = await getAllMaterialGroups();
        if (res.code === 0) {
          this.groupList = res.data || [];
        }
      } catch (error) {
        console.error("Failed to load material groups:", error);
      }
    },
    async getList () {
      this.isLoading = true;
      try {
        const params = {
          page: this.pageNum,
          pageSize: this.pageSize,
          name: this.searchKeyword,
          group_id: this.selectedGroupId === "0" ? null : this.selectedGroupId,
        };
        const res = await getList(params);
        if (res.code === 0) {
          this.dataList = res.data.data;
          this.total = res.data.total;
        }
      } catch (error) {
        console.error("Failed to get material list:", error);
      } finally {
        this.isLoading = false;
      }
    },
    handleGroupSelect (index) {
      this.selectedGroupId = index;
      this.pageNum = 1;
      this.getList();
    },
    handleUploadSuccess () {
      this.getList();
    },
    handleGroupChanged () {
      this.loadGroupList();
      this.getList();
    },
    openBatchUpload () {
      this.showBatchUpload = true;
    },
    showGroupManagement () {
      this.showGroupDialog = true;
    },
    toggleSelection (id) {
      const index = this.selectedMaterials.indexOf(id);
      if (index > -1) {
        this.selectedMaterials.splice(index, 1);
      } else {
        this.selectedMaterials.push(id);
      }
    },
    isSelected (id) {
      return this.selectedMaterials.includes(id);
    },
    batchDelete () {
      this.$confirm(this.$t('material.confirm.batchDelete', { count: this.selectedMaterials.length }), this.$t('public.confirm'), {
        confirmButtonText: this.$t('public.confirm'),
        cancelButtonText: this.$t('public.cancel'),
        type: 'warning'
      }).then(async () => {
        try {
          await batchDelete({ ids: this.selectedMaterials });
          this.$message.success('批量删除成功');
          this.selectedMaterials = [];
          this.getList();
        } catch (error) {
          this.$message.error('批量删除失败');
        }
      }).catch(() => { });
    },
    handleCommand (command, item) {
      if (command === 'edit') {
        this.editMaterial(item);
      } else if (command === 'delete') {
        this.deleteMaterial(item.id);
      }
    },
    editMaterial (item) {
      this.editForm.id = item.id;
      this.editForm.name = item.name;
      this.editForm.group_id = item.group_id || 0;
      this.showEditDialog = true;
    },
    async saveEdit () {
      try {
        await edit(this.editForm, this.editForm.id);
        this.$message.success('编辑成功');
        this.showEditDialog = false;
        this.getList();
      } catch (error) {
        this.$message.error('编辑失败');
      }
    },
    deleteMaterial (id) {
      this.$confirm(this.$t('material.confirm.delete'), this.$t('public.confirm'), {
        confirmButtonText: this.$t('public.confirm'),
        cancelButtonText: this.$t('public.cancel'),
        type: 'warning'
      }).then(async () => {
        try {
          await del(id);
          this.$message.success('删除成功');
          this.getList();
        } catch (error) {
          this.$message.error('删除失败');
        }
      }).catch(() => { });
    },
    getFileIcon (path) {
      if (!path) return "el-icon-document";
      const ext = path.split(".").pop().toLowerCase();
      const iconMap = {
        pdf: "el-icon-tickets",
        doc: "el-icon-document",
        docx: "el-icon-document",
        xls: "el-icon-data-analysis",
        xlsx: "el-icon-data-analysis",
        ppt: "el-icon-monitor",
        pptx: "el-icon-monitor",
        txt: "el-icon-document",
        zip: "el-icon-folder-opened",
        rar: "el-icon-folder-opened",
      };
      return iconMap[ext] || "el-icon-document";
    },
    getTypeName (type) {
      const types = {
        1: "图片",
        2: "视频",
        3: "PDF",
        4: "PPT",
        5: "Word",
        6: "Excel",
        7: "TXT",
        8: "其他",
      };
      return types[type] || "未知";
    },
    formatFileSize (bytes) {
      if (bytes === 0) return "0 B";
      const k = 1024;
      const sizes = ["B", "KB", "MB", "GB", "TB"];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
    },
  },
};
</script>

<style lang="scss" scoped>
.material-page {
  height: calc(100vh - 84px);
  background-color: #f0f2f5;


}

.material-layout {
  height: 100%;
}

.group-aside {
  background: #fff;
  border-right: 1px solid #e6e6e6;
  display: flex;
  flex-direction: column;

  .group-header {
    padding: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #e6e6e6;

    h3 {
      margin: 0;
      font-size: 16px;
    }
  }

  .group-menu {
    border-right: none;
    flex: 1;
    overflow-y: auto;
  }
}

.content-main {
  padding: 20px;
  display: flex;
  flex-direction: column;
}

.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  .actions-right {
    display: flex;
    align-items: center;
    gap: 15px;
  }

  .search-input {
    width: 250px;
  }
}

.material-grid-container {
  flex: 1;
  overflow-y: auto;
  padding-right: 10px; // for scrollbar
}

.material-card {
  margin-bottom: 8px;
  cursor: pointer;
  position: relative;
  border: 2px solid transparent;
  transition: all 0.2s ease-in-out;

  &.selected {
    border-color: #409eff;
    box-shadow: 0 2px 12px 0 rgba(64, 158, 255, 0.5);
  }

  .card-preview {
    height: 120px;
    background-color: #f5f7fa;
    display: flex;
    align-items: center;
    justify-content: center;

    .el-image,
    video {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .file-icon-wrapper {
      font-size: 48px;
      color: #c0c4cc;
    }
  }

  .card-info {
    padding: 8px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    min-height: 90px; // 固定信息区高度

    .card-name {
      font-size: 14px;
      color: #303133;
      margin: 0 0 8px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .card-time {
      font-size: 12px;
      color: #909399;
      margin-top: auto; // 将时间推到底部
    }

    .card-details {
      font-size: 12px;
      color: #606266;
      margin-bottom: 8px;
      display: flex;
      flex-wrap: wrap; // 允许换行
      align-items: center;
      gap: 8px;

      .type-tag {
        padding: 2px 6px;
        border-radius: 4px;
        color: #fff;
        font-weight: 500;

        &.type-1 {
          background-color: #67c23a;
        }

        // 图片
        &.type-2 {
          background-color: #409eff;
        }

        // 视频
        &.type-3 {
          background-color: #f56c6c;
        }

        // PDF
        &.type-4 {
          background-color: #e6a23c;
        }

        // PPT
        &.type-5 {
          background-color: #409eff;
        }

        // Word
        &.type-6 {
          background-color: #67c23a;
        }

        // Excel
        &.type-7 {
          background-color: #909399;
        }

        // TXT
        &.type-8 {
          background-color: #909399;
        }

        // 其他
      }
    }
  }

  .card-actions {
    position: absolute;
    top: 10px;
    right: 10px;
    display: none;

    .el-dropdown-link {
      cursor: pointer;
      color: #909399;
      font-size: 18px;
      padding: 5px;
      border-radius: 50%;

      &:hover {
        background-color: #f0f2f5;
      }
    }
  }

  &:hover .card-actions {
    display: block;
  }
}

.pagination-footer {
  margin-top: 20px;
  text-align: right;
}
</style>
<style lang="scss">
.material-page {
  .el-card__body {
    padding: 8px;

  }
}
</style>