<template>
    <div class="login-container">
        <!-- 右上角添加语言切换按钮 -->
        <div class="language-switcher">
            <el-button @click="switchLanguage">{{ $t('login.switchLang') }}</el-button>
        </div>
        <div class="flexBox">
            <div>
                <el-form ref="loginForm" :model="loginForm" :rules="loginRules" class="login-form" auto-complete="on"
                    label-position="left" @submit.native.prevent="handleLogin">
                    <div class="flex">
                        <div class="image-container">
                            <el-carousel :interval="5000" arrow="always" height="400px" class="carousel">
                                <el-carousel-item>
                                    <el-image :z-index="1" :src="require('@/assets/login/bg2.png')" fit="cover" />
                                </el-carousel-item>
                            </el-carousel>
                        </div>
                        <div class="right-container">
                            <div class="form-container">
                                <div class="title">{{ $t('login.esopBackend') }}</div>
                                <el-form-item prop="username">
                                    <span class="svg-container">
                                        <el-image style="width: 16px;"
                                            :src="require('@/assets/login/user.png')"></el-image>
                                    </span>
                                    <el-input ref="username" v-model="loginForm.username"
                                        :placeholder="$t('login.enterUsername')" name="username" type="text"
                                        tabindex="1" auto-complete="on" />
                                </el-form-item>

                                <el-form-item prop="password">
                                    <span class="svg-container">
                                        <el-image style="width: 16px;"
                                            :src="require('@/assets/login/psd.png')"></el-image>
                                    </span>
                                    <el-input :key="passwordType" ref="password" v-model="loginForm.password"
                                        :type="passwordType" :placeholder="$t('login.enterPassword')" name="password"
                                        tabindex="2" auto-complete="off" show-password />
                                </el-form-item>

                                <div style="display: flex;">
                                    <el-checkbox v-model="isRemenberPw">{{ $t('login.rememberPassword') }}</el-checkbox>
                                </div>

                                <div class="button-container">
                                    <el-button :loading="loading" type="primary" @click.native.prevent="handleLogin">{{
                                        $t('login.login') }}</el-button>
                                </div>
                            </div>
                        </div>
                    </div>
                </el-form>
            </div>
            <!-- <div class="info">
                <div class="info-item">
                    <div style="margin: 0 20px">版权</div>
                </div>
            </div> -->
        </div>
    </div>
</template>

<script>
export default {
    data () {
        const validateUsername = (rule, value, callback) => {
            if (value.length <= 0) {
                callback(new Error(this.$i18n.t("login.enterUsername")))
            } else {
                callback()
            }
        }
        const validatePassword = (rule, value, callback) => {
            if (value.length <= 0) {
                callback(new Error(this.$i18n.t("login.enterPassword")))
            } else {
                callback()
            }
        }
        return {
            imageUrl: this.$imageUrl,
            loginForm: {
                username: '',
                password: '',
                code: '',
                codeValue: ''
            },
            loginRules: {
                username: [{ required: true, trigger: 'change', validator: validateUsername }],
                password: [{ required: true, trigger: 'change', validator: validatePassword }],
            },
            loading: false,
            passwordType: 'password',
            redirect: undefined,
            isRemenberPw: false,
            codeImage: '',
            banner: [],
            companyList: [],
            dictList: []
        }
    },
    methods: {
        // 原 methods 内容
        //  switchLanguage() {
        //     this.$i18n.locale = this.$i18n.locale === 'en' ? 'zh' : 'en';
        // },
        switchLanguage () {
            // 切换语言
            this.$i18n.locale = this.$i18n.locale === 'en' ? 'zh' : 'en';

            // 保存语言设置到localStorage
            localStorage.setItem('appLanguage', this.$i18n.locale);
        },
        // 登录
        handleLogin (flag) {
            this.$refs.loginForm.validate(valid => {
                if (valid) {
                    this.loading = true
                    this.$store.dispatch('user/login', this.loginForm).then(async (user) => {
                        this.$message.success(this.$t('login.loginSuccess'));
                        this.$router.push({ path: '/EquipmentCenter' })
                        this.saveUnAndPw(user)
                        this.loading = false
                    }).catch(() => {
                        this.loading = false
                    })
                } else {
                    console.log('error submit!!')
                    return false
                }
            })
        },

        // 处理账号密码的储存
        saveUnAndPw (user) {
            localStorage.setItem('greemall_login', JSON.stringify(user.Admin))

            if (this.isRemenberPw) {
                this.setCookie(this.loginForm.username, this.loginForm.password, 7)
            }
        },

        //设置cookie
        setCookie (c_name, c_pwd, exdays) {
            var exdate = new Date() //获取时间
            exdate.setTime(exdate.getTime() + 24 * 60 * 60 * 1000 * exdays) //保存的天数
            //字符串拼接cookie
            window.document.cookie = 'greemall_username' + '=' + c_name + ';path=/;expires=' + exdate.toGMTString()
            window.document.cookie = 'greemall_password' + '=' + c_pwd + ';path=/;expires=' + exdate.toGMTString()
        },

        //读取cookie
        getCookie: function () {
            if (document.cookie.length > 0) {
                var arr = document.cookie.split('; ') //这里显示的格式需要切割一下自己可输出看下
                for (var i = 0; i < arr.length; i++) {
                    var arr2 = arr[i].split('=') //再次切割
                    //判断查找相对应的值
                    if (arr2[0] == 'greemall_username') {
                        this.loginForm.username = arr2[1] //保存到保存数据的地方
                    } else if (arr2[0] == 'greemall_password') {
                        this.loginForm.password = arr2[1]
                    }
                }
            }
        },

        //清除cookie
        clearCookie: function () {
            this.setCookie('', '', -1) //修改2值都为空，天数为负1天就好了
        },

        toEnterApply () {
            this.$router.push({ name: 'enterApply' })
        },

        toRetrievePassword () {
            this.$router.push({ name: 'retrievePassword' })
        }
    },
};
</script>

<style lang="scss">
/* 修复input 背景不协调 和光标变色 */
/* Detail see https://github.com/PanJiaChen/vue-element-admin/pull/927 */

$bg: #283443;
$light_gray: #fff;
$cursor: #fff;
$back: #333;

.info-item .el-link--inner {
    color: #ffffff;
}

/* reset element-ui css */
.login-container {
    background: url('~@/assets/login/background.png') center center;
    background-size: cover;
    height: 100vh;

    .el-input {
        display: inline-block;
        height: 47px;

        input {
            background: transparent;
            border: 0px;
            -webkit-appearance: none;
            border-radius: 0px;
            padding: 12px 10px;
            color: $back;
            height: 47px;
            caret-color: $back;
            border-bottom: none !important;

            &:-webkit-autofill {
                box-shadow: 0 0 0px 1000px $cursor inset !important;
                -webkit-text-fill-color: $back !important;
            }
        }
    }

    .el-carousel__arrow--left,
    .el-carousel__arrow--right {
        display: none;
    }

    .carousel {
        border-top-left-radius: 15px;
        border-bottom-left-radius: 15px;
        overflow: hidden;
    }

    .el-form-item {
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 5px;
        color: #454545;
        margin-bottom: 22px !important;
    }

    .el-form-item__error {
        left: 30px;
    }

    .el-form-item__content {
        display: flex;
        align-items: center;
        line-height: 0;
    }

    .show-pwd {
        line-height: 40px;
    }

    .el-checkbox__input.is-checked+.el-checkbox__label {
        color: #4684f4db;
    }

    .checkbox {
        position: relative;
        display: flex;
        align-items: center;
        margin-left: 1px;

        .check-yes {
            position: relative;
            line-height: 0;
        }

        .yes {
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
        }

        .el-checkbox {
            margin: 0 !important;
        }
    }

    /* 可以设置不同的进入和离开动画 */
    /* 设置持续时间和动画函数 */
    .slide-fade-enter-active {
        transition: all 0.3s ease;
    }

    .slide-fade-leave-active {
        transition: all 0.8s cubic-bezier(1, 0.5, 0.8, 1);
    }

    .slide-fade-leave-active {
        transform: translateX(10px);
        opacity: 0;
    }

    .wei {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        margin-top: 30px;
        text-align: center;
        text-align-last: center;

        &-item-tip {
            height: 19px;
            font-size: 20px;
            margin-top: 17px;
        }

        &-item-text {
            height: 12px;
            font-size: 14px;
            margin-top: 17px;
            color: #999;
        }
    }
}
</style>

<style lang="scss" scoped>
$bg: #2d3a4b;
$dark_gray: #889aa4;
$light_gray: #eee;

.flex {
    display: flex;
    align-items: center;
}

.info {
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: center;
    width: 1080px;
    margin-top: 50px;
    line-height: 30px;
    text-align: center;
    text-align-last: center;
    color: #fff;
    z-index: 999;
}

.link_gs {
    font-size: 14px;
}

::v-deep .link_gs .el-link--inner {
    color: #fff;
    font-weight: initial;
}

.info-item {
    display: flex;
    align-items: center;
    justify-content: center;
}

.title-item {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin: 0 30px;
    cursor: pointer;
    font-size: 22px;
    color: #666666;
    line-height: 40px;
    border-bottom: 3px solid #ffffff;
}

.acitve {
    font-weight: bold;
    color: #4684f4;
    border-bottom: 3px solid #4684f4;
}

.flexBox {
    display: flex;
    padding-top: calc(50vh - 200px);
    justify-content: space-between;
    flex-direction: column;
    align-items: center;
}

.input-box {
    position: relative;
    width: 360px;
    height: 50px;
    margin-bottom: 25px;
    background: #ffffff;
    border: 1px solid #e6e6e6 !important;
    border-radius: 4px;
}

.input-box:last-child {
    margin-bottom: 10px;
}

.flex {
    display: flex;
    flex-direction: row;
}

.empty-height {
    font-size: 30px;
    font-weight: bolder;
    height: 46px;
    margin: 15px 0;
    color: #fff;
}

.empty-height2 {
    font-size: 30px;
    font-weight: bolder;
    height: 42px;
    margin: 15px 0;
    color: #fff;
}

.fiexlay {
    width: auto;
    height: 46px;
    display: flex;
    align-items: center;
}

.logo {
    height: 42px;
    margin-right: 5px;
}

.el-carousel__item:nth-child(2n) {
    background-color: #99a9bf;
}

.el-carousel__item:nth-child(2n + 1) {
    background-color: #d3dce6;
}

.login-container {
    min-height: 100%;
    width: 100%;
    height: 100vh;
    background-color: $bg;
    overflow: hidden;

    .login-form {
        position: relative;
        width: 1920px;
        max-width: 100%;
        height: 100%;

        display: flex;
        flex-direction: column;
        align-items: center;
        box-sizing: border-box;
    }

    .image-container {
        width: 700px;
        height: 400px;
        //  overflow: hidden;
    }

    .right-container {
        width: 440px;
        height: 400px;

        .form-container {
            height: 400px;
            padding: 40px 30px 27px 40px;
            background: #fff;
            border-radius: 0 15px 15px 0;
            box-sizing: border-box;

            .title {
                justify-content: center;
                letter-spacing: 4px;
                text-align: center;
                margin-bottom: 60px;
                font-size: 24px;
                font-weight: 600;
            }

            .el-form-item {
                border: 1px solid #f1f1f1;
            }
        }
    }

    .tips {
        font-size: 14px;
        color: #fff;
        margin-bottom: 10px;

        span {
            &:first-of-type {
                margin-right: 16px;
            }
        }
    }

    .svg-container {
        margin: 0 0 0 14px;
        // padding: 6px 5px 6px 5px;
        color: #33aef7;
        vertical-align: middle;
        width: 30px;
        display: inline-block;
    }

    .title-container {
        border-radius: 15px 15px 0 0;
        overflow: hidden;

        img {
            width: 100%;
            display: block;
        }
    }

    .show-pwd {
        position: absolute;
        right: 30px;
        top: 7px;
        font-size: 16px;
        color: $dark_gray;
        cursor: pointer;
        user-select: none;
    }

    .code {
        position: absolute;
        right: 10px;
        top: 8px;
        z-index: 99;
        cursor: pointer;

        img {
            height: 30px;
        }
    }

    .code2 {
        position: absolute;
        right: 0;
        top: -1px;

        .el-button {
            height: 50px;
        }
    }

    .button-container {
        text-align: center;
        margin-top: 20px;

        button {
            font-size: 16px;
            width: 100%;
            height: 45px;
            border-radius: 4px;
            background: #4684f4;
            box-shadow: 2px 3px 8px 0px #4684f46b;
        }
    }

    .bottom-container {
        display: flex;
        justify-content: space-between;
        margin-top: 20px;
    }
}

.float_right {
    position: absolute;
    right: 0;
    font-size: 14px;
    cursor: pointer;
}

::v-deep .el-form-item__error {
    color: #f56c6c;
    font-size: 12px;
    line-height: 1;
    padding-top: 6px;
    position: absolute;
    top: 100%;
    left: 0;
}

.el-link.el-link--default:hover {
    color: #4684f4;
}

@media only screen and (max-width: 600px) {
    .image-container {
        display: none;
    }

    .form-container {
        border-radius: 15px !important;
    }
}

.language-switcher {
    position: absolute;
    top: 20px;
    right: 20px;
}
</style>