<template>
  <div class="operationlog-container">
    <el-form :model="form" ref="form" label-width="120px" label-position="left" class="demo-ruleForm">
      <el-row :gutter="20">
        <el-col :span="4">
          <el-form-item :label="$t('operationLog.form.operatorName')" prop="admin_name">
            <el-input v-model="form.admin_name"
              :placeholder="$t('operationLog.form.operatorNamePlaceholder')"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item :label="$t('operationLog.form.operatorAccount')" prop="admin_account">
            <el-input v-model="form.admin_account"
              :placeholder="$t('operationLog.form.operatorAccountPlaceholder')"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item :label="$t('operationLog.form.operationTime')" prop="date">
            <el-date-picker v-model="form.date" value-format="timestamp" type="daterange" range-separator="至"
              :start-placeholder="$t('public.startDate')" :end-placeholder="$t('public.endDate')">
            </el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row type="flex" justify="end">
        <el-col :span="4">
          <el-button @click="resetForm()" size="mini">{{ $t('public.reset') }}</el-button>
          <el-button type="primary" @click="searchForm()" size="mini">{{ $t('public.search') }}</el-button>
        </el-col>
      </el-row>
    </el-form>
    <!-- <div class="flex">
        <el-button type="primary" icon="el-icon-plus" @click="add()" size="mini">新建账号</el-button>
      </div> -->

    <div style="height: 60vh;background-color: #ccc;margin: 10px 0;">
      <el-table v-loading="isLoading" :data="dataList" style="width: 100%;" border height="100%">
        <el-table-column prop="num" :label="$t('operationLog.table.num')" width="120" align="center">
          <template slot-scope="scope">
            {{ scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="admin_name" :label="$t('operationLog.table.operatorName')"
          align="center"></el-table-column>
        <el-table-column prop="admin_account" :label="$t('operationLog.table.operatorAccount')"
          align="center"></el-table-column>
        <el-table-column prop="action" :label="$t('operationLog.table.action')" align="center"></el-table-column>
        <el-table-column prop="module" :label="$t('operationLog.table.module')" align="center"></el-table-column>
        <el-table-column prop="created_at" :label="$t('operationLog.table.operationTime')" align="center">
          <template slot-scope="scope">
            {{ $formatTimeStamp(scope.row.created_at) }}
          </template>
        </el-table-column>
      </el-table>
    </div>

    <el-row :gutter="20" type="flex" justify="end">
      <el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentChange"
        :current-page.sync="pageNum" :page-sizes="[10, 20, 50]" :page-size="pageSize" layout="total, prev, pager, next"
        :total="total">
      </el-pagination>
    </el-row>
  </div>
</template>

<script>
import { getList } from '@/api/operationLog.js'
export default {
  data () {
    return {
      dataList: [],
      pageNum: 1,
      pageSize: 10,
      total: 0,
      form: {
        admin_name: '',
        admin_account: '',
        date: []
      },
      isLoading: false
    }
  },
  created () {
    this.getList()
  },
  methods: {
    getList () {
      this.isLoading = true
      getList({
        page: this.pageNum,
        pageSize: this.pageSize,
        admin_name: this.form.admin_name,
        admin_account: this.form.admin_account,
        created_at_start: this.form.date.length > 0 ? this.form.date[0] / 1000 : '',
        created_at_end: this.form.date.length > 0 ? this.form.date[1] / 1000 : '',
      }).then(res => {
        if (res.code == 0) {
          this.dataList = res.data.data
          this.total = res.data.total
          this.isLoading = false
        }
      })
    },
    //搜索
    searchForm () {
      this.getList()
    },
    //重置
    resetForm () {
      this.page = 1
      this.$refs.form.resetFields();
      this.getList()
    },
    handleSizeChange (val) {
      this.pageNum = 1
      this.pageSize = val
      this.getList()
    },
    handleCurrentChange (val) {
      this.pageNum = val
      this.getList()
    }
  },
};
</script>

<style scoped>
.flex {
  display: flex;
}

.img {
  width: 40px;
  height: 40px;
}
</style>