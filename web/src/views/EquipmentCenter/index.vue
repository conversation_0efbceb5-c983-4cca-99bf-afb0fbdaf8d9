<template>
  <div class="equipment-center-page">
    <el-container class="equipment-layout">
      <!-- 左侧设备树 -->
      <el-aside width="280px" class="tree-aside">
        <div class="tree-header">
          <h3>{{ $t('equipmentCenter.tree.title') }}</h3>
          <el-button type="primary" icon="el-icon-plus" size="mini" circle @click="addGroupDialog"></el-button>
        </div>
        <div class="tree-search">
          <el-input :placeholder="$t('equipmentCenter.tree.searchPlaceholder')" v-model="filterText" clearable
            prefix-icon="el-icon-search" />
        </div>
        <div class="tree-container">
          <el-tree class="equipment-tree" :data="equipmentData" :props="defaultProps" :filter-node-method="filterNode"
            ref="tree" node-key="label" @node-click="handleNodeClick" :render-content="renderContent"
            :highlight-current="true" :empty-text="$t('equipmentCenter.tree.noData')" @node-expand="handleNodeExpand"
            @node-collapse="handleNodeCollapse" :expand-on-click-node="false" :default-expanded-keys="expandedKeys"
            :current-node-key="currentNodeKey">
          </el-tree>
        </div>
      </el-aside>

      <!-- 右侧主内容区 -->
      <el-main class="content-main">
        <!-- 洋葱路径（面包屑导航） -->
        <el-breadcrumb v-if="breadcrumbPaths.length > 0" separator="/" class="equipment-breadcrumb">
          <el-breadcrumb-item v-for="(path, index) in breadcrumbPaths" :key="index" :to="path.to || null">
            {{ path.label == 'Ungrouped' ? $t('equipmentCenter.tree.null') : path.label }}
          </el-breadcrumb-item>
        </el-breadcrumb>

        <!-- 未选择任何节点时的提示 -->
        <div v-if="!selectedDevice" class="empty-state">
          <el-empty :description="$t('equipmentCenter.tree.selectTip') || '请选择左侧设备或分组查看详情'" :image-size="150">
          </el-empty>
        </div>

        <!-- 详情内容 -->
        <div v-else>
          <!-- 分组详情视图 -->
          <div v-if="!selectedDevice.isClient" class="group-detail-view">
            <div class="group-header-info">
              <div class="group-title">
                <i class="el-icon-folder-opened"></i>
                <h2>{{ selectedDevice.label == 'Ungrouped' ? $t('equipmentCenter.tree.null') : selectedDevice.label
                }}</h2>
              </div>
              <div class="header-actions">
                <el-button type="primary" size="small" icon="el-icon-edit" @click="editGroupDialog(selectedDevice)">
                  {{ $t('equipmentCenter.button.editGroup') || '编辑分组' }}
                </el-button>
                <el-button type="danger" size="small" icon="el-icon-delete" @click="deleteGroupDialog(selectedDevice)">
                  {{ $t('equipmentCenter.button.deleteGroup') || '删除分组' }}
                </el-button>
              </div>
            </div>

            <!-- 统计信息 -->
            <el-row :gutter="20" class="stats-cards">
              <el-col :span="8">
                <div class="stat-card">
                  <div class="stat-content">
                    <div class="stat-label">{{ $t('equipmentCenter.tree.total') || '设备总数' }}</div>
                    <div class="stat-number">{{ selectedDevice.total || 0 }}</div>
                  </div>
                  <div class="stat-icon-wrapper total">
                    <i class="el-icon-s-grid"></i>
                  </div>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="stat-card">
                  <div class="stat-content">
                    <div class="stat-label">{{ $t('equipmentCenter.tree.online') || '在线设备' }}</div>
                    <div class="stat-number">{{ selectedDevice.online || 0 }}</div>
                  </div>
                  <div class="stat-icon-wrapper online">
                    <i class="el-icon-success"></i>
                  </div>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="stat-card">
                  <div class="stat-content">
                    <div class="stat-label">{{ $t('equipmentCenter.tree.offline') || '离线设备' }}</div>
                    <div class="stat-number">{{ (selectedDevice.total || 0) - (selectedDevice.online || 0) }}</div>
                  </div>
                  <div class="stat-icon-wrapper offline">
                    <i class="el-icon-error"></i>
                  </div>
                </div>
              </el-col>
            </el-row>

            <!-- 设备列表操作栏 -->
            <div class="action-bar">
              <el-button @click="selectAllOffline">{{ $t('equipmentCenter.actionBar.selectAllOffline')
              }}</el-button>
              <el-button @click="selectAllOnline">{{ $t('equipmentCenter.actionBar.selectAllOnline') }}</el-button>
              <el-tooltip class="item" effect="dark" :content="$t('equipmentCenter.actionBar.batchUploadTooltip')"
                placement="top-start">

                <el-button type="success" icon="el-icon-upload" @click="handleBatchUpload">{{
                  $t('equipmentCenter.actionBar.batchUpload') }}</el-button>
              </el-tooltip>
              <el-button type="danger" icon="el-icon-delete" @click="handleBatchDeleteFiles">{{
                $t('equipmentCenter.actionBar.batchDeleteFiles') }}</el-button>
              <el-button type="primary" icon="el-icon-s-promotion" @click="batchPush"
                :disabled="multipleSelection.length === 0">{{ $t('equipmentCenter.actionBar.batchPush')
                }}</el-button>
              <el-button type="danger" icon="el-icon-refresh-right" @click="batchReboot"
                :disabled="multipleSelection.length === 0">{{ $t('equipmentCenter.actionBar.batchReboot')
                }}</el-button>
              <el-button type="danger" icon="el-icon-switch-button" @click="batchShutdown"
                :disabled="multipleSelection.length === 0">{{ $t('equipmentCenter.actionBar.batchShutdown')
                }}</el-button>
              <el-button type="warning" icon="el-icon-time" @click="batchSchedulePower"
                :disabled="multipleSelection.length === 0">{{ $t('equipmentCenter.actionBar.batchSchedulePower')
                }}</el-button>
              <el-button type="info" icon="el-icon-headset" @click="batchAdjustVolume"
                :disabled="multipleSelection.length === 0">{{ $t('equipmentCenter.actionBar.adjustVolume')
                }}</el-button>

            </div>

            <!-- 设备列表 -->
            <el-table :data="selectedDevice.children" @selection-change="handleSelectionChange"
              @row-click="handleDeviceClickInGroup" class="group-device-table" v-loading="isLoading" ref="deviceTable">
              <el-table-column type="selection" width="55"></el-table-column>
              <el-table-column prop="alias_name" :label="$t('equipmentCenter.table.alias_name')" width="180"
                show-overflow-tooltip></el-table-column>
              <el-table-column prop="mac_address" :label="$t('equipmentCenter.form.mac')" width="180"
                show-overflow-tooltip></el-table-column>
              <el-table-column :label="$t('equipmentCenter.table.connectionStatus')" width="100" align="center">
                <template slot-scope="scope">
                  <el-tag :type="scope.row.isOnline ? 'success' : 'danger'" size="small">
                    {{ scope.row.isOnline ? $t('equipmentCenter.table.online') : $t('equipmentCenter.table.offline') }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column :label="$t('equipmentCenter.table.pushStatus')" show-overflow-tooltip>
                <template slot-scope="scope">
                  <el-popover placement="top-start" width="300" trigger="hover"
                    :content="getDeviceStatusContent(scope.row.mac_address)">
                    <span slot="reference">{{ scope.row.isOnline == false ? $t('equipmentCenter.status.offlineStatus') :
                      getDeviceStatus(scope.row.mac_address) }}</span>
                  </el-popover>
                </template>
              </el-table-column>
              <el-table-column :label="$t('equipmentCenter.table.operation')" width="180" align="center">
                <template slot-scope="scope">
                  <el-button size="mini" @click.stop="editDevice(scope.row)">{{ $t('equipmentCenter.table.edit')
                  }}</el-button>
                  <el-button size="mini" type="danger" @click.stop="deleteDevice(scope.row)">{{
                    $t('equipmentCenter.table.delete') }}</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>

          <!-- 单个设备详情视图 -->
          <div v-if="selectedDevice.isClient" class="device-detail-view">
            <div class="device-header-info">
              <div class="device-title">
                <i class="el-icon-monitor"></i>
                <h2>{{ selectedDevice.alias_name || selectedDevice.label }}</h2>
                <el-tag :type="selectedDevice.isOnline ? 'success' : 'danger'" size="small" effect="dark">
                  {{ selectedDevice.isOnline ? ($t('equipmentCenter.table.online') || '在线') :
                    ($t('equipmentCenter.table.offline') || '离线') }}
                </el-tag>
              </div>
              <div class="header-actions">
                <el-dropdown @command="handleDeviceCommand">
                  <el-button type="primary" size="small">
                    {{ $t('equipmentCenter.info.remoteControl') }}<i class="el-icon-arrow-down el-icon--right"></i>
                  </el-button>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item command="screenshot" icon="el-icon-camera">{{
                      $t('equipmentCenter.info.realTimeScreenshot')
                      }}</el-dropdown-item>
                    <el-dropdown-item command="reboot" icon="el-icon-refresh-right">{{
                      $t('equipmentCenter.info.remoteReboot')
                      }}</el-dropdown-item>
                    <el-dropdown-item command="shutdown" icon="el-icon-switch-button">{{
                      $t('equipmentCenter.info.remoteShutdown')
                      }}</el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
                <el-button type="success" size="small" icon="el-icon-upload" @click="otaUpgrade(selectedDevice)">
                  {{ $t('equipmentCenter.info.otaUpgrade') }}
                </el-button>
                <el-button type="primary" size="small" icon="el-icon-edit" @click="editDevice(selectedDevice)">
                  {{ $t('equipmentCenter.button.deviceEdit') }}
                </el-button>
                <el-button type="danger" size="small" icon="el-icon-delete" @click="deleteDevice(selectedDevice)">
                  {{ $t('equipmentCenter.button.delete') }}
                </el-button>
              </div>
            </div>

            <el-tabs v-model="activeTab" @tab-click="handleTabClick">
              <el-tab-pane :label="$t('equipmentCenter.info.basicInfo')" name="info">
                <div class="device-info-panel">
                  <el-descriptions border :column="3">
                    <el-descriptions-item :label="$t('equipmentCenter.table.alias_name')">{{ selectedDevice.alias_name
                      || '-'
                    }}</el-descriptions-item>
                    <el-descriptions-item :label="$t('equipmentCenter.form.mac')">{{ selectedDevice.mac_address || '-'
                    }}</el-descriptions-item>
                    <el-descriptions-item :label="$t('equipmentCenter.table.group_name')">{{ selectedDevice.group_name
                      || '-'
                    }}</el-descriptions-item>
                    <el-descriptions-item :label="$t('equipmentCenter.table.ip_addr')">{{ selectedDevice.ip_addr || '-'
                    }}</el-descriptions-item>
                    <el-descriptions-item :label="$t('equipmentCenter.table.model')">{{ selectedDevice.model || '-'
                    }}</el-descriptions-item>
                    <el-descriptions-item :label="$t('equipmentCenter.table.system')">{{ selectedDevice.system || '-'
                    }}</el-descriptions-item>
                    <el-descriptions-item :label="$t('equipmentCenter.table.system_version')">{{
                      selectedDevice.system_version
                      || '-'
                    }}</el-descriptions-item>
                    <el-descriptions-item :label="$t('equipmentCenter.table.serial_number')">{{
                      selectedDevice.serial_number ||
                      '-'
                    }}</el-descriptions-item>
                    <el-descriptions-item :label="$t('equipmentCenter.table.screen')">{{ selectedDevice.screen_height +
                      'X' +
                      selectedDevice.screen_width || '-'
                    }}</el-descriptions-item>
                    <el-descriptions-item :label="$t('equipmentCenter.table.updated_at')">{{
                      formatTime(selectedDevice.updated_at) || '-' }}</el-descriptions-item>
                  </el-descriptions>
                </div>
              </el-tab-pane>
              <el-tab-pane :label="$t('equipmentCenter.info.materialManagement')" name="material">
                <div class="material-panel">
                  <div class="material-action-bar">
                    <el-button type="primary" size="small" icon="el-icon-upload"
                      @click="uploadMaterial(selectedDevice)">
                      {{ $t('equipmentCenter.info.uploadMaterial') }}
                    </el-button>
                    <el-button type="danger" size="small" icon="el-icon-delete" @click="batchDeleteMaterials"
                      :disabled="selectedMaterialIds.length === 0">
                      {{ $t('public.batchDelete') }}
                    </el-button>
                  </div>
                  <div class="material-list-wrapper">
                    <div v-if="selectedDevice.material && selectedDevice.material.length" class="material-list">
                      <div v-for="item in selectedDevice.material" :key="item.id" class="material-card">
                        <div @click="toggleMaterialSelection(item.id)">
                          <el-checkbox class="material-checkbox" :value="selectedMaterialIds.includes(item.id)"
                            @click.native.stop></el-checkbox>
                          <div class="card-preview">
                            <el-image v-if="isImage(item)" :src="imageUrl + item.path" fit="cover"></el-image>
                            <div v-else class="file-icon-wrapper">
                              <i :class="getIconClass(item)"></i>
                            </div>
                          </div>
                          <div class="card-info">
                            <p class="card-name" :title="item.name">{{ item.name }}</p>
                            <div class="card-details">
                              <span :class="['type-tag', `type-${item.type}`]">{{ getTypeName(item.type) }}</span>
                              <span>{{ formatFileSize(item.size) }}</span>
                            </div>
                            <time class="card-time">{{ $formatTimeStamp(item.created_at, "YYYY-MM-DD HH:mm") }}</time>
                          </div>
                        </div>
                        <div class="card-actions">
                          <el-button type="text" size="small" @click="pushMaterial(item)">推送</el-button>
                          <el-button type="text" size="small" class="danger-text"
                            @click="delMaterial(item)">删除</el-button>
                        </div>
                      </div>
                    </div>
                    <el-empty v-else :description="$t('equipmentCenter.material.noMaterial')"
                      :image-size="100"></el-empty>
                  </div>
                </div>
              </el-tab-pane>
              <el-tab-pane :label="$t('equipmentCenter.info.deviceLogs')" name="log">
                <div class="log-panel" v-loading="isLogLoading">
                  <div class="material-action-bar">
                    <el-button type="danger" size="small" icon="el-icon-delete" @click="clearLogs">
                      {{ $t('equipmentCenter.info.clearLogs') }}
                    </el-button>
                  </div>
                  <el-table :data="equipmentLogs" height="calc(100vh - 450px)" style="width: 100%">
                    <el-table-column prop="timestamp" :label="$t('equipmentCenter.table.time')" width="180">
                      <template slot-scope="scope">
                        {{ $formatTimeStamp(scope.row.timestamp) }}
                      </template>
                    </el-table-column>
                    <el-table-column prop="report_type" :label="$t('equipmentCenter.table.logType')"
                      width="120"></el-table-column>
                    <el-table-column prop="data" :label="$t('equipmentCenter.table.logContent')"></el-table-column>
                  </el-table>
                  <!-- <el-empty v-if="!isLogLoading && equipmentLogs.length === 0" description="暂无日志"></el-empty> -->
                </div>
              </el-tab-pane>
            </el-tabs>
          </div>
        </div>
      </el-main>
    </el-container>

    <!-- 编辑设备弹窗 -->
    <el-dialog :title="$t('equipmentCenter.dialog.title.edit')" :visible.sync="isShow" width="40%" @close="closeDialog"
      :close-on-click-modal="false">
      <el-form :model="deviceInfo" :rules="rules" ref="deviceInfoForm" label-width="120px" label-position="left">
        <el-form-item :label="$t('equipmentCenter.table.group_name')" prop="group_name">
          <el-select v-model="deviceInfo.group_name" :placeholder="$t('equipmentCenter.form.groupPlaceholder')"
            style="width: 100%;" filterable allow-create default-first-option>
            <el-option v-for="item in groupNameList" :key="item.id" :label="item.name == 'null' ? '无分组' : item.name"
              :value="item.name">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('equipmentCenter.table.alias_name')" prop="alias_name" required>
          <el-input v-model="deviceInfo.alias_name"
            :placeholder="$t('equipmentCenter.form.namePlaceholder')"></el-input>
        </el-form-item>
        <el-form-item :label="$t('equipmentCenter.form.mac')" prop="mac_address">
          <el-input v-model="deviceInfo.mac_address" :disabled="true"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="isShow = false">{{ $t('public.cancel') }}</el-button>
        <el-button type="primary" @click="saveDevice()">{{ $t('public.confirm') }}</el-button>
      </span>
    </el-dialog>

    <!-- 素材选择弹窗 -->
    <el-dialog :title="$t('equipmentCenter.info.replaceMaterial')" :visible.sync="isMaterialDialogVisible" width="60%"
      append-to-body :close-on-click-modal="false">
      <div class="material-select-dialog">
        <el-table v-loading="isMaterialLoading" :data="materials" height="400px" style="width: 100%;" border>
          <el-table-column width="55" align="center">
            <template slot-scope="scope">
              <el-radio :label="scope.row.id" v-model="selectedMaterialId" @change.native="() => { }">&nbsp;</el-radio>
            </template>
          </el-table-column>
          <el-table-column label="缩略图" width="120" align="center">
            <template slot-scope="scope">
              <el-image v-if="isImage(scope.row)" style="width: 80px; height: 80px; border-radius: 4px;"
                :src="scope.row.thumbnail" :preview-src-list="[scope.row.thumbnail]" fit="cover">
                <div slot="error" class="image-slot">
                  <i class="el-icon-picture-outline"></i>
                </div>
              </el-image>
              <div v-else class="file-icon-container">
                <i :class="getIconClass(scope.row)" class="file-icon"></i>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="label" label="素材名称" show-overflow-tooltip></el-table-column>
          <el-table-column prop="type" label="类型" width="100" align="center"></el-table-column>
          <el-table-column prop="size" label="大小" width="120" align="center"></el-table-column>
        </el-table>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="isMaterialDialogVisible = false">{{ $t('public.cancel') }}</el-button>
        <el-button type="primary" @click="confirmReplaceMaterial">{{ $t('public.confirm') }}</el-button>
      </span>
    </el-dialog>

    <!-- 作业指导书上传弹窗 -->
    <equipment-material-upload-dialog v-if="deviceForUpload" :visible.sync="isUploadDialogVisible"
      :equipment="deviceForUpload" @upload-success="handleUploadSuccess" />

    <!-- 实时截图弹窗 -->
    <el-dialog :title="$t('equipmentCenter.info.realTimeScreenshot')" :visible.sync="isScreenshotDialogVisible"
      width="800px" @close="closeScreenshotDialog" append-to-body :close-on-click-modal="false">
      <div class="preview-dialog-content" v-loading="isScreenshotLoading" element-loading-text="正在获取截图...">
        <el-image v-if="screenshotUrl" :src="screenshotUrl" fit="contain"
          style="width: 100%; height: 450px; background: #000;"></el-image>
        <div v-else class="preview-placeholder">
          <i class="el-icon-picture-outline"></i>
          <p>无法加载截图</p>
        </div>
      </div>
    </el-dialog>



    <!-- 音量调节弹窗 -->
    <el-dialog :title="$t('equipmentCenter.info.volumeAdjustment')" :visible.sync="isVolumeDialogVisible" width="400px"
      :close-on-click-modal="false">
      <div class="volume-dialog-content">
        <p>为选中的 <el-tag size="small" type="info">{{ multipleSelection.length }}</el-tag> 个设备调节音量：</p>
        <div class="volume-slider">
          <span class="volume-icon"><i class="el-icon-turn-off"></i></span>
          <el-slider v-model="volumeValue" :step="1" :min="0" :max="100" show-input></el-slider>
          <span class="volume-icon"><i class="el-icon-open"></i></span>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="isVolumeDialogVisible = false">{{ $t('public.cancel') }}</el-button>
        <el-button type="primary" @click="sendVolumeCommand">{{ $t('public.confirm') }}</el-button>
      </span>
    </el-dialog>

    <!-- 定时开关机弹窗 -->
    <!-- 定时开关机弹窗 -->
    <el-dialog :title="$t('equipmentCenter.info.powerSchedule')" :visible.sync="isSchedulePowerDialogVisible"
      width="900px" :close-on-click-modal="false" custom-class="schedule-power-dialog">
      <div class="schedule-power-dialog-content">
        <p class="dialog-tip">为选中的 <el-tag size="small" type="info">{{ multipleSelection.length }}</el-tag> 个设备设置定时开关机：
        </p>
        <el-table :data="schedulePowerForm.timeRanges" style="width: 100%" row-key="id"
          v-loading="isSchedulePowerLoading">
          <el-table-column label="开机时间" width="160" align="center">
            <template slot-scope="scope">
              <el-time-select v-model="scope.row.powerOnTime"
                :picker-options="{ start: '00:00', step: '00:05', end: '23:55' }" placeholder="选择时间"></el-time-select>
            </template>
          </el-table-column>
          <el-table-column label="关机时间" width="160" align="center">
            <template slot-scope="scope">
              <el-time-select v-model="scope.row.powerOffTime"
                :picker-options="{ start: '00:00', step: '00:05', end: '23:55' }" placeholder="选择时间"></el-time-select>
            </template>
          </el-table-column>
          <el-table-column label="全选" width="60" align="center">
            <template slot-scope="scope">
              <el-checkbox v-model="scope.row.allWeekdays" @change="toggleAllWeekdays(scope.row)"></el-checkbox>
            </template>
          </el-table-column>
          <el-table-column label="周一" width="60" align="center">
            <template slot-scope="scope">
              <el-checkbox v-model="scope.row.weekdays.monday"
                @change="updateAllWeekdayStatus(scope.row)"></el-checkbox>
            </template>
          </el-table-column>
          <el-table-column label="周二" width="60" align="center">
            <template slot-scope="scope">
              <el-checkbox v-model="scope.row.weekdays.tuesday"
                @change="updateAllWeekdayStatus(scope.row)"></el-checkbox>
            </template>
          </el-table-column>
          <el-table-column label="周三" width="60" align="center">
            <template slot-scope="scope">
              <el-checkbox v-model="scope.row.weekdays.wednesday"
                @change="updateAllWeekdayStatus(scope.row)"></el-checkbox>
            </template>
          </el-table-column>
          <el-table-column label="周四" width="60" align="center">
            <template slot-scope="scope">
              <el-checkbox v-model="scope.row.weekdays.thursday"
                @change="updateAllWeekdayStatus(scope.row)"></el-checkbox>
            </template>
          </el-table-column>
          <el-table-column label="周五" width="60" align="center">
            <template slot-scope="scope">
              <el-checkbox v-model="scope.row.weekdays.friday"
                @change="updateAllWeekdayStatus(scope.row)"></el-checkbox>
            </template>
          </el-table-column>
          <el-table-column label="周六" width="60" align="center">
            <template slot-scope="scope">
              <el-checkbox v-model="scope.row.weekdays.saturday"
                @change="updateAllWeekdayStatus(scope.row)"></el-checkbox>
            </template>
          </el-table-column>
          <el-table-column label="周日" width="60" align="center">
            <template slot-scope="scope">
              <el-checkbox v-model="scope.row.weekdays.sunday"
                @change="updateAllWeekdayStatus(scope.row)"></el-checkbox>
            </template>
          </el-table-column>
          <el-table-column label="可用" width="70" align="center">
            <template slot-scope="scope">
              <el-switch v-model="scope.row.enabled" active-color="#13ce66"></el-switch>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="70" align="center">
            <template slot-scope="scope">
              <el-button type="danger" icon="el-icon-delete" size="mini" circle
                @click="removeTimeRange(scope.$index)"></el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button icon="el-icon-plus" @click="addTimeRange">{{ $t('equipmentCenter.info.addTimeRange') }}</el-button>
        <el-button type="primary" icon="el-icon-check" @click="sendSchedulePowerCommand"
          :loading="isSchedulePowerLoading">{{ $t('equipmentCenter.info.saveSchedule') }}</el-button>
        <el-button type="danger" icon="el-icon-delete" @click="clearSchedulePowerCommand"
          :loading="isSchedulePowerLoading">{{ $t('equipmentCenter.info.clearSchedule') }}</el-button>
      </span>
    </el-dialog>

    <!-- 分组新增/编辑弹窗 -->
    <el-dialog
      :title="isEditGroup ? $t('equipmentCenter.dialog.title.editGroup') : $t('equipmentCenter.dialog.title.addGroup')"
      :visible.sync="isGroupDialogVisible" width="40%" @close="closeGroupDialog" :close-on-click-modal="false">
      <el-form :model="groupForm" :rules="groupRules" ref="groupForm" label-width="120px" label-position="left">
        <el-form-item :label="$t('equipmentCenter.dialog.form.groupName')" prop="name">
          <el-input v-model="groupForm.name"
            :placeholder="$t('equipmentCenter.dialog.form.groupNamePlaceholder')"></el-input>
        </el-form-item>
        <el-form-item :label="$t('equipmentCenter.dialog.form.groupDescription')" prop="description">
          <el-input v-model="groupForm.description" type="textarea" :rows="3"
            :placeholder="$t('equipmentCenter.dialog.form.groupDescriptionPlaceholder')"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="isGroupDialogVisible = false">{{ $t('public.cancel') }}</el-button>
        <el-button type="primary" @click="saveGroup">{{ $t('public.confirm') }}</el-button>
      </span>
    </el-dialog>

    <!-- 批量删除文件弹窗 -->
    <el-dialog :title="$t('equipmentCenter.info.batchDeleteFiles')" :visible.sync="isBatchDeleteDialogVisible"
      width="30%" :close-on-click-modal="false">
      <el-form label-width="120px">
        <el-form-item :label="$t('equipmentCenter.info.selectDeleteDays')">
          <el-select v-model="deleteDays" :placeholder="$t('public.select')">
            <el-option label="3天" :value="3"></el-option>
            <el-option label="5天" :value="5"></el-option>
            <el-option label="10天" :value="10"></el-option>
            <el-option label="20天" :value="20"></el-option>
            <el-option label="30天" :value="30"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="isBatchDeleteDialogVisible = false">{{ $t('public.cancel') }}</el-button>
        <el-button type="primary" @click="confirmBatchDelete">{{ $t('public.confirm') }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getTreeList, edit, del, getGroupNameList, addGroup, editGroup, deleteGroup, batchUploadFiles, sendCommand, getMaterial, associateMaterial, disassociateMaterial, pushMaterial, batchPushMaterial, getScreenshotByTaskId, deleteScreenshot, getEquipmentLog, getLatestDeviceReport, clearEquipmentLog, saveEquipmentPowerSchedules, getEquipmentPowerSchedules, batchDeleteFiles } from '@/api/equipmentCenter.js'
import { getList as getMaterialList, edit as editMaterial } from '@/api/material.js'
import EquipmentMaterialUploadDialog from '@/components/equipment-material-upload-dialog.vue'

export default {
  name: 'EquipmentCenter',
  components: { EquipmentMaterialUploadDialog },
  props: ['groupName', 'macAddress'],
  watch: {
    filterText (val) {
      this.$refs.tree.filter(val);
    },
    '$route.params': {
      handler: 'processRouteChange',
      deep: true,
      immediate: true,
    }
  },
  data () {
    return {
      imageUrl: process.env.VUE_APP_BASE_API + "assets/media/",
      equipmentData: [],
      defaultProps: {
        children: 'children',
        label: 'label',
        isClient: 'isClient',
        mac_address: 'mac_address',
      },
      filterText: "",
      selectedDevice: null, // 当前选中的设备或分组
      activeTab: 'info', // for device detail tabs
      deviceInfo: {
        mac_address: '',
        alias_name: '',
        group_name: '',
      },
      groupNameList: [],
      rules: {
        alias_name: [
          { required: true, message: this.$t('equipmentCenter.form.namePlaceholder'), trigger: 'blur' },
        ],
      },
      isShow: false,
      isLoading: false,
      multipleSelection: [], // 用于存储表格中的多选行

      // 素材选择弹窗
      isMaterialDialogVisible: false,
      materials: [],
      isMaterialLoading: false,
      selectedMaterialId: null,
      selectedMaterialIds: [], // 用于批量删除

      // 素材上传弹窗
      isUploadDialogVisible: false,
      deviceForUpload: null, // 用于上传弹窗

      // 实时截图弹窗
      isScreenshotDialogVisible: false,
      isScreenshotLoading: false,
      screenshotUrl: '',
      screenshotTaskId: null,
      screenshotPollTimer: null,
      screenshotId: null, // 新增，用于存储截图ID

      // 素材编辑弹窗
      isEditMaterialDialogVisible: false,
      editingMaterial: null,
      materialRules: {
        name: [
          { required: true, message: '请输入素材名称', trigger: 'blur' },
        ],
      },

      // 分组管理弹窗
      isGroupDialogVisible: false,
      isEditGroup: false,
      groupForm: {
        id: null,
        name: '',
        description: '',
      },
      groupRules: {
        name: [
          { required: true, message: this.$t('equipmentCenter.dialog.message.groupNameRequired'), trigger: 'blur' },
        ],
      },

      // 路由处理防抖
      routeHandlingTimer: null,

      // 设备树展开和选中节点
      expandedKeys: [],
      currentNodeKey: null,

      // 音量调节
      isVolumeDialogVisible: false,
      volumeValue: 50,

      // 定时开关机
      isSchedulePowerDialogVisible: false,
      schedulePowerForm: {
        timeRanges: [
          {
            powerOnTime: '08:00',
            powerOffTime: '12:00',
            allWeekdays: false,
            enabled: true,
            weekdays: {
              monday: true,
              tuesday: true,
              wednesday: true,
              thursday: true,
              friday: true,
              saturday: false,
              sunday: false
            }
          }
        ]
      },
      isSchedulePowerLoading: false,

      // 设备日志
      equipmentLogs: [],
      isLogLoading: false,
      deviceReportStatus: {}, // 存储设备上报状态
      pollTimer: null, // 用于轮询的计时器
      isBatchDeleteDialogVisible: false,
      deleteDays: null,
    }
  },
  computed: {
    // 计算面包屑路径
    breadcrumbPaths () {

      const paths = [
        {
          label: this.$t('equipmentCenter.tree.title') || '设备列表',
          to: { name: 'EquipmentCenter' }
        }
      ];

      if (!this.selectedDevice) {
        return paths;
      }

      // 如果是分组节点
      if (!this.selectedDevice.isClient) {
        paths.push({
          label: this.selectedDevice.label
        });
        return paths;
      }

      // 如果是设备节点，需要查找其所在的分组路径
      const findDevicePath = (nodes, path = []) => {


        for (const index in nodes) {
          const currentPath = [...path, nodes[index]];
          if (nodes[index].id === this.selectedDevice.id) {
            return currentPath;
          }
          if (nodes[index].children) {
            const result = findDevicePath(nodes[index].children, currentPath);
            if (result) {

              return result;
            }
          }
        }
        return null;
      };

      const devicePath = findDevicePath(this.equipmentData);

      if (devicePath && devicePath.length > 0) {

        this.expandedKeys.push(devicePath[1])

        // 添加所有父级分组
        for (let i = 0; i < devicePath.length - 1; i++) {
          const node = devicePath[i];
          // 只添加分组节点到路径中
          if (!node.isClient) {
            paths.push({
              label: node.label,
              to: { name: 'EquipmentCenter', params: { groupName: node.label } }
            });
          }
        }
      }

      // 添加设备节点
      paths.push({
        label: this.selectedDevice.alias_name || this.selectedDevice.label
      });

      return paths;
    }
  },
  created () {
    this.getList();
    this.startPolling();
  },
  beforeDestroy () {
    this.stopPolling();
  },

  methods: {
    startPolling () {
      // 如果已经有计时器，先清除
      if (this.pollTimer) {
        clearInterval(this.pollTimer);
      }
      this.pollTimer = setInterval(() => {
        // 如果用户已经选中了某个设备或分组，则不执行轮询，避免干扰操作
        if (this.selectedDevice) {
          console.log('User has a selection, skipping poll to avoid interruption.');
          return;
        }
        console.log('Polling for device list...');
        this.getList();
      }, 5000); // 5秒轮询一次
    },

    stopPolling () {
      if (this.pollTimer) {
        clearInterval(this.pollTimer);
        this.pollTimer = null;
      }
    },
    getList () {
      this.isLoading = true
      getTreeList().then(res => {
        if (res.code == 0) {
          this.equipmentData = res.data
          console.log('设备数据已加载:', this.equipmentData)
          console.log('当前路由参数:', this.$route.params)
          // Data is loaded, call route handler again to ensure tree state is updated
          this.processRouteChange(this.$route.params);
        }
      }).finally(() => {
        this.isLoading = false
      })
    },
    handleRouteChange (params) {
      console.log('handleRouteChange 被调用，参数:', params)

      // Guard against running before data is loaded
      if (!this.equipmentData || this.equipmentData.length === 0) {
        console.log('设备数据未加载，跳过处理')
        return;
      }

      // Ensure the tree component is rendered before manipulating it
      this.$nextTick(() => {
        if (!this.$refs.tree) {
          console.log('树组件未准备好')
          return;
        }

        const { macAddress } = params;
        console.log('要查找的MAC地址:', macAddress)

        // Only proceed if we have a MAC address to select
        if (!macAddress) {
          console.log('没有MAC地址参数')
          return;
        }

        const predicate = (node) =>
          node.isClient &&
          node.mac_address &&
          node.mac_address.toUpperCase() === macAddress.toUpperCase();

        const path = this.getNodePath(this.equipmentData, predicate);
        console.log('找到的节点路径:', path)

        if (path.length > 0) {
          const targetNode = path[path.length - 1];
          console.log('目标节点:', targetNode)

          // Expand all parent nodes using the official getNode method
          path.slice(0, -1).forEach(p => {
            const node = this.$refs.tree.getNode(p.id);
            console.log('展开父节点:', p.label, node)
            if (node) {
              node.expanded = true;
            }
          });

          this.selectedDevice = targetNode;
          this.$refs.tree.setCurrentKey(targetNode.id);
          console.log('设置选中节点:', targetNode.id)

          // 移除自动加载素材的逻辑，改为在切换到作业指导书tab时加载
        } else {
          console.log('未找到匹配的节点')
          // If no node is found, clear the selection
          this.selectedDevice = null;
          this.$refs.tree.setCurrentKey(null);
        }
      });
    },
    processRouteChange (params) {
      console.log('>>>>>>> processRouteChange 被调用，参数:', params)
      console.log('>>>>>>> 当前设备数据:', this.equipmentData)

      // Guard against running before data is loaded
      if (!this.equipmentData || this.equipmentData.length === 0) {
        console.log('>>>>>>> 设备数据未加载，跳过处理')
        return;
      }

      // Ensure the tree component is rendered before manipulating it
      this.$nextTick(() => {
        if (!this.$refs.tree) {
          console.log('>>>>>>> 树组件未准备好')
          return;
        }

        const { groupName, macAddress } = params;
        console.log('>>>>>>> 要查找的分组名称:', groupName)
        console.log('>>>>>>> 要查找的MAC地址:', macAddress)

        // 重置当前选中状态
        this.selectedDevice = null;
        this.currentNodeKey = null;
        this.expandedKeys = [];

        let targetGroupNode = null;
        let expandedKeys = [];

        // 如果有分组名称，先找到并展开对应的分组
        if (groupName) {
          console.log('>>>>>>> 开始查找分组...')
          const groupPredicate = (node) => {
            const isMatch = !node.isClient && node.label === groupName;
            console.log(`>>>>>>> 检查节点: ${node.label}, isClient: ${node.isClient}, 是否匹配: ${isMatch}`);
            return isMatch;
          }

          const groupPath = this.getNodePath(this.equipmentData, groupPredicate);
          console.log('>>>>>>> 找到的分组路径:', groupPath)

          if (groupPath.length > 0) {
            targetGroupNode = groupPath[groupPath.length - 1];
            for (var index in this.equipmentData) {

              if (this.equipmentData[index].group_id == targetGroupNode.group_id) {
                expandedKeys = [targetGroupNode.label]
              }

            }
            console.log(groupPath[0], "groupPathgroupPathgroupPath");

            if (!groupPath[0].isClient && groupPath[0].children && groupPath[0].children.length > 0) {
              this.fetchDeviceStatuses(groupPath[0].children);
            }
          } else {
            console.log('>>>>>>> 未找到指定分组');
          }


        }

        // 如果有MAC地址，选中对应的设备节点
        if (macAddress) {
          console.log('>>>>>>> 开始查找设备...')
          const devicePredicate = (node) => {
            const isMatch = node.isClient &&
              node.mac_address &&
              node.mac_address.toUpperCase() === macAddress.toUpperCase();
            console.log(`>>>>>>> 检查设备节点: ${node.label}, MAC: ${node.mac_address}, 是否匹配: ${isMatch}`);
            return isMatch;
          }

          const devicePath = this.getNodePath(this.equipmentData, devicePredicate);
          console.log('>>>>>>> 找到的设备节点路径:', devicePath)

          if (devicePath.length > 0) {
            const targetNode = devicePath[devicePath.length - 1];
            console.log('>>>>>>> 目标设备节点:', targetNode)

            // 收集所有需要展开的节点ID
            devicePath.slice(0, -1).forEach((p, index) => {
              console.log(`>>>>>>> 处理设备路径节点 ${index + 1}:`, p);
              expandedKeys = [p.label]
              // expandedKeys.push(index + 1);
            });

            this.selectedDevice = targetNode;
            this.currentNodeKey = targetNode.id;
            this.expandedKeys = expandedKeys;
            console.log('>>>>>>> 设置选中节点1:', targetNode)
            console.log('>>>>>>> 设置展开节点1:', expandedKeys)

            if (targetNode.isClient) {
              this.getDeviceMaterial(targetNode.id);
              this.getDeviceLogs();
            }

            // 更新面包屑显示
            this.updateBreadcrumb();




            return;
          } else {
            console.log('>>>>>>> 未找到指定设备');
          }
        } else if (targetGroupNode) {
          // 如果只有分组名，没有设备MAC地址，则选中分组
          this.selectedDevice = targetGroupNode;
          this.currentNodeKey = targetGroupNode.id;
          this.expandedKeys = expandedKeys;
          console.log('>>>>>>> 设置选中分组节点:', targetGroupNode.id)
          console.log('>>>>>>> 设置展开节点:', expandedKeys)

          // 更新面包屑显示
          this.updateBreadcrumb();
        } else {
          // 没有选中任何节点，清空展开状态
          this.expandedKeys = [];
        }

        // 如果没有找到设备或者没有设备参数，清空选择
        console.log('>>>>>>> 未找到匹配的设备节点')
      });
    },
    findNodeById (tree, id) {
      for (const node of tree) {
        if (node.id == id) {
          return node;
        }
        if (node.children) {
          const found = this.findNodeById(node.children, id);
          if (found) {
            return found;
          }
        }
      }
      return null;
    },
    findNode (tree, predicate) {
      for (const node of tree) {
        if (predicate(node)) {
          return node;
        }
        if (node.children) {
          const found = this.findNode(node.children, predicate);
          if (found) {
            return found;
          }
        }
      }
      return null;
    },
    findNodeByLabel (tree, label) {
      return this.findNode(tree, (node) => !node.isClient && node.label === label);
    },
    findNodeByMac (tree, mac) {
      return this.findNode(tree, (node) => node.isClient && node.mac_address === mac);
    },
    getNodePath (tree, predicate) {
      const path = [];
      const find = (nodes) => {
        for (const node of nodes) {
          path.push(node);
          if (predicate(node)) {
            return true;
          }
          if (node.children) {
            if (find(node.children)) {
              return true;
            }
          }
          path.pop();
        }
        return false;
      }
      if (find(tree)) {
        return path;
      }
      return [];
    },
    handleNodeClick (data, node) {


      let groupName = null;
      let macAddress = null;

      // 收集需要展开的节点
      let expandedKeys = [];
      let parent = node.parent;
      while (parent && parent.data) {
        expandedKeys.push(parent.data.id);
        parent = parent.parent;
      }

      // 更新展开节点
      this.expandedKeys = expandedKeys;

      if (data.isClient) {
        // 对于设备节点，找到其所属的分组
        let parent = node.parent;
        while (parent && parent.data && parent.data.isClient) {
          parent = parent.parent;
        }
        if (parent && parent.data) {
          groupName = parent.data.label;
        }
        macAddress = data.mac_address;
      } else {
        // 对于分组节点
        groupName = data.label;
      }

      // 构建路由参数
      const newParams = {};
      if (groupName) {
        newParams.groupName = groupName;
      }
      if (macAddress) {
        newParams.macAddress = macAddress;
      }

      // 更新选中状态
      this.selectedDevice = data;
      this.currentNodeKey = data.id;


      // 移除自动加载素材的逻辑，改为在切换到作业指导书tab时加载

      if (!data.isClient) {
        // 对于分组节点，切换展开/收起状态
        const nodeKey = data.id;
        const index = this.expandedKeys.indexOf(nodeKey);
        if (index > -1) {
          // 收起节点
          this.expandedKeys.splice(index, 1);
        } else {
          // 展开节点
          this.expandedKeys.push(nodeKey);
        }
      }

      // 只有当路由参数发生变化时才进行跳转
      const needNavigation =
        this.$route.params.groupName !== newParams.groupName ||
        this.$route.params.macAddress !== newParams.macAddress;

      if (needNavigation) {
        this.$router.push({ name: 'EquipmentCenter', params: newParams });
      }

      // 更新面包屑显示
      this.updateBreadcrumb();

      if (!data.isClient && data.children && data.children.length > 0) {
        this.fetchDeviceStatuses(data.children);
      }
    },

    filterNode (value, data) {
      if (!value) return true;
      const lowerCaseValue = value.toLowerCase();
      return (data.label && data.label.toLowerCase().indexOf(lowerCaseValue) !== -1) ||
        (data.alias_name && data.alias_name.toLowerCase().indexOf(lowerCaseValue) !== -1) ||
        (data.mac_address && data.mac_address.toLowerCase().indexOf(lowerCaseValue) !== -1);
    },

    renderContent (h, { node, data }) {
      const isClient = data.isClient;
      return h(
        'span',
        { class: 'custom-tree-node' },
        [
          h(
            'span',
            { class: 'node-content' },
            [
              h('i', {
                class: [
                  isClient ? 'el-icon-monitor' : (!node.expanded ? 'el-icon-folder' : 'el-icon-folder-opened'),
                  'node-icon',
                  { 'online-icon': isClient && data.isOnline, 'offline-icon': isClient && !data.isOnline }
                ]
              }),
              h('span', { class: 'node-label' }, node.label == 'Ungrouped' ? this.$t('equipmentCenter.tree.null') : node.label),
              isClient
                ? h(
                  'el-tag',
                  {
                    props: {
                      size: 'mini',
                      type: data.isOnline ? 'success' : 'danger',
                    },
                    class: 'status-tag',
                  },
                  data.isOnline ? this.$t('equipmentCenter.tree.online') : this.$t("equipmentCenter.tree.offline")
                )
                : null,
            ]
          ),
          !isClient
            ? h(
              'span',
              { class: 'node-extra' },
              [
                h(
                  'span',
                  { class: 'device-count' },
                  [
                    h('span', { class: 'online-count' }, data.online || 0),
                    ' / ',
                    h('span', { class: 'total-count' }, data.total || 0),
                  ]
                )
              ]
            )
            : null,
        ]

      );
    },

    getDeviceMaterial (id) {
      getMaterial(id).then(res => {
        if (res.code == 0 && res.data) {
          // 确保 material 是一个数组
          const materials = Array.isArray(res.data) ? res.data : [res.data];
          this.$set(this.selectedDevice, 'material', materials)
        } else {
          this.$set(this.selectedDevice, 'material', [])
        }
      })
    },

    // =================== 素材与预览相关方法 ===================
    uploadMaterial (device) {
      this.deviceForUpload = device
      this.isUploadDialogVisible = true
    },

    handleUploadSuccess () {
      // 上传成功后，刷新当前设备的素材列表
      if (this.selectedDevice && this.selectedDevice.isClient) {
        this.getDeviceMaterial(this.selectedDevice.id)
      }
    },

    replaceMaterial (device) {
      this.selectedMaterialId = device.material ? device.material.id : null;
      this.isMaterialDialogVisible = true;
      this.fetchMaterials();
    },

    fetchMaterials () {
      this.isMaterialLoading = true;
      // 假设 getMaterialList 返回素材列表
      getMaterialList({ page: 1, limit: 1000 }).then(res => {
        if (res.code === 0) {
          this.materials = (res.data.list || res.data).map(item => ({
            ...item,
            id: item.id,
            label: item.label || item.name,
            // 确保 thumbnail 路径正确
            thumbnail: item.path && item.path.startsWith('http') ? item.path : process.env.VUE_APP_BASE_API + '/' + item.path,
            type: item.type === 1 ? '图片' : '视频',
          }));
        } else {
          this.$message.error('获取素材列表失败');
        }
      }).catch(() => {
        this.$message.error('获取素材列表失败');
      }).finally(() => {
        this.isMaterialLoading = false;
      });
    },

    confirmReplaceMaterial () {
      if (!this.selectedMaterialId) {
        this.$message.warning('请选择一个素材');
        return;
      }

      const associationData = {
        equipment_id: this.selectedDevice.id,
        material_id: this.selectedMaterialId
      };

      // 调用API进行关联
      associateMaterial(associationData).then(() => {
        this.$message.success('素材关联成功');
        // 只有当前在作业指导书管理tab时才刷新素材信息
        if (this.activeTab === 'material') {
          this.getDeviceMaterial(this.selectedDevice.id);
        }
        this.isMaterialDialogVisible = false;
      }).catch(() => {
        this.$message.error('素材关联失败');
      });
    },

    screenshotDevice (device) {
      // 检查设备是否在线
      if (!device.isOnline) {
        this.$message.error('终端离线，无法发送');
        return;
      }

      this.isScreenshotDialogVisible = true;
      this.isScreenshotLoading = true;
      this.screenshotUrl = '';
      // 注意：不再在前端生成 task_id

      const commandData = {
        type: 4,
        group_name: device.group_name,
        equipment_alias_name: device.alias_name,
        command: "screenshot",
        // task_id 将由后端生成并返回
      };

      sendCommand(commandData).then((res) => {
        if (res.code === 0 && res.data && res.data.task_id) {
          this.screenshotTaskId = res.data.task_id; // 使用后端返回的 task_id
          this.$message.success(`已发送截图命令到设备 ${device.label}`);
          this.pollScreenshot(1); // 开始轮询
        } else {
          this.$message.error('发送截图命令失败或未返回任务ID');
          this.isScreenshotLoading = false;
          this.closeScreenshotDialog();
        }
      }).catch(() => {
        this.$message.error('发送截图命令请求失败');
        this.isScreenshotLoading = false;
        this.closeScreenshotDialog();
      });
    },

    pollScreenshot (attempt) {
      const maxAttempts = 30; // 轮询15次，总共约15秒
      if (attempt > maxAttempts) {
        this.$message.warning('获取截图超时，请稍后重试');
        this.isScreenshotLoading = false;
        this.closeScreenshotDialog();
        return;
      }

      this.screenshotPollTimer = setTimeout(() => {
        getScreenshotByTaskId(this.screenshotTaskId).then(res => {
          if (res.code === 0 && res.data && res.data.image_url) {
            this.screenshotUrl = process.env.VUE_APP_BASE_API + res.data.image_url;
            this.screenshotId = res.data.id; // 保存截图ID
            this.isScreenshotLoading = false;
            clearTimeout(this.screenshotPollTimer);
          } else {
            // 继续轮询
            this.pollScreenshot(attempt + 1);
          }
        }).catch(() => {
          // 请求失败也继续轮询
          this.pollScreenshot(attempt + 1);
        });
      }, 1000);
    },

    closeScreenshotDialog () {
      if (this.screenshotId) {
        deleteScreenshot({ id: this.screenshotId }).then(() => {
          console.log('Screenshot deleted successfully');
        }).catch(err => {
          console.error('Failed to delete screenshot', err);
        });
      }
      this.isScreenshotDialogVisible = false;
      this.screenshotUrl = '';
      this.screenshotTaskId = null;
      this.screenshotId = null; // 重置ID
      if (this.screenshotPollTimer) {
        clearTimeout(this.screenshotPollTimer);
        this.screenshotPollTimer = null;
      }
    },

    editMaterial (material) {
      // 深拷贝一份，避免直接修改
      this.editingMaterial = JSON.parse(JSON.stringify(material));
      this.isEditMaterialDialogVisible = true;
    },


    unbindMaterial (device) {
      this.$confirm(`确定要解除设备 [${device.label}] 与其素材的关联吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const disassociationData = {
          equipment_id: device.id,
        };
        disassociateMaterial(disassociationData).then(() => {
          this.$message.success('解除关联成功');
          this.getDeviceMaterial(this.selectedDevice.id);
        }).catch(() => {
          this.$message.error('解除关联失败');
        });
      }).catch(() => { });
    },

    // =================== 分组管理相关方法 ===================
    addGroupDialog () {
      this.isEditGroup = false;
      this.groupForm = {
        id: null,
        name: '',
        description: '',
      };
      this.isGroupDialogVisible = true;
    },

    editGroupDialog (group) {
      this.isEditGroup = true;
      this.groupForm = {
        id: group.id,
        name: group.label,
        description: group.description || '',
      };
      this.isGroupDialogVisible = true;
    },

    saveGroup () {
      this.$refs.groupForm.validate((valid) => {
        if (valid) {
          const api = this.isEditGroup ? editGroup : addGroup;
          const params = {
            name: this.groupForm.name,
            description: this.groupForm.description,
          };

          if (this.isEditGroup) {
            params.id = this.groupForm.id;
          }

          api(params).then(() => {
            this.$message.success(
              this.isEditGroup
                ? this.$t('equipmentCenter.dialog.message.groupEditSuccess')
                : this.$t('equipmentCenter.dialog.message.groupAddSuccess')
            );
            this.isGroupDialogVisible = false;
            this.getList(); // 刷新设备树
          }).catch(() => {
            this.$message.error('操作失败，请稍后重试');
          });
        }
      });
    },

    deleteGroupDialog (group) {
      if (group.group_id == 1) {
        this.$message.error(this.$t('equipmentCenter.dialog.message.defaultGroupDelete'));
        return;
      }
      if (group.children?.length > 0) {
        this.$message.error('请先删除分组下的所有设备');
        return;
      }

      this.$confirm(
        this.$t('equipmentCenter.dialog.message.deleteGroupWarning'),
        this.$t('equipmentCenter.dialog.message.deleteGroupConfirm'),
        {
          confirmButtonText: this.$t('public.confirm'),
          cancelButtonText: this.$t('public.cancel'),
          type: 'warning'
        }
      ).then(() => {
        deleteGroup(group.group_id).then(() => {
          this.$message.success(this.$t('equipmentCenter.dialog.message.groupDeleteSuccess'));
          this.selectedDevice = null; // 清空选中状态
          this.getList(); // 刷新设备树
        }).catch(() => {
          this.$message.error('删除失败，请稍后重试');
        });
      }).catch(() => {
        // 用户取消
      });
    },

    closeGroupDialog () {
      this.$refs.groupForm.resetFields();
      this.groupForm = {
        id: null,
        name: '',
        description: '',
      };
    },

    // =================== 原有方法 ===================
    editDevice (device) {
      if (!device || !device.isClient) {
        this.$message.warning('无效的设备');
        return;
      }
      this.deviceInfo = { ...device };
      this.isShow = true;
      this.fetchGroupNameList();
    },

    fetchGroupNameList () {
      getGroupNameList().then(res => {
        if (res.code == 0) {
          this.groupNameList = res.data
        }
      })
    },

    deleteDevice (device) {
      if (!device || !device.isClient) {
        this.$message.warning('无效的设备');
        return;
      }
      this.$confirm(`确定删除设备: ${device.label}?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        del(device.id).then(() => {
          this.$message({ type: 'success', message: '删除成功' });
          if (this.selectedDevice && this.selectedDevice.id === device.id) {
            this.selectedDevice = null;
          }
          this.getList(); // 刷新列表
        }).catch(() => {
          this.$message({ type: 'error', message: '删除失败' });
        });
      }).catch(() => {
        // 用户取消
      });
    },

    saveDevice () {
      this.$refs.deviceInfoForm.validate((valid) => {
        if (valid) {
          edit(this.deviceInfo).then(() => {
            this.$message({ type: 'success', message: '保存成功' });
            this.isShow = false;
            this.getList(); // 刷新整个树以保证数据一致性
          }).catch(() => {
            this.$message({ type: 'error', message: '保存失败' });
          });
        }
      });
    },

    closeDialog () {
      this.$refs.deviceInfoForm.resetFields();
      this.deviceInfo = { mac_address: '', alias_name: '', group_name: '' };
    },

    handleSelectionChange (val) {
      this.multipleSelection = val;
    },

    handleDeviceClickInGroup (row) {
      if (!row || !row.isClient) return;

      // 找到设备所属的分组名称
      let groupName = "Ungrouped";
      if (this.selectedDevice && !this.selectedDevice.isClient) {
        groupName = this.selectedDevice.label;
      }

      const macAddress = row.mac_address;

      // 更新选中状态
      this.selectedDevice = row;
      this.currentNodeKey = row.id;

      // 移除自动加载素材的逻辑，改为在切换到作业指导书tab时加载

      // 构建路由参数
      const newParams = {};
      if (groupName) {
        newParams.groupName = groupName;
      }
      if (macAddress) {
        newParams.macAddress = macAddress;
      }

      // 只有当路由参数发生变化时才进行跳转
      const needNavigation =
        this.$route.params.groupName !== newParams.groupName ||
        this.$route.params.macAddress !== newParams.macAddress;

      if (needNavigation) {
        this.$router.push({
          name: 'EquipmentCenter',
          params: newParams
        });
      }

      // 更新面包屑显示
      this.updateBreadcrumb();
    },

    async batchPush () {
      if (this.multipleSelection.length === 0) {
        this.$message.warning('请至少选择一个设备');
        return;
      }

      // 检查选中设备中是否有离线设备
      // const offlineDevices = this.multipleSelection.filter(device => !device.isOnline);
      // if (offlineDevices.length > 0) {
      //   const offlineNames = offlineDevices.map(device => device.alias_name || device.label).join('、');
      //   this.$message.error(`以下设备离线，无法发送：${offlineNames}`);
      //   return;
      // }

      const selections = this.multipleSelection;
      this.$message.info(`准备向 ${selections.length} 个设备进行批量推送...`);

      const promises = selections.map(device => {
        return batchPushMaterial({ id: device.id })
          .then(res => {
            if (res.code === 0) {
              return { success: true, device: device.label, message: '推送成功' };
            } else {
              return { success: false, device: device.label, message: res.msg || '推送失败' };
            }
          })
          .catch(err => {
            return { success: false, device: device.label, message: err.message || '推送请求异常' };
          });
      });

      const results = await Promise.all(promises);

      const successCount = results.filter(r => r.success).length;
      const failureCount = results.length - successCount;

      if (failureCount > 0) {
        const failureDetails = results.filter(r => !r.success)
          .map(r => `${r.device}: ${r.message}`)
          .join('\n');
        this.$alert(`<strong>${successCount}个设备推送成功, ${failureCount}个设备推送失败:</strong><br><pre>${failureDetails}</pre>`, '推送结果', {
          dangerouslyUseHTMLString: true,
          type: 'warning'
        });
      } else {
        this.$message.success(`所有 ${successCount} 个设备均已成功发送推送命令。`);
      }
    },

    formatTime (timestamp) {
      if (!timestamp) return '-';
      return this.$formatTimeStamp ? this.$formatTimeStamp(timestamp) : new Date(timestamp).toLocaleString();
    },

    handleNodeExpand (data, node) {

      // 只有分组节点才需要更新路由
      if (!data.isClient) {
        // 获取当前选中的设备MAC地址（如果有的话）
        let macAddress = null;
        if (this.selectedDevice && this.selectedDevice.isClient) {
          macAddress = this.selectedDevice.mac_address;
        }

        // 构建路由参数
        const newParams = {
          groupName: data.label
        };

        if (macAddress) {
          newParams.macAddress = macAddress;
        }

        // 只有当路由参数发生变化时才进行跳转
        const needNavigation =
          this.$route.params.groupName !== newParams.groupName ||
          this.$route.params.macAddress !== newParams.macAddress;

        if (needNavigation) {
          this.$router.push({ name: 'EquipmentCenter', params: newParams });
        }

        // 更新展开节点
        if (!this.expandedKeys.includes(data.id)) {
          this.expandedKeys.push(data.id);
        }
      }
    },

    handleNodeCollapse (data, node) {

      // 更新展开节点
      const index = this.expandedKeys.indexOf(data.id);
      if (index > -1) {
        this.expandedKeys.splice(index, 1);
      }
    },

    handleDeviceCommand (command) {
      if (!this.selectedDevice || !this.selectedDevice.isClient) return;
      switch (command) {
        case 'screenshot':
          this.screenshotDevice(this.selectedDevice);
          break;
        case 'reboot':
          this.remoteReboot(this.selectedDevice);
          break;
        case 'shutdown':
          this.remoteShutdown(this.selectedDevice);
          break;
      }
    },
    handleBatchUpload () {
      if (!this.selectedDevice || this.selectedDevice.isClient) {
        this.$message.warning('请先选择一个设备分组');
        return;
      }
      const input = document.createElement('input');
      input.type = 'file';
      input.webkitdirectory = true;
      input.onchange = e => {
        const files = e.target.files;
        if (files.length === 0) {
          return;
        }
        this.uploadFolder(files);
      };
      input.click();
    },

    otaUpgrade (device) {
      // 检查设备是否在线
      if (!device.isOnline) {
        this.$message.error('终端离线，无法发送');
        return;
      }

      this.$prompt('请输入OTA升级文件', 'OTA 升级', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /^(http?:\/\/)?([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$/,
        inputErrorMessage: '请输入正确的URL或者文件名'
      }).then(({ value }) => {
        const otaData = {
          type: 5,
          group_name: device.group_name,
          equipment_alias_name: device.alias_name,
          version: "1.0.0", // 示例版本，可根据实际情况修改
          ota_url: value
        };
        sendCommand(otaData).then(() => {
          this.$message.success(`已发送OTA升级命令到设备 ${device.label}`);
        }).catch(() => {
          this.$message.error('发送OTA升级命令失败');
        });
      }).catch(() => {
        this.$message.info('已取消OTA升级');
      });
    },

    remoteReboot (device) {
      // 检查设备是否在线
      if (!device.isOnline) {
        this.$message.error('终端离线，无法发送');
        return;
      }

      this.$confirm(`确定要远程重启设备: ${device.label}?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const commandData = {
          type: 4,
          group_name: device.group_name,
          equipment_alias_name: device.alias_name,
          command: "reboot"
        };
        sendCommand(commandData).then(() => {
          this.$message.success(`已发送重启命令到设备 ${device.label}`);
        }).catch(() => {
          this.$message.error('发送重启命令失败');
        });
      }).catch(() => { });
    },

    remoteShutdown (device) {
      // 检查设备是否在线
      if (!device.isOnline) {
        this.$message.error('终端离线，无法发送');
        return;
      }

      this.$confirm(`确定要远程关机设备: ${device.label}? 这可能导致设备离线无法操作。`, '警告', {
        confirmButtonText: '确定关机',
        cancelButtonText: '取消',
        type: 'error'
      }).then(() => {
        const commandData = {
          type: 4,
          group_name: device.group_name,
          equipment_alias_name: device.alias_name,
          command: "shutdown"
        };
        sendCommand(commandData).then(() => {
          this.$message.success(`已发送关机命令到设备 ${device.label}`);
        }).catch(() => {
          this.$message.error('发送关机命令失败');
        });
      }).catch(() => { });
    },

    async uploadFolder (files) {
      const formData = new FormData();
      formData.append('groupId', this.selectedDevice.group_id);
      formData.append('groupName', this.selectedDevice.group_name);

      this.isLoading = true;

      try {
        const dimensionPromises = Array.from(files).map(file => this.getFileDimensions(file));
        const dimensions = await Promise.all(dimensionPromises);

        for (let i = 0; i < files.length; i++) {
          formData.append('files', files[i]);
          formData.append('sizes', files[i].size);
          formData.append('source_widths', dimensions[i].width);
          formData.append('source_heights', dimensions[i].height);
        }

        const res = await batchUploadFiles(formData);
        if (res.code === 0 && res.data) {
          const { success_count, failure_count, failed_files } = res.data;
          if (failure_count > 0) {
            const failedFilesHtml = failed_files.map(f => `<li>${f}</li>`).join('');
            const message = `
              <p><strong>${success_count}个文件上传成功, ${failure_count}个文件未能匹配到设备。</strong></p>
              <p>未匹配成功的文件列表:</p>
              <ul style="list-style-type: disc; padding-left: 20px; max-height: 150px; overflow-y: auto;">
                ${failedFilesHtml}
              </ul>
            `;
            this.$alert(message, '上传结果', {
              dangerouslyUseHTMLString: true,
              type: 'warning'
            });
          } else {
            this.$message.success(`所有 ${success_count} 个文件均已成功上传并关联。`);
          }
          this.getList(); // 刷新列表
        } else {
          this.$message.error(res.msg || '上传失败');
        }
      } catch (error) {
        console.error("Upload error:", error);
        this.$message.error('上传过程中发生错误，详情请查看控制台。');
      } finally {
        this.isLoading = false;
      }
    },

    getFileDimensions (file) {
      return new Promise((resolve) => {
        if (file.type.startsWith('image/')) {
          const img = new Image();
          img.src = URL.createObjectURL(file);
          img.onload = () => {
            resolve({ width: img.width, height: img.height });
            URL.revokeObjectURL(img.src);
          };
          img.onerror = () => {
            resolve({ width: 0, height: 0 }); // 加载失败
            URL.revokeObjectURL(img.src);
          }
        } else if (file.type.startsWith('video/')) {
          const video = document.createElement('video');
          video.preload = 'metadata';
          video.src = URL.createObjectURL(file);
          video.onloadedmetadata = () => {
            resolve({ width: video.videoWidth, height: video.videoHeight });
            URL.revokeObjectURL(video.src);
          };
          video.onerror = () => {
            resolve({ width: 0, height: 0 }); // 加载失败
            URL.revokeObjectURL(video.src);
          }
        } else {
          resolve({ width: 0, height: 0 }); // 非图片视频文件
        }
      });
    },

    pushMaterial (item) {
      // 检查设备是否在线


      const fileTypeMap = {
        "image/jpeg": "image",
        "image/png": "image",
        "image/gif": "image",
        "application/pdf": "pdf",
        "video/mp4": "video",
        "video/webm": "video",
        "video/avi": "video",
        "vidoe/mpeg": "video",
        "video/ogg": "video",
        "application/vnd.ms-powerpoint": "ppt",
        "application/vnd.openxmlformats-officedocument.presentationml.presentation": "ppt",
        "application/msword": "word",
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document": "word",
        "application/vnd.ms-excel": "excel",
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": "excel",
      };

      const fileType = fileTypeMap[item.content_type] || 'other';

      const pushData = {
        type: 1,
        group_name: this.selectedDevice.group_name,
        list: [
          {
            download_file: "assets/media/" + item.path,
            file_type: fileType,
            equipment_alias_name: this.selectedDevice.alias_name,
            hash: item.hash || ''
          }
        ]
      };

      pushMaterial(pushData).then(() => {
        this.$message.success('素材推送命令已发送');
      }).catch(() => {
        this.$message.error('素材推送失败');
      });
    },

    delMaterial (material) {
      this.$confirm(`确定要删除素材 "${material.name}" 吗? 这将解除该素材与当前设备的关联。`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const disassociationData = {
          equipment_id: this.selectedDevice.id,
          material_id: material.id
        };
        disassociateMaterial(disassociationData).then(() => {
          this.$message.success('素材删除成功');
          // 只有当前在作业指导书管理tab时才刷新素材信息
          if (this.activeTab === 'material') {
            this.getDeviceMaterial(this.selectedDevice.id);
          }
        }).catch(() => {
          this.$message.error('素材删除失败');
        });
      }).catch(() => {
        // 用户取消
      });
    },

    batchDeleteMaterials () {
      if (this.selectedMaterialIds.length === 0) {
        this.$message.warning('请至少选择一个素材');
        return;
      }
      this.$confirm(`确定要删除选中的 ${this.selectedMaterialIds.length} 个素材吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 调用批量删除API
        disassociateMaterial({
          equipment_id: this.selectedDevice.id,
          material_ids: this.selectedMaterialIds
        }).then(() => {
          this.$message.success('批量删除成功');
          // 只有当前在作业指导书管理tab时才刷新素材信息
          if (this.activeTab === 'material') {
            this.getDeviceMaterial(this.selectedDevice.id); // 刷新素材列表
          }
          this.selectedMaterialIds = []; // 清空选项
        }).catch(() => {
          this.$message.error('批量删除失败');
        });
      }).catch(() => { });
    },

    toggleMaterialSelection (id) {
      const index = this.selectedMaterialIds.indexOf(id);
      if (index > -1) {
        this.selectedMaterialIds.splice(index, 1);
      } else {
        this.selectedMaterialIds.push(id);
      }
    },

    getTypeName (type) {
      const types = {
        1: "图片",
        2: "视频",
        3: "PDF",
        4: "PPT",
        5: "Word",
        6: "Excel",
        7: "TXT",
        8: "其他",
      };
      return types[type] || "未知";
    },
    formatFileSize (bytes) {
      if (bytes === 0) return "0 B";
      const k = 1024;
      const sizes = ["B", "KB", "MB", "GB", "TB"];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
    },
    getIconClass (item) {
      if (item.content_type.startsWith('video/')) {
        return 'el-icon-video-camera-solid';
      }
      if (item.content_type === 'application/pdf') {
        return 'el-icon-document';
      }
      return 'el-icon-folder';
    },
    isImage (item) {
      return item.content_type.startsWith('image/');
    },

    // 更新面包屑路径显示
    updateBreadcrumb () {
      // 触发computed属性更新
      this.$forceUpdate();
    },

    // 处理tab切换
    handleTabClick (tab) {
      // 当切换到作业指导书管理tab时，才加载素材数据
      if (tab.name === 'material' && this.selectedDevice && this.selectedDevice.isClient) {
        this.getDeviceMaterial(this.selectedDevice.id);
      }
      if (tab.name === 'log' && this.selectedDevice && this.selectedDevice.isClient) {
        this.getDeviceLogs();
      }
    },

    getDeviceLogs () {
      if (!this.selectedDevice || !this.selectedDevice.isClient) return;
      this.isLogLoading = true;
      getEquipmentLog({
        mac_address: this.selectedDevice.mac_address,
        page: 1,
        pageSize: 100
      })
        .then(res => {
          if (res.code === 0) {
            this.equipmentLogs = res.data.data || [];
          } else {
            this.$message.error('获取设备日志失败');
            this.equipmentLogs = [];
          }
        })
        .catch(() => {
          this.$message.error('获取设备日志失败');
          this.equipmentLogs = [];
        })
        .finally(() => {
          this.isLogLoading = false;
        });
    },
    clearLogs () {
      if (!this.selectedDevice || !this.selectedDevice.isClient) return;
      this.$confirm('此操作将永久删除该设备的日志, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        clearEquipmentLog({ mac_address: this.selectedDevice.mac_address }).then(() => {
          this.$message({
            type: 'success',
            message: '清空成功!'
          });
          this.getDeviceLogs();
        }).catch(() => {
          this.$message({
            type: 'error',
            message: '清空失败'
          });
        });
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消清空'
        });
      });
    },

    // 批量调节音量
    batchAdjustVolume () {
      if (this.multipleSelection.length === 0) {
        this.$message.warning('请至少选择一个设备');
        return;
      }

      // 检查选中设备中是否有离线设备
      const offlineDevices = this.multipleSelection.filter(device => !device.isOnline);
      if (offlineDevices.length > 0) {
        const offlineNames = offlineDevices.map(device => device.alias_name || device.label).join('、');
        this.$message.error(`以下设备离线，无法发送：${offlineNames}`);
        return;
      }

      // 显示音量调节弹窗
      this.isVolumeDialogVisible = true;
    },

    // 发送音量调节指令
    sendVolumeCommand () {
      const selections = this.multipleSelection;
      this.$message.info(`准备向 ${selections.length} 个设备发送音量调节指令...`);

      const promises = selections.map(device => {
        const commandData = {
          type: 6, // 调节音量指令类型为6
          group_name: device.group_name,
          equipment_alias_name: device.alias_name,
          volume: this.volumeValue, // 音量值
          mute: this.volumeValue === 0
        };

        return sendCommand(commandData)
          .then(res => {
            if (res.code === 0) {
              return { success: true, device: device.alias_name || device.label, message: '音量调节成功' };
            } else {
              return { success: false, device: device.alias_name || device.label, message: res.msg || '调节失败' };
            }
          })
          .catch(err => {
            return { success: false, device: device.alias_name || device.label, message: err.message || '请求异常' };
          });
      });

      Promise.all(promises).then(results => {
        const successCount = results.filter(r => r.success).length;
        const failureCount = results.length - successCount;

        if (failureCount > 0) {
          const failureDetails = results.filter(r => !r.success)
            .map(r => `${r.device}: ${r.message}`)
            .join('\n');
          this.$alert(`<strong>${successCount}个设备设置成功, ${failureCount}个设备设置失败:</strong><br><pre>${failureDetails}</pre>`, '设置结果', {
            dangerouslyUseHTMLString: true,
            type: 'warning'
          });
        } else {
          this.$message.success(`所有 ${successCount} 个设备均已成功调节音量。`);
        }

        // 关闭弹窗
        this.isVolumeDialogVisible = false;
      });
    },

    // 批量定时开关机
    async batchSchedulePower () {
      if (this.multipleSelection.length === 0) {
        this.$message.warning('请至少选择一个设备');
        return;
      }

      // 检查选中设备中是否有离线设备
      const offlineDevices = this.multipleSelection.filter(device => !device.isOnline);
      if (offlineDevices.length > 0) {
        const offlineNames = offlineDevices.map(device => device.alias_name || device.label).join('、');
        this.$message.error(`以下设备离线，无法发送：${offlineNames}`);
        return;
      }

      // 显示定时开关机弹窗
      this.isSchedulePowerDialogVisible = true;
      this.isSchedulePowerLoading = true;

      // 获取第一个选中设备的设置作为默认值
      const firstSelectedDevice = this.multipleSelection[0];
      try {
        const res = await getEquipmentPowerSchedules(firstSelectedDevice.id);
        if (res.code === 0 && res.data && res.data.length > 0) {
          this.schedulePowerForm.timeRanges = res.data.map(item => {
            let weekdays = {};
            try {
              weekdays = JSON.parse(item.weekdays);
            } catch (e) {
              // 解析失败则使用默认值
              weekdays = { monday: false, tuesday: false, wednesday: false, thursday: false, friday: false, saturday: false, sunday: false };
            }
            return {
              id: item.id,
              enabled: item.enabled,
              powerOnTime: item.power_on_time,
              powerOffTime: item.power_off_time,
              weekdays: weekdays,
              allWeekdays: Object.values(weekdays).every(day => day)
            };
          });
        } else {
          // 如果没有数据，则重置为默认值
          this.resetSchedulePowerForm();
        }
      } catch (error) {
        this.$message.error('获取定时开关机设置失败');
        this.resetSchedulePowerForm();
      } finally {
        this.isSchedulePowerLoading = false;
      }
    },

    addTimeRange () {
      this.schedulePowerForm.timeRanges.push({
        powerOnTime: '13:00',
        powerOffTime: '18:00',
        allWeekdays: false,
        enabled: true,
        weekdays: {
          monday: false,
          tuesday: false,
          wednesday: false,
          thursday: false,
          friday: false,
          saturday: false,
          sunday: false
        },
        id: Date.now() // Add a unique id for the key
      });
    },

    removeTimeRange (index) {
      if (this.schedulePowerForm.timeRanges.length > 1) {
        this.schedulePowerForm.timeRanges.splice(index, 1);
      } else {
        this.$message.warning('至少需要一个时间段');
      }
    },
    // 发送定时开关机指令
    async sendSchedulePowerCommand () {
      this.isSchedulePowerLoading = true;
      try {
        const selections = this.multipleSelection;
        this.$message.info(`准备为 ${selections.length} 个设备保存并应用定时开关机设置...`);

        const activeTimeRanges = this.schedulePowerForm.timeRanges.filter(t => t.enabled);

        if (activeTimeRanges.length === 0) {
          this.$message.warning('没有可用的时间设置');
          return;
        }

        const savePromises = selections.map(device => {
          const payload = {
            equipment_id: device.id,
            time_ranges: activeTimeRanges.map(tr => ({
              powerOnTime: tr.powerOnTime,
              powerOffTime: tr.powerOffTime,
              weekdays: tr.weekdays,
              enabled: tr.enabled
            }))
          };
          return saveEquipmentPowerSchedules(payload)
            .then(res => {
              if (res.code === 0) {
                return { success: true, device: device, message: '设置已保存' };
              } else {
                return { success: false, device: device, message: res.msg || '保存失败' };
              }
            })
            .catch(err => {
              return { success: false, device: device, message: err.message || '保存请求异常' };
            });
        });

        const saveResults = await Promise.all(savePromises);

        const successfulSaves = saveResults.filter(r => r.success);
        const failedSaves = saveResults.filter(r => !r.success);

        if (failedSaves.length > 0) {
          const failureDetails = failedSaves.map(r => `${r.device.alias_name || r.device.label}: ${r.message}`).join('\n');
          this.$alert(`<strong>${successfulSaves.length}个设备保存成功, ${failedSaves.length}个设备保存失败:</strong><br><pre>${failureDetails}</pre>`, '保存结果', {
            dangerouslyUseHTMLString: true,
            type: 'warning'
          });
        }

        if (successfulSaves.length === 0) {
          this.$message.error("所有设备都未能成功保存设置。");
          return;
        }

        // 对保存成功的设备发送MQTT指令
        const commandPromises = successfulSaves.map(({ device }) => {
          const perDeviceCommandPromises = activeTimeRanges.map(timeRange => {
            const weekdays = [
              timeRange.weekdays.monday ? 1 : 0,
              timeRange.weekdays.tuesday ? 1 : 0,
              timeRange.weekdays.wednesday ? 1 : 0,
              timeRange.weekdays.thursday ? 1 : 0,
              timeRange.weekdays.friday ? 1 : 0,
              timeRange.weekdays.saturday ? 1 : 0,
              timeRange.weekdays.sunday ? 1 : 0
            ];
            const [onHour, onMinute] = timeRange.powerOnTime.split(':');
            const [offHour, offMinute] = timeRange.powerOffTime.split(':');
            const powerOnTime = `{${onHour},${onMinute}}`;
            const powerOffTime = `{${offHour},${offMinute}}`;

            const commandData = {
              type: 7,
              group_name: device.group_name,
              equipment_alias_name: device.alias_name,
              powerOnTime: powerOnTime,
              powerOffTime: powerOffTime,
              weekdays: `{${weekdays.join(',')}}`
            };
            return sendCommand(commandData);
          });
          return Promise.all(perDeviceCommandPromises)
            .then(() => ({ success: true, deviceName: device.alias_name || device.label }))
            .catch(() => ({ success: false, deviceName: device.alias_name || device.label }));
        });

        const commandResults = await Promise.all(commandPromises);
        const successfulCommands = commandResults.filter(r => r.success).length;

        this.$message.success(`为 ${successfulSaves.length} 个设备成功保存设置，并向 ${successfulCommands} 个设备成功发送了更新指令。`);

        this.isSchedulePowerDialogVisible = false;
      } catch (error) {
        console.error("Failed to send schedule power command:", error);
        this.$message.error("操作失败，请查看控制台日志。");
      } finally {
        this.isSchedulePowerLoading = false;
      }
    },

    // 清除定时开关机
    async clearSchedulePowerCommand () {
      if (this.multipleSelection.length === 0) {
        this.$message.warning('请至少选择一个设备')
        return
      }

      // 检查选中设备中是否有离线设备
      const offlineDevices = this.multipleSelection.filter(device => !device.isOnline)
      if (offlineDevices.length > 0) {
        const offlineNames = offlineDevices.map(device => device.alias_name || device.label).join('、')
        this.$message.error(`以下设备离线，无法发送：${offlineNames}`)
        return
      }

      this.$confirm(`确定要清除选中的 ${this.multipleSelection.length} 个设备的定时开关机设置吗?`, '提示', {
        confirmButtonText: '确定清除',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const selections = this.multipleSelection
        this.$message.info(`准备向 ${selections.length} 个设备发送清除定时指令...`)

        const promises = selections.map(device => {
          const commandData = {
            type: 8, // 清除定时开关机指令
            group_name: device.group_name,
            equipment_alias_name: device.alias_name
          }

          return sendCommand(commandData)
            .then(res => {
              if (res.code === 0) {
                return { success: true, device: device.alias_name || device.label, message: '清除定时成功' }
              } else {
                return { success: false, device: device.alias_name || device.label, message: res.msg || '清除失败' }
              }
            })
            .catch(err => {
              return { success: false, device: device.alias_name || device.label, message: err.message || '请求异常' }
            })
        })

        const results = await Promise.all(promises)
        const successCount = results.filter(r => r.success).length
        const failureCount = results.length - successCount

        if (failureCount > 0) {
          const failureDetails = results.filter(r => !r.success)
            .map(r => `${r.device}: ${r.message}`)
            .join('\n')
          this.$alert(`<strong>${successCount}个设备清除成功, ${failureCount}个设备清除失败:</strong><br><pre>${failureDetails}</pre>`, '清除结果', {
            dangerouslyUseHTMLString: true,
            type: 'warning'
          })
        } else {
          this.$message.success(`所有 ${successCount} 个设备均已成功清除定时设置。`)
        }

        // 关闭弹窗
        this.isSchedulePowerDialogVisible = false
      }).catch(() => {
        this.$message.info('已取消清除定时操作')
      })
    },

    // 批量重启
    batchReboot () {
      if (this.multipleSelection.length === 0) {
        this.$message.warning('请至少选择一个设备');
        return;
      }

      const offlineDevices = this.multipleSelection.filter(device => !device.isOnline);
      if (offlineDevices.length > 0) {
        const offlineNames = offlineDevices.map(device => device.alias_name || device.label).join('、');
        this.$message.error(`以下设备离线，无法发送：${offlineNames}`);
        return;
      }

      this.$confirm(`确定要批量重启选中的 ${this.multipleSelection.length} 个设备吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const selections = this.multipleSelection;
        this.$message.info(`准备向 ${selections.length} 个设备发送重启命令...`);

        const promises = selections.map(device => {
          const commandData = {
            type: 4,
            group_name: device.group_name,
            equipment_alias_name: device.alias_name,
            command: "reboot"
          };
          return sendCommand(commandData)
            .then(res => {
              if (res.code === 0) {
                return { success: true, device: device.alias_name || device.label, message: '重启命令发送成功' };
              } else {
                return { success: false, device: device.alias_name || device.label, message: res.msg || '发送失败' };
              }
            })
            .catch(err => {
              return { success: false, device: device.alias_name || device.label, message: err.message || '请求异常' };
            });
        });

        const results = await Promise.all(promises);
        const successCount = results.filter(r => r.success).length;
        const failureCount = results.length - successCount;

        if (failureCount > 0) {
          const failureDetails = results.filter(r => !r.success)
            .map(r => `${r.device}: ${r.message}`)
            .join('\n');
          this.$alert(`<strong>${successCount}个设备发送成功, ${failureCount}个设备发送失败:</strong><br><pre>${failureDetails}</pre>`, '操作结果', {
            dangerouslyUseHTMLString: true,
            type: 'warning'
          });
        } else {
          this.$message.success(`所有 ${successCount} 个设备均已成功发送重启命令。`);
        }
      }).catch(() => {
        this.$message.info('已取消批量重启操作');
      });
    },

    // 批量关机
    batchShutdown () {
      if (this.multipleSelection.length === 0) {
        this.$message.warning('请至少选择一个设备');
        return;
      }

      const offlineDevices = this.multipleSelection.filter(device => !device.isOnline);
      if (offlineDevices.length > 0) {
        const offlineNames = offlineDevices.map(device => device.alias_name || device.label).join('、');
        this.$message.error(`以下设备离线，无法发送：${offlineNames}`);
        return;
      }

      this.$confirm(`确定要批量关机选中的 ${this.multipleSelection.length} 个设备吗? 这可能导致设备离线无法操作。`, '警告', {
        confirmButtonText: '确定关机',
        cancelButtonText: '取消',
        type: 'error'
      }).then(async () => {
        const selections = this.multipleSelection;
        this.$message.info(`准备向 ${selections.length} 个设备发送关机命令...`);

        const promises = selections.map(device => {
          const commandData = {
            type: 4,
            group_name: device.group_name,
            equipment_alias_name: device.alias_name,
            command: "shutdown"
          };
          return sendCommand(commandData)
            .then(res => {
              if (res.code === 0) {
                return { success: true, device: device.alias_name || device.label, message: '关机命令发送成功' };
              } else {
                return { success: false, device: device.alias_name || device.label, message: res.msg || '发送失败' };
              }
            })
            .catch(err => {
              return { success: false, device: device.alias_name || device.label, message: err.message || '请求异常' };
            });
        });

        const results = await Promise.all(promises);
        const successCount = results.filter(r => r.success).length;
        const failureCount = results.length - successCount;

        if (failureCount > 0) {
          const failureDetails = results.filter(r => !r.success)
            .map(r => `${r.device}: ${r.message}`)
            .join('\n');
          this.$alert(`<strong>${successCount}个设备发送成功, ${failureCount}个设备发送失败:</strong><br><pre>${failureDetails}</pre>`, '操作结果', {
            dangerouslyUseHTMLString: true,
            type: 'warning'
          });
        } else {
          this.$message.success(`所有 ${successCount} 个设备均已成功发送关机命令。`);
        }
      }).catch(() => {
        this.$message.info('已取消批量关机操作');
      });
    },
    fetchDeviceStatuses (devices) {
      console.log('devices', devices);

      if (!devices || devices.length === 0) {
        return;
      }
      const macAddresses = devices.map(d => d.mac_address);
      getLatestDeviceReport({ mac_addresses: macAddresses }).then(res => {
        if (res.code === 0 && res.data) {
          this.deviceReportStatus = res.data;
        }
      });
    },
    getDeviceStatus (macAddress) {
      const status = this.deviceReportStatus[macAddress];
      console.log(status, "statusstatusstatus");

      if (!status) {
        return this.$t('equipmentCenter.status.idle');
      }

      switch (status.report_type) {
        case 'document_preview_started':
        case 'webview_loaded':
          return this.$t('equipmentCenter.status.previewing');
        case 'file_download_started':
          return this.$t('equipmentCenter.status.downloading');
        case 'file_extraction_started':
          return this.$t('equipmentCenter.status.decompressing');
        case 'mqtt_command_received':
          return this.$t('equipmentCenter.status.executingCommand');
        case 'mqtt_command_processed':
          return this.$t('equipmentCenter.status.executingCommand');
        case 'file_upload_started':
          return this.$t('equipmentCenter.status.uploading');
        case 'current_open_file':
          let fileName = '';
          if (status.data && JSON.parse(status.data).file_name) {
            try {
              fileName = JSON.parse(status.data).file_name;
              console.log(fileName, "dfdfdfd");

            } catch (e) {
              console.log(e, "dfdfdfd")
              fileName = status.data.file_name; // Fallback to raw string if not valid JSON
            }
          }
          return this.$t('equipmentCenter.status.previewingFile') + fileName + '...';
        default:
          return this.$t('equipmentCenter.status.idle');
      }
    },
    getDeviceStatusContent (macAddress) {
      const status = this.deviceReportStatus[macAddress];
      if (!status) {
        return '设备暂无最新上报状态';
      }

      let fileName = '';
      if (status.data) {
        if (status.data.file_path) {
          fileName = status.data.file_path.split('/').pop();
        } else if (status.data.html_file_path) {
          fileName = status.data.html_file_path.split('/').pop();
        } else if (status.data.file_name) {
          fileName = status.data.file_name;
        }
      }

      const statusText = this.getDeviceStatus(macAddress);

      if (fileName) {
        return `${statusText}: ${fileName}`;
      }
      return statusText;
    },
    selectAllOffline () {
      if (this.selectedDevice && this.selectedDevice.children) {
        this.$refs.deviceTable.clearSelection();
        this.selectedDevice.children.forEach(device => {
          if (!device.isOnline) {
            this.$refs.deviceTable.toggleRowSelection(device, true);
          }
        });
      }
    },

    selectAllOnline () {
      if (this.selectedDevice && this.selectedDevice.children) {
        this.$refs.deviceTable.clearSelection();
        this.selectedDevice.children.forEach(device => {
          if (device.isOnline) {
            this.$refs.deviceTable.toggleRowSelection(device, true);
          }
        });
      }
    },
    toggleAllWeekdays (row) {
      const value = row.allWeekdays;
      row.weekdays.monday = value;
      row.weekdays.tuesday = value;
      row.weekdays.wednesday = value;
      row.weekdays.thursday = value;
      row.weekdays.friday = value;
      row.weekdays.saturday = value;
      row.weekdays.sunday = value;
    },
    updateAllWeekdayStatus (row) {
      const weekdays = Object.values(row.weekdays);
      row.allWeekdays = weekdays.every(day => day);
    },
    resetSchedulePowerForm () {
      this.schedulePowerForm.timeRanges = [
        {
          powerOnTime: '08:00',
          powerOffTime: '12:00',
          allWeekdays: false,
          enabled: true,
          weekdays: {
            monday: true,
            tuesday: true,
            wednesday: true,
            thursday: true,
            friday: true,
            saturday: false,
            sunday: false
          },
          id: Date.now()
        }
      ];
    },
    handleBatchDeleteFiles () {
      this.isBatchDeleteDialogVisible = true;
    },

    confirmBatchDelete () {
      if (!this.deleteDays) {
        this.$message.warning('请选择删除天数');
        return;
      }
      const params = {
        days: this.deleteDays
      };
      batchDeleteFiles(params).then(() => {
        this.$message.success(`删除成功`);
        this.isBatchDeleteDialogVisible = false;
      }).catch(() => {
        this.$message.error('删除失败');
      });
    },
  }
};
</script>

<style lang="scss">
/* 自定义树节点样式 */
.equipment-tree {
  .el-tree-node__content {
    height: 40px;

    &:hover {
      background-color: #f5f7fa;
    }
  }

  .el-tree-node.is-current>.el-tree-node__content {
    background-color: #ecf5ff;
    color: #409eff;
    font-weight: bold;
  }


  .custom-tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    padding-right: 8px;
  }

  .node-content {
    display: flex;
    align-items: center;
    flex: 1;
    overflow: hidden;
  }

  .node-icon {
    margin-right: 8px;
    font-size: 16px;
    color: #909399;

    &.online-icon {
      color: #67c23a;
    }
  }

  .node-label {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .status-tag {
    margin-left: 8px;
  }

  .node-extra {
    font-size: 12px;
    color: #909399;

    .online-count {
      color: #67c23a;
    }
  }
}

/* 表格行点击手势 */
.group-device-table .el-table__row {
  cursor: pointer;
}
</style>

<style lang="scss" scoped>
.equipment-center-page {
  height: calc(100vh - 84px);
  background-color: #f0f2f5;
}

.equipment-layout {
  height: 100%;
}

/* 面包屑导航 */
.equipment-breadcrumb {
  margin-bottom: 20px;
  padding: 10px 0;
  background-color: #fff;
  border-radius: 4px;
  padding-left: 10px;
}

/* 左侧设备树 */
.tree-aside {
  background: #fff;
  border-right: 1px solid #e6e6e6;
  display: flex;
  flex-direction: column;

  .tree-header {
    padding: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #e6e6e6;
    flex-shrink: 0;

    h3 {
      margin: 0;
      font-size: 16px;
    }
  }

  .tree-search {
    padding: 10px 15px;
    flex-shrink: 0;
  }

  .tree-container {
    flex: 1;
    overflow-y: auto;
  }
}

/* 右侧主内容区 */
.content-main {
  padding: 10px;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow-y: auto;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

/* 通用头部信息 */
.group-header-info,
.device-header-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;

  .group-title,
  .device-title {
    display: flex;
    align-items: center;
    gap: 12px;

    i {
      font-size: 24px;
      color: #409eff;
    }

    h2 {
      margin: 0;
      font-size: 22px;
    }
  }

  .header-actions {
    display: flex;
    gap: 10px;
  }
}

/* 统计卡片 */
.stats-cards {
  margin-bottom: 24px;

  .stat-card {
    background-color: #fff;
    border-radius: 8px;
    padding: 5px 10px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border: 1px solid #ebeef5;
    transition: box-shadow 0.3s;

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    }
  }

  .stat-icon-wrapper {
    font-size: 28px;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;

    &.total {
      background-color: #409eff;
    }

    &.online {
      background-color: #67c23a;
    }

    &.offline {
      background-color: #f56c6c;
    }
  }

  .stat-content {
    text-align: left;

    .stat-label {
      font-size: 14px;
      color: #909399;
      margin-bottom: 8px;
    }

    .stat-number {
      font-size: 28px;
      font-weight: 500;
      color: #303133;
    }
  }
}

/* 操作栏 */
.action-bar {
  margin-bottom: 20px;
}

/* 设备详情 */
.device-detail-view {
  .device-title .el-tag {
    margin-left: 8px;
  }

  .el-tabs {
    margin-top: 20px;
  }

  .device-info-panel {
    padding: 16px;
  }
}

/* 素材管理面板 */
.material-panel {
  .material-action-bar {
    margin-bottom: 16px;
  }

  .material-list-wrapper {
    max-height: calc(100vh - 400px);
    overflow-y: auto;
  }

  .material-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: 16px;
  }

  .material-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: 16px;
  }

  .material-card {
    margin-bottom: 8px;
    cursor: pointer;
    position: relative;
    border: 2px solid transparent;
    background: #fff;
    border-radius: 8px;
    transition: all 0.2s ease-in-out;
    border: 1px solid #ebeef5;
    border-radius: 8px;
    overflow: hidden;
    position: relative;

    .material-checkbox {
      position: absolute;
      top: 10px;
      left: 10px;
      z-index: 10;
    }

    &:hover {
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

      .card-actions {
        display: block;
      }
    }

    .card-preview {
      height: 120px;
      background-color: #f5f7fa;
      display: flex;
      align-items: center;
      justify-content: center;

      .el-image,
      video {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .file-icon-wrapper {
        font-size: 48px;
        color: #c0c4cc;
      }
    }

    .card-info {
      padding: 8px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      min-height: 90px; // 固定信息区高度

      .card-name {
        font-size: 14px;
        color: #303133;
        margin: 0 0 8px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .card-time {
        font-size: 12px;
        color: #909399;
        margin-top: auto; // 将时间推到底部
      }

      .card-details {
        font-size: 12px;
        color: #606266;
        margin-bottom: 8px;
        display: flex;
        flex-wrap: wrap; // 允许换行
        align-items: center;
        gap: 8px;

        .type-tag {
          padding: 2px 6px;
          border-radius: 4px;
          color: #fff;
          font-weight: 500;

          &.type-1 {
            background-color: #67c23a;
          }

          // 图片
          &.type-2 {
            background-color: #409eff;
          }

          // 视频
          &.type-3 {
            background-color: #f56c6c;
          }

          // PDF
          &.type-4 {
            background-color: #e6a23c;
          }

          // PPT
          &.type-5 {
            background-color: #409eff;
          }

          // Word
          &.type-6 {
            background-color: #67c23a;
          }

          // Excel
          &.type-7 {
            background-color: #909399;
          }

          // TXT
          &.type-8 {
            background-color: #909399;
          }

          // 其他
        }
      }
    }

    .card-actions {
      padding: 8px 12px;
      border-top: 1px solid #ebeef5;
      text-align: right;

      .danger-text {
        color: #f56c6c;
      }
    }
  }
}

/* 弹窗预览 */
.preview-dialog-content {
  position: relative;
  min-height: 450px;
}

.preview-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.5);
  color: #fff;

  i {
    font-size: 60px;
    margin-bottom: 20px;
  }
}

.file-icon-container {
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.file-icon {
  font-size: 40px;
  color: #c0c4cc;
}
</style>

<style lang="scss" scoped>
/* 分组内设备列表样式 */
.group-device-table {
  /* 设置一个计算高度，减去页面其他元素的大致高度 */
  height: calc(100vh - 420px);
  overflow-y: auto;
}

/* 音量调节弹窗样式 */
.volume-dialog-content {
  padding: 10px 0;

  .volume-slider {
    display: flex;
    align-items: center;
    margin-top: 20px;
    padding: 0 10px;

    .volume-icon {
      font-size: 20px;
      color: #606266;
      margin: 0 10px;

      &:first-child {
        margin-left: 0;
      }

      &:last-child {
        margin-right: 0;
      }
    }

    .el-slider {
      flex: 1;
    }
  }
}

/* 定时开关机弹窗样式 */
.schedule-power-dialog {
  .el-dialog__body {
    padding: 10px 20px;
  }

  .dialog-tip {
    margin-bottom: 15px;
    color: #606266;
  }

  .el-table {
    .el-checkbox {
      margin-right: 0;
    }

    .el-time-select {
      width: 120px;
    }
  }

  .dialog-footer {
    text-align: right;
  }
}
</style>
