<template>
  <div class="resource-page">
    <el-container class="resource-layout">
      <!-- 主内容区 -->
      <el-main class="content-main">
        <!-- 顶部操作栏 -->
        <div class="action-bar">
          <el-button type="primary" icon="el-icon-s-promotion" @click="openResourceSelectionDialog">
            {{ $t("resource.button.sendByRule") }}
          </el-button>
          <div class="actions-right">
            <el-form :model="form" ref="form" :inline="true" class="search-form">
              <el-form-item prop="name">
                <el-input v-model="form.name" :placeholder="$t('resource.form.namePlaceholder')" clearable
                  @clear="searchForm" @keyup.enter.native="searchForm"></el-input>
              </el-form-item>
              <el-form-item prop="packName">
                <el-input v-model="form.packName" :placeholder="$t('resource.form.packNamePlaceholder')" clearable
                  @clear="searchForm" @keyup.enter.native="searchForm"></el-input>
              </el-form-item>
              <el-form-item>
                <el-button @click="resetForm('form', 'getList')">{{ $t("public.reset") }}</el-button>
                <el-button type="primary" @click="searchForm">{{ $t("public.search") }}</el-button>
              </el-form-item>
            </el-form>
          </div>
        </div>

        <!-- 资源卡片展示区 -->
        <div v-loading="isLoading" class="resource-grid-container">
          <el-row :gutter="20">
            <el-col v-for="item in dataList" :key="item.id" :xs="12" :sm="8" :md="6" :lg="6" :xl="4">
              <el-card shadow="hover" class="resource-card">
                <div class="card-header">
                  <span class="card-title" :title="item.name">{{ item.name }}</span>
                  <el-popconfirm :title="$t('resource.table.deleteResource')" @confirm="del(item.id)">
                    <el-button type="text" size="small" slot="reference" icon="el-icon-delete" class="delete-btn"
                      style="color: #ff0000"></el-button>
                  </el-popconfirm>
                </div>
                <div class="card-body">
                  <p class="card-pack-name" :title="item.pack_name">
                    <i class="el-icon-box"></i> {{ item.pack_name }}
                  </p>
                  <time class="card-time">
                    <i class="el-icon-time"></i> {{ $formatTimeStamp(item.created_at, "YYYY-MM-DD HH:mm") }}
                  </time>
                </div>
                <div class="card-footer">
                  <el-button type="primary" plain size="mini" @click="sendAll(item.id)">
                    {{ $t("resource.button.sendAll") }}
                  </el-button>
                  <el-button type="success" plain size="mini" @click="openSendPartDialog(item)">
                    {{ $t("resource.button.sendPart") }}
                  </el-button>
                </div>
              </el-card>
            </el-col>
          </el-row>
          <el-empty v-if="!isLoading && dataList.length === 0"
            :description="$t('resource.empty.noResources')"></el-empty>
        </div>

        <!-- 分页 -->
        <el-pagination v-if="total > 0" background layout="total, sizes, prev, pager, next, jumper" :total="total"
          :page-size.sync="pageSize" :current-page.sync="pageNum" @size-change="handleSizeChange"
          @current-change="handleCurrentChange" class="pagination-footer">
        </el-pagination>
      </el-main>
    </el-container>

    <!-- 弹窗 -->
    <el-dialog :title="$t('resource.dialog.title.selectDevice')" :visible.sync="isShow" width="50%" @close="close">
      <el-input :placeholder="$t('resource.dialog.tip.filterPlaceholder')" v-model="filterText"></el-input>
      <el-tree class="filter-tree" :data="equipmentData" :props="defaultProps" :filter-node-method="filterNode"
        ref="tree" node-key="id" @node-click="handleNodeClick" :render-content="renderContent" show-checkbox
        @check-change="handleCheckChange" check-strictly :highlight-current="true">
      </el-tree>
      <span slot="footer" class="dialog-footer">
        <el-button @click="isShow = false">{{ $t("public.cancel") }}</el-button>
        <el-button type="primary" @click="confirm">{{ $t("public.confirm") }}</el-button>
      </span>
    </el-dialog>

    <!-- 新增的资源选择弹窗 -->
    <el-dialog :title="$t('resource.dialog.title.selectResource')" :visible.sync="isResourceSelectionDialogVisible"
      width="60%">
      <el-form :model="resourceForm" ref="resourceForm" label-width="80px" label-position="left" class="demo-ruleForm">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item :label="$t('resource.form.name')" prop="name">
              <el-input v-model="resourceForm.name" :placeholder="$t('resource.form.namePlaceholder')"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="$t('resource.form.packName')" prop="packName">
              <el-input v-model="resourceForm.packName"
                :placeholder="$t('resource.form.packNamePlaceholder')"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item :label="$t('resource.form.date')" prop="date">
              <el-date-picker v-model="resourceForm.date" value-format="timestamp" type="datetimerange"
                :range-separator="$t('public.to')" :start-placeholder="$t('public.startDate')"
                :end-placeholder="$t('public.endDate')">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" justify="end">
          <el-col :span="4">
            <el-button @click="resetForm('resourceForm', 'getResourceList')">
              {{ $t("public.reset") }}</el-button>
            <el-button type="primary" @click="
              resourcePageNum = 1;
            getResourceList();
            ">{{ $t("public.search") }}</el-button>
          </el-col>
        </el-row>
      </el-form>
      <el-table v-loading="isResourceLoading" ref="resourceTable" tooltip-effect="dark"
        @selection-change="handleResourceSelectionChange" :data="resourceList" style="width: 100%" border
        :row-key="(row) => row.id">
        <el-table-column type="selection" width="40" reserve-selection :selectable="selectable">
        </el-table-column>
        <el-table-column prop="num" label="序号" width="80" align="center">
          <template slot-scope="scope">
            {{ scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="name" :label="$t('resource.table.name')" align="center"></el-table-column>
        <el-table-column prop="pack_name" :label="$t('resource.table.pack_name')" align="center"></el-table-column>
        <el-table-column prop="created_at" :label="$t('resource.table.created_at')" align="center">
          <template slot-scope="scope">
            {{ $formatTimeStamp(scope.row.created_at) }}
          </template>
        </el-table-column>
      </el-table>
      <el-row :gutter="20" type="flex" justify="end">
        <el-pagination background @size-change="handleResourceSizeChange" @current-change="handleResourceCurrentChange"
          :current-page.sync="resourcePageNum" :page-sizes="[10, 20, 50]" :page-size="resourcePageSize"
          layout="total, prev, pager, next" :total="resourceTotal">
        </el-pagination>
      </el-row>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleCancelInResourceDialog">{{
          $t("public.cancel")
        }}</el-button>
        <el-button type="primary" @click="handleResourceSelectionConfirm">{{
          $t("resource.button.nextStep")
        }}</el-button>
      </span>
    </el-dialog>

    <el-dialog :title="$t('resource.dialog.title.group_name')" :visible.sync="isGroupSelectionDialogVisible"
      width="60%">
      <el-form :model="groupForm" ref="groupForm" label-width="80px" label-position="left" class="demo-ruleForm">
        <!-- 这里如果后续有搜索需求可添加输入框等，目前没有搜索功能则先空着 -->
      </el-form>
      <el-table v-loading="isGroupLoading" ref="groupTable" tooltip-effect="dark"
        @selection-change="handleGroupSelectionChange" :data="groupList" style="width: 100%" border
        :row-key="(row) => row.id">
        <el-table-column type="selection" width="40" reserve-selection :selectable="selectable">
        </el-table-column>
        <el-table-column prop="num" :label="$t('resource.table.num')" width="80" align="center">
          <template slot-scope="scope">
            {{ scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="name" :label="$t('resource.dialog.title.group_name')" align="center"></el-table-column>
      </el-table>
      <el-row :gutter="20" type="flex" justify="end">
        <el-pagination background @size-change="handleGroupSizeChange" @current-change="handleGroupCurrentChange"
          :current-page.sync="groupPageNum" :page-sizes="[10, 20, 50]" :page-size="groupPageSize"
          layout="total, prev, pager, next" :total="groupTotal">
        </el-pagination>
      </el-row>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleCancelInGroupDialog">{{ $t("public.cancel") }}</el-button>
        <el-button type="primary" @click="handleGroupNextStep">{{
          $t("resource.button.nextStep")
        }}</el-button>
      </span>
    </el-dialog>

    <el-dialog :title="$t('resource.dialog.title.inputDevice')" :visible.sync="isResourceDisplayDialogVisible"
      width="60%">
      <div v-if="selectedResources.length > 0">
        <el-form class="vertical-form">
          <el-form-item v-for="resource in selectedResources" :key="resource.id">
            <template #label>
              <div class="resource-label">{{ $t('resource.form.resourceNameLabel') }}{{ resource.name }}</div>
            </template>
            <el-input v-model="resource.inputValue" :placeholder="$t('resource.dialog.tip.deviceAliasHint')"
              class="input-block"></el-input>
          </el-form-item>
        </el-form>
      </div>
      <div v-else>{{ $t("resource.dialog.tip.noSelectedResources") }}</div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleCancelInGroupResourceDialog">
          {{ $t("public.cancel") }}
        </el-button>
        <el-button @click="sendRule" type="primary">
          {{ $t("public.confirm") }}
        </el-button>
      </span>
    </el-dialog>

    <el-dialog :title="$t('resource.dialog.title.selectDevice')" :visible.sync="isShowForNextStep" width="80%"
      @close="closeForNextStep">
      <el-form :model="equipmentForm" ref="equipmentForm" label-width="80px" label-position="left"
        class="demo-ruleForm">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item :label="$t('resource.form.deviceName')" prop="name">
              <el-input v-model="equipmentForm.name"
                :placeholder="$t('resource.form.deviceNamePlaceholder')"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="$t('resource.form.deviceId')" prop="mac_address">
              <el-input v-model="equipmentForm.mac_address"
                :placeholder="$t('resource.form.deviceIdPlaceholder')"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('resource.form.date')" prop="date">
              <el-date-picker v-model="equipmentForm.date" value-format="timestamp" type="datetimerange"
                :range-separator="$t('public.to')" :start-placeholder="$t('public.startDate')"
                :end-placeholder="$t('public.endDate')"></el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" justify="end">
          <el-col :span="4">
            <el-button @click="resetForm('equipmentForm', 'getEquipmentList')">
              {{ $t("public.reset") }}</el-button>
            <el-button type="primary" @click="
              pageNum1 = 1;
            getEquipmentList();
            ">{{ $t("public.search") }}</el-button>
          </el-col>
        </el-row>
      </el-form>
      <el-table v-loading="isLoading" ref="singleTable" tooltip-effect="dark" @selection-change="handleSelectionChange"
        :data="equipmentList" style="width: 100%" border :row-key="(row) => row.id" :row-class-name="tableRowClassName"
        :cell-class-name="tableCellClassName">
        <el-table-column type="selection" width="40" reserve-selection :selectable="selectable"></el-table-column>
        <el-table-column prop="num" :label="$t('resource.table.num')" width="80" align="center">
          <template slot-scope="scope">
            {{ scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="name" :label="$t('resource.form.deviceName')" align="center"></el-table-column>
        <el-table-column prop="mac_address" :label="$t('resource.form.mac_address')" align="center"></el-table-column>
        <el-table-column prop="created_at" :label="$t('resource.table.created_at')" align="center">
          <template slot-scope="scope">
            {{ $formatTimeStamp(scope.row.created_at) }}
          </template>
        </el-table-column>
      </el-table>
      <el-row :gutter="20" type="flex" justify="end">
        <el-pagination background @size-change="handleEquipmentSizeChange"
          @current-change="handleEquipmentCurrentChange" :current-page.sync="pageNum1" :page-sizes="[10, 20, 50]"
          :page-size="pageSize1" layout="total, prev, pager, next" :total="total1"></el-pagination>
      </el-row>
      <span slot="footer" class="dialog-footer">
        <el-button @click="isShowForNextStep = false">{{
          $t("public.cancel")
        }}</el-button>
        <el-button type="primary" @click="confirmForNextStep()">{{
          $t("public.confirm")
        }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  getList,
  getEquipmentList,
  del,
  send,
  getGroupList,
} from "@/api/resource.js";

export default {
  name: "Resource",
  watch: {
    filterText (val) {
      this.$refs.tree.filter(val);
    },
  },
  data () {
    return {
      isLoading: false,
      dataList: [],
      equipmentData: [],
      defaultProps: {
        children: 'children',
        label: 'label',
        isClient: 'isClient'
      },
      selectedNode: null,
      disabledNodes: [], // 存储被禁用的节点 ID
      equipmentList: [],
      resourceList: [],

      pageNum: 1,
      pageSize: 12,
      total: 0,

      // 新增分组相关数据
      groupList: [],
      groupPageNum: 1,
      groupPageSize: 10,
      groupTotal: 0,
      isGroupLoading: false,
      selectedGroupIds: [],
      isGroupSelectionDialogVisible: false,

      //部分发送存储Id
      sendPartId: 0,

      pageNum1: 1,
      pageSize1: 5,
      total1: 0,
      resourcePageNum: 1,
      resourcePageSize: 10,
      resourceTotal: 0,
      form: {
        name: "",
        packName: "",
        date: [],
      },
      equipmentForm: {
        name: "",
        mac_address: "",
        date: [],
      },
      resourceForm: {
        name: "",
        packName: "",
        date: [],
      },
      groupForm: {
        name: "",
        packName: "",
        date: [],
      },
      id: "",
      currentNodeKey: null,
      equipment_name: "",
      equipment_id_str: "",
      pack_name: "",
      isShow: false,
      isEdit: false,
      isResourceLoading: false,
      selectedEquipmentIds: [],
      isResourceSelectionDialogVisible: false,
      isShowForNextStep: false,
      selectedResourceIds: [],
      selectedResources: [],
      isResourceDisplayDialogVisible: false,
      filterText: "",
    };
  },
  created () {
    this.getList();
  },
  methods: {
    openSendPartDialog (item) {
      this.isShow = true;
      this.sendPartId = item.id;
      this.pack_name = item.name;
      this.pageNum1 = 1;
      this.getEquipmentList();
    },
    // 自定义节点渲染
    renderContent (h, { node, data }) {
      const isClient = data.isClient;
      const isDisabled = !isClient || (this.selectedNode && this.selectedNode.id !== data.id);

      return h(
        'span',
        {
          style: {
            color: isDisabled ? '#7e7e7e' : '#333',
            cursor: isDisabled ? 'not-allowed' : 'pointer',
            pointerEvents: isDisabled ? 'none' : 'auto'
          },
          class: {
            'disabled-node': isDisabled,
            'enabled-node': !isDisabled && isClient
          }
        },
        node.label
      );
    },
    handleCheckChange (node, checked) {
      if (checked) {
        const checkedNodes = this.$refs.tree.getCheckedNodes();
        const leafNodes = checkedNodes.filter(n => n.isClient);

        if (leafNodes.length > 0) {
          const lastSelected = leafNodes[leafNodes.length - 1];
          this.$refs.tree.setCheckedNodes([lastSelected], false);
          this.selectedNode = lastSelected;
          this.disableOtherNodes(lastSelected.id);
        }
      } else {
        this.selectedNode = null;
      }
    },
    handleNodeClick (node) {
      if (!node.isClient) return;

      if (this.selectedNode && this.selectedNode.id === node.id) {
        this.selectedNode = null;
        this.disabledNodes = [];
        this.$refs.tree.setCheckedKeys([]);
      } else {
        this.selectedNode = node;
        this.$refs.tree.setCheckedKeys([node.id]);
        this.disableOtherNodes(node.id);
      }
    },
    disableOtherNodes (currentId) {
      const getAllLeafIds = (nodes) => {
        let ids = [];
        nodes.forEach(node => {
          if (node.isClient && node.id !== currentId) {
            ids.push(node.id);
          }
          if (node.children) {
            ids = ids.concat(getAllLeafIds(node.children));
          }
        });
        return ids;
      };
      this.disabledNodeIds = getAllLeafIds(this.equipmentData).filter(id => id !== currentId);
    },
    filterNode (value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    selectable (row, index) {
      return row.status !== "disabled";
    },
    tableRowClassName ({ row, rowIndex }) {
      if (this.selectedEquipmentIds.includes(row.id)) {
        return "selected-row";
      }
      return "";
    },
    tableCellClassName ({ row, column, rowIndex, columnIndex }) {
      if (
        column.property === "name" &&
        this.selectedEquipmentIds.includes(row.id)
      ) {
        return "selected-cell";
      }
      return "";
    },
    getList () {
      this.isLoading = true;
      getList({
        page: this.pageNum,
        pageSize: this.pageSize,
        name: this.form.name,
        packName: this.form.packName,
        created_at_start:
          this.form.date && this.form.date.length > 0 ? this.form.date[0] / 1000 : "",
        created_at_end:
          this.form.date && this.form.date.length > 0 ? this.form.date[1] / 1000 : "",
      }).then((res) => {
        if (res.code == 0) {
          this.dataList = res.data.data;
          this.total = res.data.total;
        }
      }).finally(() => {
        this.isLoading = false;
      });
    },
    getGroupList () {
      this.isGroupLoading = true;
      getGroupList({
        page: this.groupPageNum,
        pageSize: this.groupPageSize,
      }).then((res) => {
        if (res.code === 0) {
          this.groupList = res.data.data;
          this.groupTotal = res.data.total;
        }
      }).finally(() => {
        this.isGroupLoading = false;
      });
    },
    handleGroupSelectionChange (val) {
      this.selectedGroupIds = val.map(item => item.id);
    },
    handleGroupSelectionConfirm () {
      this.isGroupSelectionDialogVisible = false;
      this.$message({
        type: "info",
        message: this.$t("resource.dialog.message.selectedGroups", {
          count: this.selectedGroupIds.length,
        }),
      });
    },
    handleGroupSizeChange (val) {
      this.groupPageNum = 1;
      this.groupPageSize = val;
      this.getGroupList();
    },
    handleGroupCurrentChange (val) {
      this.groupPageNum = val;
      this.getGroupList();
    },
    getResourceList () {
      this.isResourceLoading = true;
      getList({
        page: this.resourcePageNum,
        pageSize: this.resourcePageSize,
        name: this.resourceForm.name,
        packName: this.resourceForm.packName,
        created_at_start:
          this.resourceForm.date.length > 0
            ? this.resourceForm.date[0] / 1000
            : "",
        created_at_end:
          this.resourceForm.date.length > 0
            ? this.resourceForm.date[1] / 1000
            : "",
      }).then((res) => {
        if (res.code === 0) {
          this.resourceList = res.data.data;
          this.resourceTotal = res.data.total;
        }
      }).finally(() => {
        this.isResourceLoading = false;
      });
    },
    closeForNextStep () {
      this.pack_name = "";
      this.equipment_name = "";
      this.selectedEquipmentIds = [];
      if (this.$refs.equipmentForm) {
        this.$refs.equipmentForm.resetFields();
      }
    },
    confirmForNextStep () {
      if (!this.equipment_id_str) {
        return this.$message.warning(this.$t("resource.dialog.tip.selectAtLeastOneDevice"));
      }
      this.$confirm(
        this.$t("resource.confirm.sendToDevices"),
        this.$t("resource.confirm.title"),
        {
          confirmButtonText: this.$t("public.confirm"),
          cancelButtonText: this.$t("public.cancel"),
          type: "warning",
        }
      ).then(() => {
        this.sendPart(this.equipment_id_str);
        this.equipment_id_str = "";
        this.sendPartId = 0;
      }).catch(() => { });
    },
    handleResourceSizeChange (val) {
      this.resourcePageNum = 1;
      this.resourcePageSize = val;
      this.getResourceList();
    },
    handleResourceCurrentChange (val) {
      this.resourcePageNum = val;
      this.getResourceList();
    },
    getEquipmentList () {
      getEquipmentList().then((res) => {
        if (res.code == 0) {
          this.equipmentData = res.data;
        }
      });
    },
    confirm () {
      let equipment_name = this.$refs.tree.getCheckedNodes();
      this.equipment_id_str = equipment_name.map((item) => item.id).join(",");

      if (!this.equipment_id_str) {
        return this.$message.warning(this.$t("resource.dialog.tip.selectAtLeastOneDevice"));
      }
      this.$confirm(
        this.$t("resource.confirm.sendToDevices"),
        this.$t("resource.confirm.title"),
        {
          confirmButtonText: this.$t("public.confirm"),
          cancelButtonText: this.$t("public.cancel"),
          type: "warning",
        }
      ).then(() => {
        this.sendPart(this.equipment_id_str);
        this.equipment_id_str = "";
        this.sendPartId = 0;
        this.selectedNode = null;
        this.disabledNodes = [];
        if (this.$refs.tree) {
          this.$refs.tree.setCheckedKeys([]);
        }
        this.isShow = false;
      }).catch(() => { });
    },
    handleSelectionChange (val) {
      this.selectedEquipmentIds = val.map(item => item.id);
      this.equipment_id_str = this.selectedEquipmentIds.join(",");
    },
    sendAll (id) {
      const data = {
        type: 2,
        list: [{ resource_id: id }],
      };
      send(data).then((res) => {
        if (res.code === 0) {
          this.$message.success(this.$t("resource.dialog.message.sendSuccess"));
          this.getList();
        }
      });
    },
    sendRule () {
      const groupIdStr = this.selectedGroupIds.join(",");
      const list = this.selectedResources.map((resource) => ({
        resource_id: resource.id,
        equipment_name: resource.inputValue,
      }));

      const data = {
        type: 3,
        group_id: groupIdStr,
        list: list,
      };

      send(data).then((res) => {
        if (res.code === 0) {
          this.$message.success(this.$t("resource.dialog.message.sendByRuleSuccess"));
          this.selectedResourceIds = [];
          this.selectedResources = [];
          this.selectedGroupIds = [];
          if (this.$refs.resourceTable) this.$refs.resourceTable.clearSelection();
          if (this.$refs.groupTable) this.$refs.groupTable.clearSelection();
          this.isResourceDisplayDialogVisible = false;
        } else {
          this.$message.error(this.$t("resource.dialog.message.sendFailed"));
        }
      }).catch((error) => {
        this.$message.error(this.$t("resource.dialog.message.requestError"));
        console.error(error);
      });
    },
    sendPart (equipment_id) {
      const data = {
        type: 1,
        list: [{
          resource_id: this.sendPartId,
          equipment_id: equipment_id,
        }],
      };
      send(data).then((res) => {
        if (res.code === 0) {
          this.$message.success(this.$t("resource.dialog.message.sendSuccess"));
          this.getList();
        }
      });
    },
    del (id) {
      del(id).then((res) => {
        this.$message.success(this.$t("public.deleteSuccess"));
        this.getList();
      });
    },
    close () {
      this.pack_name = "";
      this.equipment_name = "";
      this.selectedEquipmentIds = [];
      if (this.$refs.equipmentForm) this.$refs.equipmentForm.resetFields();
      if (this.$refs.singleTable) this.$refs.singleTable.clearSelection();
    },
    searchForm () {
      this.pageNum = 1;
      this.getList();
    },
    resetForm (form, name) {
      this.pageNum = 1;
      if (this.$refs[form]) {
        this.$refs[form].resetFields();
      }
      this[name]();
    },
    handleSizeChange (val) {
      this.pageNum = 1;
      this.pageSize = val;
      this.getList();
    },
    handleCurrentChange (val) {
      this.pageNum = val;
      this.getList();
    },
    handleEquipmentSizeChange (val) {
      this.pageNum1 = 1;
      this.pageSize1 = val;
      this.getEquipmentList();
    },
    handleEquipmentCurrentChange (val) {
      this.pageNum1 = val;
      this.getEquipmentList();
    },
    handleCancelInResourceDialog () {
      this.isResourceSelectionDialogVisible = false;
      this.selectedResourceIds = [];
      this.selectedResources = [];
      if (this.$refs.resourceTable) this.$refs.resourceTable.clearSelection();
      if (this.$refs.resourceForm) this.$refs.resourceForm.resetFields();
    },
    openResourceSelectionDialog () {
      this.isResourceSelectionDialogVisible = true;
      this.resourcePageNum = 1;
      this.getResourceList();
    },
    handleResourceSelectionChange (val) {
      this.selectedResources = val;
      this.selectedResourceIds = val.map(item => item.id);
    },
    handleGroupNextStep () {
      if (this.selectedGroupIds.length === 0) {
        return this.$message.warning(this.$t("resource.dialog.tip.selectAtLeastOneGroup"));
      }
      this.isGroupSelectionDialogVisible = false;
      this.isResourceDisplayDialogVisible = true;
    },
    handleCancelInGroupResourceDialog () {
      this.handleCancelInResourceDialog();
      this.isResourceDisplayDialogVisible = false;
    },
    handleCancelInGroupDialog () {
      this.isGroupSelectionDialogVisible = false;
      this.selectedGroupIds = [];
      this.selectedResourceIds = [];
      if (this.$refs.groupTable) this.$refs.groupTable.clearSelection();
      this.handleCancelInResourceDialog();
    },
    handleResourceSelectionConfirm () {
      if (this.selectedResourceIds.length === 0) {
        return this.$message.warning(this.$t("resource.dialog.tip.selectAtLeastOneResource"));
      }
      this.isResourceSelectionDialogVisible = false;
      this.isGroupSelectionDialogVisible = true;
      this.groupPageNum = 1;
      this.getGroupList();
    },
  },
};
</script>

<style lang="scss" scoped>
.resource-page {
  height: calc(100vh - 84px);
  background-color: #f0f2f5;
}

.resource-layout {
  height: 100%;
}

.content-main {
  padding: 20px;
  display: flex;
  flex-direction: column;
}

.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  background-color: #fff;
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);

  .actions-right {
    display: flex;
    align-items: center;
    gap: 15px;
  }

  .search-form .el-form-item {
    margin-bottom: 0;
  }
}

.resource-grid-container {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.resource-card {
  margin-bottom: 20px;
  transition: all 0.2s ease-in-out;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.1);
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 10px;
    border-bottom: 1px solid #ebeef5;

    .card-title {
      font-size: 16px;
      font-weight: bold;
      color: #303133;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .delete-btn {
      font-size: 16px;
      padding: 5px;
    }
  }

  .card-body {
    padding: 15px 0;
    font-size: 14px;
    color: #606266;

    p,
    time {
      margin: 0 0 10px;
      display: flex;
      align-items: center;

      i {
        margin-right: 8px;
        color: #909399;
      }
    }

    .card-pack-name {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  .card-footer {
    padding-top: 10px;
    border-top: 1px solid #ebeef5;
    text-align: right;
  }
}

.pagination-footer {
  margin-top: 20px;
  text-align: right;
  background-color: #fff;
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.filter-tree {
  margin-top: 10px;
}

.vertical-form {
  max-width: 800px;
  margin: 0 auto;
}

.el-form-item {
  margin-bottom: 24px;
}

.resource-label {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 8px;
}

.input-block {
  width: 100%;
  max-width: 500px;
}
</style>

<style>
.el-tree-node__content .el-checkbox {
  display: none;
}

.el-tree-node__children .el-tree-node__content .el-checkbox {
  display: block;
}
</style>