package repository

import (
	"context"
	v1 "esop/api/v1"
	"esop/internal/model"
	esop_mqtt "esop/internal/mqtt"
	"fmt"

	mqtt "github.com/mochi-mqtt/server/v2"
	"go.uber.org/zap"

	//"github.com/mutecomm/go-sqlcipher/v4"
	sqlcipher "github.com/gdanko/gorm-sqlcipher"
	"github.com/spf13/viper"
	"gorm.io/driver/mysql"

	//"gorm.io/driver/sqlite"
	//"github.com/glebarez/sqlite"
	"gorm.io/gorm"
	"moul.io/zapgorm2"
)

const ctxTxKey = "MfKey"

type Repository struct {
	db *gorm.DB
	//rdb    *redis.Client
	logger *zap.Logger
	config *viper.Viper
	mqtt   esop_mqtt.InternalClient
}

func NewRepository(config *viper.Viper, logger *zap.Logger, db *gorm.DB, mqtt esop_mqtt.InternalClient) *Repository {
	return &Repository{
		db:     db,
		config: config,
		logger: logger,
		mqtt:   mqtt,
	}
}

//func NewDb(conf *viper.Viper, l *log.Logger) *gorm.DB {
//	// TODO: init db
//	logger := zapgorm2.New(l.Logger)
//	logger.SetAsDefault()
//	db, err := gorm.Open(mysql.Open(conf.GetString("data.db.dsn")), &gorm.Config{
//		Logger: logger,
//	})
//	if err != nil {
//		panic(err)
//	}
//	db = db.Debug()
//
//	return db
//	// return &gorm.DB{}
//}

func NewDb(conf *viper.Viper, l *zap.Logger) *gorm.DB {
	logger := zapgorm2.New(l)
	logger.SetAsDefault()
	key := "MufongEsop666"
	pageSize := 4096
	// 获取数据库驱动和 DSN 配置
	driver := conf.GetString("data.db.driver")
	dsn := conf.GetString("data.db.dsn")

	var dialector gorm.Dialector

	// 根据驱动类型选择合适的 dialector
	switch driver {
	case "mysql":
		dialector = mysql.Open(dsn)
	case "sqlite":
		// 确保 SQLite DSN 以 "file:" 开头
		//if !strings.HasPrefix(dsn, "file:") {
		//	dsn = dsn
		//}
		dbname := fmt.Sprintf(dsn, key, pageSize)
		fmt.Println(dbname, "dbnamedbnamedbname")
		dialector = sqlcipher.Open(dbname)

	case "postgres":
		// 可以添加其他驱动支持
		// dialector = postgres.Open(dsn)
	default:
		panic(fmt.Sprintf("不支持的数据库驱动: %s", driver))
	}

	// 使用选定的 dialector 打开数据库
	db, err := gorm.Open(dialector, &gorm.Config{
		Logger: logger,
	})

	if err != nil {
		panic(fmt.Sprintf("无法连接数据库: %v", err))
	}
	db.Exec("PRAGMA temp_store=2")
	db.Exec("PRAGMA page_size=4096")
	db.Exec("PRAGMA journal_mode=WAL")
	db.Exec("PRAGMA synchronous=NORMAL")
	db.Exec("PRAGMA journal_size_limit=-1")
	db = db.Debug()
	// Auto-migrate server_license table
	_ = db.AutoMigrate(&model.ServerLicense{})
	return db
}

func NewInternalMqttClient(server *mqtt.Server) esop_mqtt.InternalClient {
	return esop_mqtt.NewInternalMqttClient(server)
}

func (r *Repository) Paginate(req *v1.PagingRequest) func(db *gorm.DB) *gorm.DB {

	return func(db *gorm.DB) *gorm.DB {
		page := req.Page
		if page == 0 {
			page = 1
		}
		pageSize := req.PageSize
		switch {
		case pageSize > 100:
			pageSize = 100
		case pageSize <= 0:
			pageSize = 20
		}
		offset := (page - 1) * pageSize
		return db.Offset(offset).Limit(pageSize)
	}
}
func (r *Repository) DB(ctx context.Context) *gorm.DB {
	v := ctx.Value(ctxTxKey)
	if v != nil {
		if tx, ok := v.(*gorm.DB); ok {
			return tx
		}
	}

	return r.db.WithContext(ctx)
}
