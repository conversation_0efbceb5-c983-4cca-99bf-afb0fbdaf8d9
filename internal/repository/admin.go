package repository

import (
	"context"
	"errors"
	v1 "esop/api/v1"
	"esop/internal/model"
	"time"

	"github.com/gin-gonic/gin"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

type AdminRepository interface {
	FirstById(id int64) (*model.Admin, error)
	GetAdminList(ctx *gin.Context, req v1.AdminListRequest) (int64, []*v1.AdminData, error)
	Delete(ctx *gin.Context, id int) error
	Create(ctx *gin.Context, v *v1.SaveAdminListRequest) error
	EditUsing(ctx context.Context, req v1.UpdateAdminUsingRequest) error
	GetByAccount(ctx context.Context, account string) (*model.Admin, error)
	GetByNameAndId(ctx context.Context, id int, name string) (*model.Admin, error)
	Edit(ctx *gin.Context, id int, req v1.SaveAdminListRequest) error
}

func NewAdminRepository(repository *Repository) AdminRepository {

	return &adminRepository{
		Repository:             repository,
		OperationLogRepository: NewOperationLogRepository(repository),
	}
}

type adminRepository struct {
	*Repository
	OperationLogRepository OperationLogRepository
}

func (r *adminRepository) FirstById(id int64) (*model.Admin, error) {
	var admin model.Admin
	// TODO: query db
	return &admin, nil
}
func (r *adminRepository) GetAdminList(ctx *gin.Context, request v1.AdminListRequest) (int64, []*v1.AdminData, error) {
	var (
		adminList []*v1.AdminData
		total     int64 = 10
		err       error
	)

	var db = r.DB(ctx).Table("admin").
		Where("is_deleted = 1").
		Order("id DESC").
		Select("*").
		Count(&total)

	if request.Name != "" {
		db.Where("name like ?", "%"+request.Name+"%")
	}
	if request.Account != "" {
		db.Where("account like ?", "%"+request.Account+"%")
	}

	if request.CreatedAtStart != 0 {
		db.Where("created_at >=  ?", request.CreatedAtStart)
	}
	if request.CreatedAtEnd != 0 {
		db.Where("created_at <= ?", request.CreatedAtEnd)
	}
	err = db.Scopes(r.Paginate(&request.PagingRequest)).Find(&adminList).Error

	if err != nil {
		return total, nil, err
	}
	return total, adminList, nil

}
func (r *adminRepository) Delete(ctx *gin.Context, id int) error {
	now := time.Now().Unix()
	var update = &model.Admin{
		IsDeleted: 2,
		CreatedAt: now,
		UpdatedAt: now,
	}
	if err := r.DB(ctx).Table("admin").Where("id = ?", id).Updates(update).Error; err != nil {
		return err
	}
	r.OperationLogRepository.AddOperationLogData(ctx, id, v1.ActionDelete, v1.ModuleAdmin)
	return nil

}
func (r *adminRepository) EditUsing(ctx context.Context, req v1.UpdateAdminUsingRequest) error {
	var update = &model.Admin{
		IsStopUsing: req.IsStopUsing,
		UpdatedAt:   time.Now().Unix(),
	}
	if err := r.DB(ctx).Table("admin").Where("id = ?", req.Id).Updates(update).Error; err != nil {
		return err
	}
	return nil

}

func (r *adminRepository) Create(ctx *gin.Context, v *v1.SaveAdminListRequest) error {
	// 密码字符串转换为 []byte 类型
	passwordBytes := []byte(v.Password)
	hashedPassword, err := bcrypt.GenerateFromPassword(passwordBytes, bcrypt.DefaultCost)
	if err != nil {
		return err
	}
	now := time.Now().Unix()
	Admin := &model.Admin{
		Name:      v.Name,
		Account:   v.Account,
		Password:  string(hashedPassword),
		CreatedAt: now,
		UpdatedAt: now,
		IsDeleted: 1,
	}
	if err := r.DB(ctx).Create(Admin).Error; err != nil {
		return err
	}
	r.OperationLogRepository.AddOperationLogData(ctx, Admin.ID, v1.ActionCreate, v1.ModuleAdmin)
	return nil

}
func (r *adminRepository) GetByAccount(ctx context.Context, account string) (*model.Admin, error) {
	var admin model.Admin
	if err := r.DB(ctx).Where("account = ?", account).Where("is_deleted = 1").First(&admin).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &admin, nil
}

func (r *adminRepository) Edit(ctx *gin.Context, id int, req v1.SaveAdminListRequest) error {
	// 密码字符串转换为 []byte 类型
	if req.Password != "" {
		passwordBytes := []byte(req.Password)
		hashedPassword, err := bcrypt.GenerateFromPassword(passwordBytes, bcrypt.DefaultCost)
		if err != nil {
			return err
		}
		req.Password = string(hashedPassword)
	}
	req.UpdatedAt = time.Now().Unix()

	// return r.DB(ctx).Table("admin").Where("id = ?", id).Updates(req).Error
	if err := r.DB(ctx).Table("admin").Where("id = ?", id).Updates(req).Error; err != nil {
		return err
	}
	r.OperationLogRepository.AddOperationLogData(ctx, id, v1.ActionEdit, v1.ModuleAdmin)
	return nil
}
func (r *adminRepository) GetByNameAndId(ctx context.Context, id int, name string) (*model.Admin, error) {
	var admin model.Admin
	if err := r.DB(ctx).Where("name = ?", name).Where("id != ? ", id).Where("is_deleted = 1").First(&admin).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &admin, nil
}
