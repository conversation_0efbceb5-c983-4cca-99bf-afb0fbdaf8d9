package repository

import (
	"context"
	"errors"
	v1 "esop/api/v1"
	"esop/internal/model"
	"fmt"
	"strconv"
	"strings"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/gin-gonic/gin"
	// "net/http"
	// "gorm.io/gorm"
	// "errors"
)

type EquipmentRepository interface {
	GetEquipmentList(context.Context, v1.EquipmentListRequest) (int64, []*v1.EquipmentData, error)
	GetEquipmentGroupList(context.Context, v1.EquipmentListGroupRequest) (int64, []*v1.EquipmentGroupData, error) //查看分组列表
	FirstById(ctx context.Context, id int64) (*model.Equipment, error)
	Create(ctx *gin.Context, v *v1.SaveEquipmentListRequest) error
	Edit(ctx *gin.Context, req v1.SaveEquipmentListRequest) error
	Delete(ctx *gin.Context, id int) error
	CheckEquipment(ctx *gin.Context, macAddress string) error

	GetEquipmentsByIds(ctx context.Context, idStr string) ([]*model.Equipment, error)
	GetGroupsByIds(ctx context.Context, idStr string) ([]*model.EquipmentGroup, error)
	LifeEquipment(ctx *gin.Context, id string) (v1.EquipmentData, error)
	GetEquipmentGroupData() ([]model.EquipmentGroup, error)
	GetEquipmentData(ctx context.Context, groupId int) ([]model.Equipment, error)
	ScanEquipmentTime() ([]model.Equipment, error)
	SetEquipmentStatusByMacAddress(macAddress string, status int) error
	GroupNameList(ctx *gin.Context) ([]v1.EquipmentGroupData, error)
	DeleteGroup(ctx *gin.Context, groud_id int) error
	AddGroup(ctx *gin.Context, req v1.GroupRequest) error
	GetEquipment(ctx *gin.Context, address string) (v1.EquipmentTree, error)
	ProcessEquipmentReport(report *model.EquipmentReport) error
	ProcessEquipmentReportBatch(reports []*model.EquipmentReport) error
	// CleanOldReports 清理指定天数前的上报数据
	CleanOldReports(days int) error
	GetEquipmentByAliasName(ctx context.Context, aliasName string) (*model.Equipment, error)
	GetEquipmentByAliasNameAndGroupId(ctx context.Context, aliasName string, groupId int) (*model.Equipment, error)
	GetMaterialByEquipmentId(ctx context.Context, id int) ([]v1.SourceMaterialData, error)
	AssociateMaterial(ctx context.Context, equipmentId int, materialId int) error
	DisassociateMaterial(ctx context.Context, id int, equipmentId int) (*model.SourceMaterial, error)
	DisassociateMaterials(ctx context.Context, ids []int, equipmentId int) ([]*model.SourceMaterial, error)
	GetEquipmentById(ctx context.Context, id int) (*model.Equipment, error)
	GetLatestMaterialByEquipmentId(ctx context.Context, id int) (*model.SourceMaterial, error)
	CreateEquipmentScreenshot(ctx context.Context, screenshot *model.EquipmentScreenshot) error
	GetLatestScreenshotByEquipment(ctx context.Context, groupName, equipmentAliasName string) (*model.EquipmentScreenshot, error)
	GetScreenshotByTaskId(ctx context.Context, taskId string) (*model.EquipmentScreenshot, error)
	DeleteEquipmentScreenshot(ctx context.Context, id int64) error
	GetEquipmentScreenshotByID(ctx context.Context, id int64) (*model.EquipmentScreenshot, error)
	GetEquipmentLog(ctx context.Context, req v1.EquipmentLogRequest) (int64, []*model.EquipmentReport, error)
	UpdateEquipmentSchedule(ctx context.Context, aliasName string, powerOnTime string, powerOffTime string, weekdays string) error
	FindLatestReportByMacs(ctx context.Context, macAddresses []string) (map[string]*model.EquipmentReport, error)
	ClearEquipmentLog(ctx context.Context, macAddress string) error
	SaveEquipmentPowerSchedules(ctx context.Context, equipmentId int, schedules []*model.EquipmentPowerSchedule) error
	GetEquipmentPowerSchedules(ctx context.Context, equipmentId int) ([]*model.EquipmentPowerSchedule, error)
	BatchDeleteFiles(ctx context.Context, days int) error
}

	func NewEquipmentRepository(repository *Repository) EquipmentRepository {
		return &equipmentRepository{
		Repository:             repository,
		OperationLogRepository: NewOperationLogRepository(repository),
	}
}

type equipmentRepository struct {
	*Repository
	OperationLogRepository OperationLogRepository
}

func (r *equipmentRepository) FirstById(ctx context.Context, id int64) (*model.Equipment, error) {
	var equipment model.Equipment
	// TODO: query db
	err := r.DB(ctx).Where("id = ?", id).Where("is_deleted = 1").First(&equipment).Error
	if err != nil {
		r.logger.Error("get apply authorize contact by id fail", zap.Any("err", err), zap.Any("id", id))
		return nil, err
	}
	return &equipment, nil
}

func (r *equipmentRepository) GetEquipmentList(ctx context.Context, request v1.EquipmentListRequest) (int64, []*v1.EquipmentData, error) {
	var (
		equipmentList []*v1.EquipmentData
		total         int64 = 10
		err           error
	)
	var db = r.DB(ctx).Table("equipment").
		Where("is_deleted = 1").
		Order("id DESC").
		Select("*").
		Count(&total)

	if request.AliasName != "" {
		db.Where("name like ?", "%"+request.AliasName+"%")
	}
	if request.MacAddress != "" {
		db.Where("mac_address like ?", "%"+request.MacAddress+"%")
	}

	if request.CreatedAtStart != 0 {
		db.Where("created_at >=  ?", request.CreatedAtStart)
	}
	if request.CreatedAtEnd != 0 {
		db.Where("created_at <= ?", request.CreatedAtEnd)
	}
	err = db.Scopes(r.Paginate(&request.PagingRequest)).Find(&equipmentList).Error

	if err != nil {
		return total, nil, err
	}
	// 填充 groupName 字段
	for _, equipment := range equipmentList {
		var groupName string
		err := r.DB(ctx).Table("equipment_group").
			Where("id = ?", equipment.GroupId).
			Select("name").
			Limit(1).
			Find(&groupName).Error
		if err != nil {
			// 处理错误，这里可以记录日志或者给 groupName 赋一个默认值
			groupName = "未知分组"
		}
		equipment.GroupName = groupName
	}

	return total, equipmentList, nil

}
func (r *equipmentRepository) GetEquipmentGroupList(ctx context.Context, request v1.EquipmentListGroupRequest) (int64, []*v1.EquipmentGroupData, error) {
	var (
		equipmentGroupList []*v1.EquipmentGroupData
		total              int64 = 10
		err                error
	)
	var db = r.DB(ctx).Table("equipment_group").
		Order("id DESC").
		Select("*").
		Count(&total)

	err = db.Scopes(r.Paginate(&request.PagingRequest)).Find(&equipmentGroupList).Error

	if err != nil {
		return total, nil, err
	}

	return total, equipmentGroupList, nil

}
func (r *equipmentRepository) Create(ctx *gin.Context, v *v1.SaveEquipmentListRequest) error {
	// 检查设备编号是否已存在
	// fmt.Println(generatekey.GetGenerateKey(v.DeviceId))
	//var existingEquipment model.Equipment
	var groupId int
	now := time.Now().Unix()
	total, err := r.getTotal(ctx, v.MacAddress)
	if err != nil {
		return err
	}

	if total > 0 {
		if err := r.UpdateDeviceInfo(ctx, v1.SaveEquipmentListRequest{
			Name:             v.Name,
			MacAddress:       v.MacAddress,
			RegistrationCode: v.RegistrationCode,
			Status:           1,
			CreatedAt:        now,
			UpdatedAt:        now,
			AliasName:        v.AliasName,
			IpAddr:           v.IpAddr,
			Model:            v.Model,
			Manufacturer:     v.Manufacturer,
			System:           v.System,
			SystemVersion:    v.SystemVersion,
			HardwareInfo:     v.HardwareInfo,
			Brand:            v.Brand,
			Device:           v.Device,
			Product:          v.Product,
			AndroidId:        v.AndroidId,
			Hardware:         v.Hardware,
			SerialNumber:     v.SerialNumber,
			ScreenWidth:      v.ScreenWidth,
			ScreenHeight:     v.ScreenHeight,
			AppVersion:       v.AppVersion,
			GroupId:          groupId,
			IsDeleted:        1,
		}); err != nil {
			return err
		}
		return v1.NewError(0, "success")
	}
	if v.GroupName != "" {
		groupId, err = r.addGroup(ctx, v.GroupName)
	} else {
		groupId = 1
	}

	Equipment := &model.Equipment{
		MacAddress:       v.MacAddress,
		RegistrationCode: v.RegistrationCode,
		Status:           1,
		CreatedAt:        now,
		UpdatedAt:        now,
		AliasName:        v.AliasName,
		IpAddr:           v.IpAddr,
		Model:            v.Model,
		Manufacturer:     v.Manufacturer,
		System:           v.System,
		SystemVersion:    v.SystemVersion,
		HardwareInfo:     v.HardwareInfo,
		Brand:            v.Brand,
		Device:           v.Device,
		Product:          v.Product,
		AndroidId:        v.AndroidId,
		Hardware:         v.Hardware,
		SerialNumber:     v.SerialNumber,
		ScreenWidth:      v.ScreenWidth,
		ScreenHeight:     v.ScreenHeight,
		AppVersion: v.AppVersion,
		GroupId:          groupId,
		IsDeleted:        1,
	}
	if err := r.DB(ctx).Create(Equipment).Error; err != nil {
		return err
	}
	return nil
}

func (r *equipmentRepository) Edit(ctx *gin.Context, req v1.SaveEquipmentListRequest) error {
	var groupId int
	var err error

	if req.GroupName == "" {
		groupId = 1
		req.GroupName = "null"
		groupId, err = r.addGroup(ctx, req.GroupName)
		if err != nil {
			return err
		}
	} else {
		groupId, err = r.addGroup(ctx, req.GroupName)
	}

	if err != nil {
		return err
	}
	r.db.Table("equipment").
		Where("mac_address = ?", req.MacAddress).
		Updates(model.Equipment{
			AliasName: req.AliasName,
			GroupId:   groupId,
			GroupName: req.GroupName,
		})
	return nil
}

func (r *equipmentRepository) UpdateDeviceInfo(ctx *gin.Context, req v1.SaveEquipmentListRequest) error {

	r.db.Table("equipment").
		Where("mac_address = ?", req.MacAddress).
		Updates(model.Equipment{
			DeviceNumber:     req.DeviceNumber,
			RegistrationCode: req.RegistrationCode,
			AliasName:        req.AliasName,
			IpAddr:           req.IpAddr,
			Status:           req.Status,
			IsDeleted:        req.IsDeleted,
			GroupName:        req.GroupName,
			GroupId:          req.GroupId,
			System:           req.System,
			SystemVersion:    req.SystemVersion,
			HardwareInfo:     req.HardwareInfo,
			Manufacturer:     req.Manufacturer,
			Model:            req.Model,
			Brand:            req.Brand,
			Device:           req.Device,
			Product:          req.Product,
			AndroidId:        req.AndroidId,
			Hardware:         req.Hardware,
			SerialNumber:     req.SerialNumber,
			ScreenWidth:      req.ScreenWidth,
			ScreenHeight:     req.ScreenHeight,
			UpdatedAt:        req.UpdatedAt,
		})
	return nil
}
func (r *equipmentRepository) Delete(ctx *gin.Context, id int) error {
	now := time.Now().Unix()
	var update = &model.Equipment{
		IsDeleted: 2,
		UpdatedAt: now,
	}
	if err := r.DB(ctx).Table("equipment").Where("id = ?", id).Updates(update).Error; err != nil {
		return err
	}
	r.OperationLogRepository.AddOperationLogData(ctx, id, v1.ActionDelete, v1.ModuleEquipment)
	return nil

}

func (r *equipmentRepository) addGroup(ctx *gin.Context, groupName string) (int, error) {
	now := time.Now().Unix()
	var groupData v1.EquipmentGroupData
	db := r.DB(ctx).Table("equipment_group").
		Where("name =?", groupName).
		Select("id, number").
		First(&groupData)
	if db.Error != nil {

		newGroup := v1.SaveEquipmentGroupRequest{
			Name:      groupName,
			Number:    1,
			CreatedAt: now,
			UpdatedAt: now,
		}

		result := r.DB(ctx).Table("equipment_group").Create(newGroup)
		if result.Error != nil {
			return 0, result.Error
		}
		// 获取新增记录的 ID
		insertID := int(result.RowsAffected)
		if insertID > 0 {
			// 重新查询获取新增记录的 ID
			r.DB(ctx).Table("equipment_group").Where("name =?", groupName).Order("id DESC").Select("id").First(&groupData)
			return groupData.ID, nil
		}
		return 0, v1.ErrDataNotExists

		// 处理其他数据库错误
	}

	// 分组存在，将 number 字段自增 1
	result := r.DB(ctx).Table("equipment_group").Where("id =?", groupData.ID).UpdateColumn("number", gorm.Expr("number + ?", 1))
	if result.Error != nil {
		return 0, result.Error
	}
	return groupData.ID, nil
}

//检查设备是否存在

func (r *equipmentRepository) CheckEquipment(ctx *gin.Context, macAddress string) error {
	total, err := r.getTotal(ctx, macAddress)
	if err != nil {
		return err
	}
	if total > 0 {
		return nil

	}
	return v1.ErrDataNotExists

}

// 获取数量
func (r *equipmentRepository) getTotal(ctx *gin.Context, macAddress string) (int64, error) {
	var total int64
	err := r.DB(ctx).Table("equipment").Where("mac_address = ?", macAddress).Count(&total).Error
	if err != nil {
		return 0, err
	}
	return total, nil
}

// GetEquipmentsByIds 根据传入的 idStr 字符串获取设备数据
func (r *equipmentRepository) GetEquipmentsByIds(ctx context.Context, idStr string) ([]*model.Equipment, error) {
	// 分割字符串为多个 id 字符串
	idStrs := strings.Split(idStr, ",")
	// 转换为 int64 类型的切片
	var ids []int64
	for _, str := range idStrs {
		id, err := strconv.ParseInt(str, 10, 64)
		if err != nil {
			return nil, fmt.Errorf("invalid id: %s", str)
		}
		ids = append(ids, id)
	}

	// 执行查询
	var equipments []*model.Equipment
	err := r.DB(ctx).Table("equipment").Where("id IN (?)", ids).Where("is_deleted = 1").Find(&equipments).Error
	if err != nil {
		r.logger.Error("get equipments by ids fail", zap.Any("err", err), zap.Any("ids", ids))
		return nil, err
	}

	return equipments, nil
}
func (r *equipmentRepository) GetGroupsByIds(ctx context.Context, idStr string) ([]*model.EquipmentGroup, error) {
	// 分割字符串为多个 id 字符串
	idStrs := strings.Split(idStr, ",")
	// 转换为 int64 类型的切片
	var ids []int64
	for _, str := range idStrs {
		id, err := strconv.ParseInt(str, 10, 64)
		if err != nil {
			return nil, fmt.Errorf("invalid id: %s", str)
		}
		ids = append(ids, id)
	}

	// 执行查询
	var equipmentGroup []*model.EquipmentGroup
	err := r.DB(ctx).Table("equipment_group").Where("id IN (?)", ids).Find(&equipmentGroup).Error
	if err != nil {
		r.logger.Error("get equipments by ids fail", zap.Any("err", err), zap.Any("ids", ids))
		return nil, err
	}

	return equipmentGroup, nil
}
func (r *Repository) LifeEquipment(ctx *gin.Context, mac_address string) (v1.EquipmentData, error) {
	var equipment v1.EquipmentData
	if err := r.db.Table("equipment").
		Where("mac_address = ?", mac_address).Updates(map[string]interface{}{"status": 1,
		"updated_at": time.Now().Unix(),
	}).Select("*", "equipment_group.name as group_name").
		Joins("left join equipment_group on equipment_group.id = equipment.group_id").
		Scan(&equipment).Error; err != nil {
		return v1.EquipmentData{}, err
	}
	return equipment, nil
}
func (r *equipmentRepository) GetEquipmentGroupData() ([]model.EquipmentGroup, error) {
	var data []model.EquipmentGroup

	err := r.db.Table("equipment_group").
		Select("*").Scan(&data).Error
	if err != nil {
		return nil, err
	}
	return data, nil
}

func (r *equipmentRepository) GetEquipmentData(ctx context.Context, groupId int) ([]model.Equipment, error) {
	var data []model.Equipment

	err := r.DB(ctx).Table("equipment").
		Select("*").
		Where("is_deleted =?", 1).
		Where("group_id =?", groupId).
		Scan(&data).Error
	if err != nil {
		return nil, err
	}
	return data, nil
}
func (r *equipmentRepository) ScanEquipmentTime() ([]model.Equipment, error) {
	var data []model.Equipment
	if err := r.db.Table("equipment").
		Select("*").
		Where("is_deleted =?", 1).
		Where("status =?", 1).
		Scan(&data).Error; err != nil {
		return nil, err
	}
	return data, nil
}
func (r *equipmentRepository) SetEquipmentStatusByMacAddress(macAddress string, status int) error {
	return r.db.Table("equipment").
		Where("mac_address = ?", macAddress).
		Updates(map[string]interface{}{"status": status,
			"updated_at": time.Now().Unix(),
		}).Error
}
func (r *equipmentRepository) GroupNameList(ctx *gin.Context) ([]v1.EquipmentGroupData, error) {
	var data []v1.EquipmentGroupData
	if err := r.DB(ctx).Table("equipment_group").
		Select("id,name").
		Scan(&data).Error; err != nil {
		return data, err
	}
	return data, nil

}
func (r *equipmentRepository) DeleteGroup(ctx *gin.Context, groud_id int) error {

	return r.db.Table("equipment_group").
		Where("id = ?", groud_id).
		Delete(&model.EquipmentGroup{}).Error

}
func (r *equipmentRepository) AddGroup(ctx *gin.Context, req v1.GroupRequest) error {
	return r.db.Table("equipment_group").
		Create(&model.EquipmentGroup{
			Name: req.Name,
		}).Error
}
func (r *equipmentRepository) GetEquipment(ctx *gin.Context, address string) (v1.EquipmentTree, error) {
	var data v1.EquipmentTree
	if err := r.db.Table("equipment").
		Where("mac_address = ?", address).
		Select("*", "equipment_group.name as group_name").
		Joins("left join equipment_group on equipment_group.id = equipment.group_id").
		Scan(&data).Error; err != nil {
		return v1.EquipmentTree{}, err
	}

	return data, nil

}

// 设备上报数据模型
type EquipmentReport struct {
	ID               int64  `gorm:"column:id;primaryKey" json:"id"`
	MacAddress       string `gorm:"column:mac_address" json:"mac_address"`
	DeviceAlias      string `gorm:"column:device_alias" json:"device_alias"`
	GroupName        string `gorm:"column:group_name" json:"group_name"`
	Timestamp        string `gorm:"column:timestamp" json:"timestamp"`
	ReportType       string `gorm:"column:report_type" json:"report_type"`
	OperationID      string `gorm:"column:operation_id" json:"operation_id"`
	Data             string `gorm:"column:data" json:"data"`
	CreatedAt        int64  `gorm:"column:created_at" json:"created_at"`
}

func (m *EquipmentReport) TableName() string {
	return "equipment_report"
}

// ProcessEquipmentReport 处理单个设备上报数据
func (r *equipmentRepository) ProcessEquipmentReport(report *model.EquipmentReport) error {
	// 实现单个设备上报数据的处理逻辑
	// 例如：保存到数据库
	return r.db.Create(report).Error
}

// ProcessEquipmentReportBatch 处理批量设备上报数据
func (r *equipmentRepository) ProcessEquipmentReportBatch(reports []*model.EquipmentReport) error {
	// 实现批量设备上报数据的处理逻辑
	// 例如：批量保存到数据库
	return r.db.Create(&reports).Error
}

// CleanOldReports 清理指定天数前的上报数据
func (r *equipmentRepository) CleanOldReports(days int) error {
	// 计算指定天数前的时间
	cutoffTime := time.Now().AddDate(0, 0, -days)

	// 使用 GORM 删除指定时间前的记录
	return r.db.Where("timestamp < ?", cutoffTime).Delete(&model.EquipmentReport{}).Error
}

func (r *equipmentRepository) GetEquipmentByAliasName(ctx context.Context, aliasName string) (*model.Equipment, error) {
	var equipment model.Equipment
	err := r.DB(ctx).Where("alias_name = ?", aliasName).First(&equipment).Error
	if err != nil {
		return nil, err
	}
	return &equipment, nil
}

func (r *equipmentRepository) GetEquipmentByAliasNameAndGroupId(ctx context.Context, aliasName string, groupId int) (*model.Equipment, error) {
	var equipment model.Equipment
	err := r.DB(ctx).Where("alias_name = ? AND group_id = ?", aliasName, groupId).First(&equipment).Error
	if err != nil {
		return nil, err
	}
	return &equipment, nil
}

func (r *equipmentRepository) GetMaterialByEquipmentId(ctx context.Context, id int) ([]v1.SourceMaterialData, error) {
	var material []v1.SourceMaterialData
	err := r.DB(ctx).Table("source_material").
		Where("equipment_id = ?", id).
		Order("created_at DESC").
		Scan(&material).Error
	if err != nil {
		return nil, err
	}
	return material, nil
}

func (r *equipmentRepository) AssociateMaterial(ctx context.Context, equipmentId int, materialId int) error {
	// 使用 GORM 的 FirstOrCreate 来处理关联关系
	// 首先定义一个要操作的记录
	association := model.EquipmentSM{
		EquipmentId: equipmentId,
		MaterialId:  materialId,
	}

	// FirstOrCreate 会查找第一条匹配的记录，如果没有找到，则会创建一个新的记录
	// 这里我们只关心 equipment_id，如果存在则更新 material_id，如果不存在则创建
	// 我们使用 Where 来查找，使用 Assign 来指定要更新或创建的字段
	err := r.DB(ctx).
		Where(model.EquipmentSM{EquipmentId: equipmentId}).
		Assign(model.EquipmentSM{MaterialId: materialId}).
		FirstOrCreate(&association).Error

	return err
}

func (r *equipmentRepository) GetEquipmentById(ctx context.Context, id int) (*model.Equipment, error) {
	var equipment model.Equipment
	err := r.DB(ctx).
	Select("*","equipment_group.name as group_name").
	Where("equipment.id = ?", id).
	Joins("left join equipment_group on equipment_group.id = equipment.group_id").
	First(&equipment).Error
	if err != nil {
		return nil, err
	}
	return &equipment, nil
}

func (r *equipmentRepository) GetLatestMaterialByEquipmentId(ctx context.Context, id int) (*model.SourceMaterial, error) {
	var material model.SourceMaterial
	err := r.DB(ctx).Table("source_material").
		Where("equipment_id = ?", id).
		Order("created_at DESC").
		First(&material).Error
	if err != nil {
		return nil, err
	}
	return &material, nil
}

func (r *equipmentRepository) DisassociateMaterial(ctx context.Context, id int, equipmentId int) (*model.SourceMaterial, error) {
	var material model.SourceMaterial
	// 首先查找记录
	err := r.DB(ctx).
		Where("equipment_id = ? AND id = ?", equipmentId, id).
		First(&material).Error
	if err != nil {
		return nil, err
	}

	// 删除记录
	err = r.DB(ctx).
		Where("id = ?", id).
		Delete(&model.SourceMaterial{}).Error
	if err != nil {
		return nil, err
	}

	return &material, nil
}

func (r *equipmentRepository) DisassociateMaterials(ctx context.Context, ids []int, equipmentId int) ([]*model.SourceMaterial, error) {
	var materials []*model.SourceMaterial
	// 首先查找记录
	err := r.DB(ctx).
		Where("equipment_id = ? AND id IN ?", equipmentId, ids).
		Find(&materials).Error
	if err != nil {
		return nil, err
	}

	// 删除记录
	err = r.DB(ctx).
		Where("id IN ?", ids).
		Delete(&model.SourceMaterial{}).Error
	if err != nil {
		return nil, err
	}

	return materials, nil
}

func (r *equipmentRepository) CreateEquipmentScreenshot(ctx context.Context, screenshot *model.EquipmentScreenshot) error {
	return r.DB(ctx).Create(screenshot).Error
}

func (r *equipmentRepository) GetScreenshotByTaskId(ctx context.Context, taskId string) (*model.EquipmentScreenshot, error) {
	var screenshot model.EquipmentScreenshot
	err := r.db.WithContext(ctx).
		Where("task_id = ?", taskId).
		First(&screenshot).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &screenshot, nil
}

func (r *equipmentRepository) DeleteEquipmentScreenshot(ctx context.Context, id int64) error {
	return r.DB(ctx).Delete(&model.EquipmentScreenshot{}, id).Error
}

func (r *equipmentRepository) GetEquipmentScreenshotByID(ctx context.Context, id int64) (*model.EquipmentScreenshot, error) {
	var screenshot model.EquipmentScreenshot
	err := r.DB(ctx).First(&screenshot, id).Error
	if err != nil {
		return nil, err
	}
	return &screenshot, nil
}

func (r *equipmentRepository) GetEquipmentLog(ctx context.Context, req v1.EquipmentLogRequest) (int64, []*model.EquipmentReport, error) {
	var (
		logs  []*model.EquipmentReport
		total int64
	)

	db := r.DB(ctx).Model(&model.EquipmentReport{}).Where("mac_address = ?", req.MacAddress)

	if err := db.Count(&total).Error; err != nil {
		return 0, nil, err
	}

	if err := db.Scopes(r.Paginate(&req.PagingRequest)).Order("timestamp DESC").Find(&logs).Error; err != nil {
		return 0, nil, err
	}

	return total, logs, nil
}

func (r *equipmentRepository) UpdateEquipmentSchedule(ctx context.Context, aliasName string, powerOnTime string, powerOffTime string, weekdays string) error {
	err := r.DB(ctx).Table("equipment").
		Where("alias_name = ?", aliasName).
		Updates(map[string]interface{}{
			"power_on_time":  powerOnTime,
			"power_off_time": powerOffTime,
			"weekdays":       weekdays,
			"updated_at":     time.Now().Unix(),
		}).Error
	if err != nil {
		r.logger.Error("failed to update equipment schedule", zap.Error(err), zap.String("aliasName", aliasName))
		return err
	}
	return nil
}

func (r *equipmentRepository) FindLatestReportByMacs(ctx context.Context, macAddresses []string) (map[string]*model.EquipmentReport, error) {
	var allReports []*model.EquipmentReport
	err := r.DB(ctx).
		Where("mac_address IN ?", macAddresses).
		Where("report_type IN ?", []string{"current_open_file", "file_extraction_started", "file_download_completed","mqtt_command_processed"}).
		Order("timestamp DESC").
		Find(&allReports).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return make(map[string]*model.EquipmentReport), nil
		}
		r.logger.Error("find reports by macs fail", zap.Error(err))
		return nil, err
	}

	// 在应用层处理，找到每个MAC地址最新的报告
	latestReports := make(map[string]*model.EquipmentReport)
	for _, report := range allReports {
		if _, ok := latestReports[report.MacAddress]; !ok {
			// 由于已经按时间戳降序排序，第一个遇到的就是最新的
			latestReports[report.MacAddress] = report
		}
	}

	return latestReports, nil
}

func (r *equipmentRepository) GetLatestScreenshotByEquipment(ctx context.Context, groupName, equipmentAliasName string) (*model.EquipmentScreenshot, error) {
	var screenshot model.EquipmentScreenshot
	err := r.DB(ctx).Model(&model.EquipmentScreenshot{}).
		Where("group_name = ? AND equipment_alias_name = ?", groupName, equipmentAliasName).
		Order("created_at DESC").
		First(&screenshot).Error
	if err != nil {
		return nil, err
	}
	return &screenshot, nil
}

func (r *equipmentRepository) ClearEquipmentLog(ctx context.Context, macAddress string) error {
	return r.DB(ctx).Where("mac_address = ?", macAddress).Delete(&model.EquipmentReport{}).Error
}

func (r *equipmentRepository) SaveEquipmentPowerSchedules(ctx context.Context, equipmentId int, schedules []*model.EquipmentPowerSchedule) error {
	tx := r.DB(ctx).Begin()
	if tx.Error != nil {
		return tx.Error
	}

	// 1. 删除旧的调度
	if err := tx.Where("equipment_id = ?", equipmentId).Delete(&model.EquipmentPowerSchedule{}).Error; err != nil {
		tx.Rollback()
		return err
	}

	// 2. 插入新的调度
	if len(schedules) > 0 {
		for _, schedule := range schedules {
			schedule.EquipmentID = equipmentId
		}

		if err := tx.Create(&schedules).Error; err != nil {
			tx.Rollback()
			return err
		}
	}

	return tx.Commit().Error
}

func (r *equipmentRepository) GetEquipmentPowerSchedules(ctx context.Context, equipmentId int) ([]*model.EquipmentPowerSchedule, error) {
	var schedules []*model.EquipmentPowerSchedule
	err := r.DB(ctx).Where("equipment_id = ?", equipmentId).Find(&schedules).Error
	if err != nil {
		return nil, err
	}
	return schedules, nil
}

func (r *equipmentRepository) BatchDeleteFiles(ctx context.Context, days int) error {
	// 计算截止时间
	cutoffTime := time.Now().AddDate(0, 0, -days)

	// 删除 source_material 表中符合条件的记录
	if err := r.DB(ctx).Where("created_at < ?", cutoffTime.Unix()).Delete(&model.SourceMaterial{}).Error; err != nil {
		return err
	}

	return nil
}


