package repository

import (
	v1 "esop/api/v1"
	"esop/internal/model"
	"fmt"
	"time"

	"github.com/gin-gonic/gin"
)

type OperationLogRepository interface {
	FirstById(id int64) (*model.OperationLog, error)
	GetOperationLogList(ctx *gin.Context, req v1.OperationLogRequest) (int64, []*v1.OperationLogData, error)
	AddOperationLogData(ctx *gin.Context, id int, action string, module string)
}

func NewOperationLogRepository(repository *Repository) OperationLogRepository {
	return &operationLogRepository{
		Repository: repository,
	}
}

type operationLogRepository struct {
	*Repository
}

func (r *operationLogRepository) FirstById(id int64) (*model.OperationLog, error) {
	var operationLog model.OperationLog
	// TODO: query db
	return &operationLog, nil
}

func (r *operationLogRepository) GetOperationLogList(ctx *gin.Context, request v1.OperationLogRequest) (int64, []*v1.OperationLogData, error) {
	var (
		operationLog []*v1.OperationLogData
		total        int64 = 10
		err          error
	)

	var db = r.DB(ctx).Table("operation_log").
		Order("id DESC").
		Select("*").
		Count(&total)

	if request.AdminName != "" {
		db.Where("admin_name like ?", "%"+request.AdminName+"%")
	}
	if request.AdminAccount != "" {
		db.Where("admin_account like ?", "%"+request.AdminAccount+"%")
	}
	if request.Module != "" {
		db.Where("module like ?", "%"+request.Module+"%")
	}

	if request.ModuleId != 0 {
		db.Where("module_id = ?", request.ModuleId)
	}

	if request.CreatedAtStart != 0 {
		db.Where("created_at >=  ?", request.CreatedAtStart)
	}
	if request.CreatedAtEnd != 0 {
		db.Where("created_at <= ?", request.CreatedAtEnd)
	}
	err = db.Scopes(r.Paginate(&request.PagingRequest)).Find(&operationLog).Error

	if err != nil {
		return total, nil, err
	}
	// r.AddOperationLogList(ctx, 0, v1.ActionCreate, v1.ModuleTemplate)
	return total, operationLog, nil

}

// 新增日志
func (r *operationLogRepository) AddOperationLogData(ctx *gin.Context, id int, action string, module string) {
	fmt.Println(id)
	now := time.Now().Unix()
	OperationLog := &model.OperationLog{
		Action:       action,
		Module:       module,
		ModuleId:     id,
		AdminName:    "",
		AdminAccount: "",
		CreatedAt:    now,
		UpdatedAt:    now,
	}

	fmt.Printf("r: %v\n", r)
	r.DB(ctx).Create(OperationLog)

}
