package repository

import (
	"context"
	v1 "esop/api/v1"
	"esop/internal/model"
	"time"

	"github.com/gin-gonic/gin"
)

type MaterialGroupRepository interface {
	GetMaterialGroupList(ctx context.Context, req v1.MaterialGroupListRequest) (int64, []*v1.MaterialGroupData, error)
	GetMaterialGroupDetail(ctx context.Context, id int) (v1.MaterialGroupData, error)
	Create(ctx *gin.Context, req *v1.SaveMaterialGroupRequest) error
	Edit(ctx *gin.Context, id int, req v1.SaveMaterialGroupRequest) error
	Delete(ctx *gin.Context, id int) error
	FirstById(id int64) (*model.MaterialGroup, error)
	GetAllGroups(ctx context.Context) ([]*v1.MaterialGroupData, error)
}

func NewMaterialGroupRepository(repository *Repository) MaterialGroupRepository {
	return &materialGroupRepository{
		Repository: repository,
	}
}

type materialGroupRepository struct {
	*Repository
}

func (r *materialGroupRepository) GetMaterialGroupList(ctx context.Context, req v1.MaterialGroupListRequest) (int64, []*v1.MaterialGroupData, error) {
	var total int64
	var groups []*model.MaterialGroup

	db := r.db.WithContext(ctx).Model(&model.MaterialGroup{}).Where("is_deleted = ?", 1)

	// 按名称筛选
	if req.Name != "" {
		db = db.Where("name LIKE ?", "%"+req.Name+"%")
	}

	// 获取总数
	if err := db.Count(&total).Error; err != nil {
		return 0, nil, err
	}

	// 分页查询
	if err := db.Order("sort_order ASC, id DESC").
		Limit(req.PageSize).
		Offset((req.Page - 1) * req.PageSize).
		Find(&groups).Error; err != nil {
		return 0, nil, err
	}

	// 转换为响应数据并统计素材数量
	var result []*v1.MaterialGroupData
	for _, group := range groups {
		// 统计该分组下的素材数量
		var materialCount int64
		r.db.Model(&model.SourceMaterial{}).
			Where("group_id = ? AND is_deleted = ?", group.ID, 1).
			Count(&materialCount)

		result = append(result, &v1.MaterialGroupData{
			ID:            group.ID,
			Name:          group.Name,
			Description:   group.Description,
			Color:         group.Color,
			SortOrder:     group.SortOrder,
			MaterialCount: int(materialCount),
			CreatedAt:     group.CreatedAt,
			UpdatedAt:     group.UpdatedAt,
		})
	}

	return total, result, nil
}

func (r *materialGroupRepository) GetMaterialGroupDetail(ctx context.Context, id int) (v1.MaterialGroupData, error) {
	var group model.MaterialGroup
	var result v1.MaterialGroupData

	if err := r.db.WithContext(ctx).Where("id = ? AND is_deleted = ?", id, 1).First(&group).Error; err != nil {
		return result, err
	}

	// 统计素材数量
	var materialCount int64
	r.db.Model(&model.SourceMaterial{}).
		Where("group_id = ? AND is_deleted = ?", group.ID, 1).
		Count(&materialCount)

	result = v1.MaterialGroupData{
		ID:            group.ID,
		Name:          group.Name,
		Description:   group.Description,
		Color:         group.Color,
		SortOrder:     group.SortOrder,
		MaterialCount: int(materialCount),
		CreatedAt:     group.CreatedAt,
		UpdatedAt:     group.UpdatedAt,
	}

	return result, nil
}

func (r *materialGroupRepository) Create(ctx *gin.Context, req *v1.SaveMaterialGroupRequest) error {
	now := time.Now().Unix()

	group := &model.MaterialGroup{
		Name:        req.Name,
		Description: req.Description,
		Color:       req.Color,
		SortOrder:   req.SortOrder,
		IsDeleted:   1,
		CreatedAt:   now,
		UpdatedAt:   now,
	}

	return r.db.WithContext(ctx).Create(group).Error
}

func (r *materialGroupRepository) Edit(ctx *gin.Context, id int, req v1.SaveMaterialGroupRequest) error {
	now := time.Now().Unix()

	updates := map[string]interface{}{
		"name":        req.Name,
		"description": req.Description,
		"color":       req.Color,
		"sort_order":  req.SortOrder,
		"updated_at":  now,
	}

	return r.db.WithContext(ctx).Model(&model.MaterialGroup{}).
		Where("id = ? AND is_deleted = ?", id, 1).
		Updates(updates).Error
}

func (r *materialGroupRepository) Delete(ctx *gin.Context, id int) error {
	now := time.Now().Unix()

	// 软删除分组
	if err := r.db.WithContext(ctx).Model(&model.MaterialGroup{}).
		Where("id = ? AND is_deleted = ?", id, 1).
		Updates(map[string]interface{}{
			"is_deleted": 2,
			"updated_at": now,
		}).Error; err != nil {
		return err
	}

	// 将该分组下的素材移动到未分组（group_id = 0）
	return r.db.WithContext(ctx).Model(&model.SourceMaterial{}).
		Where("group_id = ? AND is_deleted = ?", id, 1).
		Updates(map[string]interface{}{
			"group_id":   0,
			"updated_at": now,
		}).Error
}

func (r *materialGroupRepository) FirstById(id int64) (*model.MaterialGroup, error) {
	var group model.MaterialGroup
	if err := r.db.Where("id = ? AND is_deleted = ?", id, 1).First(&group).Error; err != nil {
		return nil, err
	}
	return &group, nil
}

func (r *materialGroupRepository) GetAllGroups(ctx context.Context) ([]*v1.MaterialGroupData, error) {
	var groups []*model.MaterialGroup

	if err := r.db.WithContext(ctx).
		Where("is_deleted = ?", 1).
		Order("sort_order ASC, id ASC").
		Find(&groups).Error; err != nil {
		return nil, err
	}

	var result []*v1.MaterialGroupData
	for _, group := range groups {
		// 统计素材数量
		var materialCount int64
		r.db.Model(&model.SourceMaterial{}).
			Where("group_id = ? AND is_deleted = ?", group.ID, 1).
			Count(&materialCount)

		result = append(result, &v1.MaterialGroupData{
			ID:            group.ID,
			Name:          group.Name,
			Description:   group.Description,
			Color:         group.Color,
			SortOrder:     group.SortOrder,
			MaterialCount: int(materialCount),
			CreatedAt:     group.CreatedAt,
			UpdatedAt:     group.UpdatedAt,
		})
	}

	return result, nil
}