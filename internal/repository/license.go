package repository

import (
	"context"
	"errors"
	"esop/internal/model"
	"esop/pkg/helper/decode"

	"gorm.io/gorm"
)

type LicenseRepository interface {
	GetServerLicense(ctx context.Context) (*model.ServerLicense, error)
	UpsertServerLicense(ctx context.Context, lic *model.ServerLicense) error
	GetTerminalStatus(ctx context.Context, keyword string, page, pageSize int) (int64, []map[string]interface{}, error)
	UpdateTerminalRegistrationCodes(ctx context.Context, items []decode.TerminalDevice) error
	FetchTerminalCodes(ctx context.Context, onlyUnregistered bool) ([]TermCodeRow, error)
}

type licenseRepository struct { *Repository }

type TermRegItem struct { AliasName string `json:"alias_name"`; MacAddress string `json:"mac_address"`;SerialNumber string `json:"serial_number"`;Reg string `json:"reg"`  }

type TermCodeRow struct { AliasName string `json:"alias_name"`; Mac<PERSON>ddress string `json:"mac_address"`;SerialNumber string `json:"serial_number"` }

func NewLicenseRepository(r *Repository) LicenseRepository { return &licenseRepository{Repository: r} }

func (r *licenseRepository) GetServerLicense(ctx context.Context) (*model.ServerLicense, error) {
	var lic model.ServerLicense
	err := r.DB(ctx).Table((&model.ServerLicense{}).TableName()).Limit(1).Scan(&lic).Error
	if err == gorm.ErrRecordNotFound || lic.ID == 0 {
		return nil, nil
	}
	return &lic, err
}

func (r *licenseRepository) UpsertServerLicense(ctx context.Context, lic *model.ServerLicense) error {
	var exist model.ServerLicense
	db := r.DB(ctx)
	db.Table((&model.ServerLicense{}).TableName()).Limit(1).Scan(&exist)
	if exist.ID == 0 {
		return db.Create(lic).Error
	}
	lic.ID = exist.ID
	return db.Model(&exist).Updates(lic).Error
}


func (r *licenseRepository) GetTerminalStatus(ctx context.Context, keyword string, page, pageSize int) (int64, []map[string]interface{}, error) {
	db := r.DB(ctx).Table("equipment").Where("is_deleted=1")
	if keyword != "" {
		db = db.Where("alias_name like ? OR mac_address like ?", "%"+keyword+"%", "%"+keyword+"%")
	}
	var total int64
	db.Count(&total)
	if page <= 0 { page = 1 }
	if pageSize <= 0 { pageSize = 10 }
	offset := (page-1)*pageSize
	var rows []map[string]interface{}
	err := db.Select("alias_name, mac_address, registration_code, created_at").Order("id desc").Offset(offset).Limit(pageSize).Find(&rows).Error
	return total, rows, err
}

func (r *licenseRepository) UpdateTerminalRegistrationCodes(ctx context.Context, items []decode.TerminalDevice) error {
	db := r.DB(ctx)
	for _, it := range items {
		if it.MacAddress == "" || it.SerialNumber == "" { return errors.New("invalid mac address") }
		if err := db.Table("equipment").
		Where("mac_address = ?", it.MacAddress).
		Where("serial_number = ?", it.SerialNumber).
		Update("registration_code", it.Reg).Error; err != nil { return err }
	}
	return nil
}

func (r *licenseRepository) FetchTerminalCodes(ctx context.Context, onlyUnregistered bool) ([]TermCodeRow, error) {
	db := r.DB(ctx).Table("equipment").Where("is_deleted=1")
	if onlyUnregistered { db = db.Where("registration_code='' OR registration_code IS NULL") }
	var rows []TermCodeRow
	err := db.Select("alias_name , mac_address,serial_number").Order("id desc").Find(&rows).Error
	return rows, err
}

