package repository

import (
	"context"
	"time"

	v1 "esop/api/v1"
	"esop/internal/model"

	"github.com/gin-gonic/gin"
)

type SourceMaterialRepository interface {
	FirstById(id int64) (*model.SourceMaterial, error)
	GetSourceMaterialList(ctx context.Context, req v1.SourceMaterialListRequest) (int64, []*v1.SourceMaterialData, error)
	GetSourceMaterialDetail(ctx context.Context, id int) (v1.SourceMaterialData, error)
	Delete(ctx *gin.Context, ids ...int) ([]*model.SourceMaterial, error)
	Edit(ctx *gin.Context, id int, req v1.SaveSourceMaterialRequest) error
	Create(ctx *gin.Context, v *v1.SaveSourceMaterialRequest) error
	BatchCreate(ctx *gin.Context, materials []*model.SourceMaterial) error
	UpdateMaterialsGroup(ctx *gin.Context, materialIds []int, groupId int) error
}

func NewSourceMaterialRepository(repository *Repository) SourceMaterialRepository {
	return &sourceMaterialRepository{
		Repository:             repository,
		OperationLogRepository: NewOperationLogRepository(repository),
	}
}

type sourceMaterialRepository struct {
	*Repository
	OperationLogRepository OperationLogRepository
}

func (r *sourceMaterialRepository) FirstById(id int64) (*model.SourceMaterial, error) {
	var sourceMaterial model.SourceMaterial
	// TODO: query db
	return &sourceMaterial, nil
}

func (r *sourceMaterialRepository) GetSourceMaterialList(ctx context.Context, request v1.SourceMaterialListRequest) (int64, []*v1.SourceMaterialData, error) {
	var (
		sourceMaterialList []*v1.SourceMaterialData
		total              int64 = 10
		err                error
	)
	db := r.DB(ctx).Table("source_material").
		Where("is_deleted = 1").
		Where("equipment_id = ?", 0).
		Order("id DESC").
		Select("*")

	if request.Name != "" {
		db.Where("name like ?", "%"+request.Name+"%")
	}
	if request.Type != 0 {
		db.Where("type = ?", request.Type)
	}
	if request.GroupId != nil {
		db.Where("group_id = ?", *request.GroupId)
	}

	if request.CreatedAtStart != 0 {
		db.Where("created_at >=  ?", request.CreatedAtStart)
	}
	if request.CreatedAtEnd != 0 {
		db.Where("created_at <= ?", request.CreatedAtEnd)
	}
	db.Count(&total)
	err = db.Scopes(r.Paginate(&request.PagingRequest)).Find(&sourceMaterialList).Error
	if err != nil {
		return total, nil, err
	}
	return total, sourceMaterialList, nil
}

func (r *sourceMaterialRepository) GetSourceMaterialDetail(ctx context.Context, id int) (v1.SourceMaterialData, error) {
	var list v1.SourceMaterialData
	if err := r.DB(ctx).Table("source_material").
		Select("*").
		Where(&model.SourceMaterial{ID: id, IsDeleted: 0}).First(&list).Error; err != nil {
		return list, err
	}
	return list, nil
}

func (r *sourceMaterialRepository) Delete(ctx *gin.Context, ids ...int) ([]*model.SourceMaterial, error) {
	var materials []*model.SourceMaterial
	// First, find the records to get their paths
	if err := r.DB(ctx).Where("id IN ?", ids).Find(&materials).Error; err != nil {
		return nil, err
	}

	// Then, delete the records from the database
	if err := r.DB(ctx).Where("id IN ?", ids).Delete(&model.SourceMaterial{}).Error; err != nil {
		return nil, err
	}

	for _, id := range ids {
		r.OperationLogRepository.AddOperationLogData(ctx, id, v1.ActionDelete, v1.ModuleSourceMaterial)
	}
	return materials, nil
}

func (r *sourceMaterialRepository) Create(ctx *gin.Context, v *v1.SaveSourceMaterialRequest) error {
	now := time.Now().Unix()
	SourceMaterial := &model.SourceMaterial{
		Name:         v.Name,
		Type:         v.Type,
		Path:         v.Path,
		SourceWidth:  v.SourceWidth,
		SourceHeight: v.SourceHeight,
		ContentType:  v.ContentType,
		Size:         v.Size,
		EquipmentId:  v.EquipmentId,
		IsDeleted:    1,
		CreatedAt:    now,
		UpdatedAt:    now,
	}
	if err := r.DB(ctx).Create(SourceMaterial).Error; err != nil {
		return err
	}
	r.OperationLogRepository.AddOperationLogData(ctx, SourceMaterial.ID, v1.ActionCreate, v1.ModuleSourceMaterial)
	return nil
}

// UpdateMaterialsGroup 批量更新素材的分组
func (r *sourceMaterialRepository) UpdateMaterialsGroup(ctx *gin.Context, materialIds []int, groupId int) error {
	if len(materialIds) == 0 {
		return nil
	}

	now := time.Now().Unix()
	updates := map[string]interface{}{
		"group_id":   groupId,
		"updated_at": now,
	}

	return r.db.WithContext(ctx).Model(&model.SourceMaterial{}).
		Where("id IN ? AND is_deleted = ?", materialIds, 1).
		Updates(updates).Error
}

func (r *sourceMaterialRepository) BatchCreate(ctx *gin.Context, materials []*model.SourceMaterial) error {
	if err := r.DB(ctx).Create(materials).Error; err != nil {
		return err
	}
	for _, material := range materials {
		r.OperationLogRepository.AddOperationLogData(ctx, material.ID, v1.ActionCreate, v1.ModuleSourceMaterial)
	}
	return nil
}

func (r *sourceMaterialRepository) Edit(ctx *gin.Context, id int, req v1.SaveSourceMaterialRequest) error {
	req.UpdatedAt = time.Now().Unix()
	if err := r.DB(ctx).Table("source_material").Where("id = ?", id).Updates(req).Error; err != nil {
		return err
	}
	r.OperationLogRepository.AddOperationLogData(ctx, id, v1.ActionEdit, v1.ModuleSourceMaterial)
	return nil
}
