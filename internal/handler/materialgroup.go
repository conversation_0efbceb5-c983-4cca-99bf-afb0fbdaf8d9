package handler

import (
	v1 "esop/api/v1"
	"esop/internal/service"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

type MaterialGroupHandler struct {
	*Handler
	materialGroupService service.MaterialGroupService
}

func NewMaterialGroupHandler(handler *Handler, materialGroupService service.MaterialGroupService) *MaterialGroupHandler {
	return &MaterialGroupHandler{
		Handler:              handler,
		materialGroupService: materialGroupService,
	}
}

// GetList 获取分组列表
func (h *MaterialGroupHandler) GetList(ctx *gin.Context) {
	var req v1.MaterialGroupListRequest
	if err := ctx.ShouldBindQuery(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, err)
		return
	}

	total, groups, err := h.materialGroupService.GetMaterialGroupList(ctx, req)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, err)
		return
	}

	v1.HandleSuccess(ctx, nil, v1.PagingResponse{
		PagingData: *v1.Paging(total, req.PagingRequest),
		Data:       groups,
	})
}

// GetAllGroups 获取所有分组（不分页）
func (h *MaterialGroupHandler) GetAllGroups(ctx *gin.Context) {
	groups, err := h.materialGroupService.GetAllGroups(ctx)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, err)
		return
	}

	v1.HandleSuccess(ctx, nil, groups)
}

// GetDetail 获取分组详情
func (h *MaterialGroupHandler) GetDetail(ctx *gin.Context) {
	id, _ := strconv.Atoi(ctx.Query("id"))
	detail, err := h.materialGroupService.GetDetail(ctx, id)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrPramaterError, err)
		return
	}
	v1.HandleSuccess(ctx, nil, detail)
}

// Create 创建分组
func (h *MaterialGroupHandler) Create(ctx *gin.Context) {
	var req v1.SaveMaterialGroupRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrPramaterError, err)
		return
	}

	if err := h.materialGroupService.CreateMaterialGroup(ctx, &req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, err)
		return
	}

	v1.HandleSuccess(ctx, nil, nil)
}

// Update 更新分组
func (h *MaterialGroupHandler) Update(ctx *gin.Context) {
	var req v1.SaveMaterialGroupRequest
	id, err := strconv.Atoi(ctx.Param("id"))
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrPramaterError, err)
		return
	}

	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, err)
		return
	}

	if err := h.materialGroupService.EditMaterialGroup(ctx, id, req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrUpdateError, err)
		return
	}

	v1.HandleSuccess(ctx, nil, nil)
}

// Delete 删除分组
func (h *MaterialGroupHandler) Delete(ctx *gin.Context) {
	id, _ := strconv.Atoi(ctx.Param("id"))
	err := h.materialGroupService.DeleteMaterialGroup(ctx, id)
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrBadRequest, err)
		return
	}
	v1.HandleSuccess(ctx, nil, nil)
}

// MoveMaterialsToGroup 移动素材到分组
func (h *MaterialGroupHandler) MoveMaterialsToGroup(ctx *gin.Context) {
	var req v1.MoveMaterialsToGroupRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrPramaterError, err)
		return
	}

	if err := h.materialGroupService.MoveMaterialsToGroup(ctx, &req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, err)
		return
	}

	v1.HandleSuccess(ctx, nil, nil)
}