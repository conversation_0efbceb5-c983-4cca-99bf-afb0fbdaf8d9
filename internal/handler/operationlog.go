package handler

import (
	"github.com/gin-gonic/gin"
	"esop/internal/service"
	v1 "esop/api/v1"
	"net/http"
	// "strconv"
)

type OperationLogHandler struct {
	*Handler
	operationLogService service.OperationLogService
}

func NewOperationLogHandler(handler *Handler, operationLogService service.OperationLogService) *OperationLogHandler {
	return &OperationLogHandler{
		Handler:      handler,
		operationLogService: operationLogService,
	}
}

func (h *OperationLogHandler) GetOperationLog(ctx *gin.Context) {

}

func (h *OperationLogHandler) GetList(ctx *gin.Context) {
	var req v1.OperationLogRequest
	if err := ctx.ShouldBindQuery(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, err)
		return
	}
	total, operationLogList, err := h.operationLogService.GetList(ctx, req)

	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	v1.HandleSuccess(ctx, nil, v1.PagingResponse{
		PagingData: *v1.Paging(total, req.PagingRequest),
		Data:       operationLogList,
	})

}
