package handler

import (
	v1 "esop/api/v1"
	"esop/internal/service"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	// "go.uber.org/zap"
)

type AdminHandler struct {
	*Handler
	adminService service.AdminService
	// mqtt         mqtt.Client
}

func NewAdminHandler(handler *Handler, adminService service.AdminService) *AdminHandler {
	return &AdminHandler{
		Handler:      handler,
		adminService: adminService,
	}
}

func (h *AdminHandler) GetAdmin(ctx *gin.Context) {

}

func (h *AdminHandler) AddAdmin(ctx *gin.Context) {
	var req v1.SaveAdminListRequest
	if err := ctx.ShouldBind(&req); err != nil {
		v1.HandleError(ctx, http.StatusOK, v1.ErrPramaterError, nil)
		return
	}

	if err := h.adminService.AddAdmin(ctx, &req); err != nil {
		v1.HandleError(ctx, http.StatusOK, err, err)
		return
	}
	v1.HandleSuccess(ctx, nil, nil)

}
func (h *AdminHandler) EditAdmin(ctx *gin.Context) {
	var req v1.SaveAdminListRequest
	if err := ctx.ShouldBind(&req); err != nil {
		v1.HandleError(ctx, http.StatusOK, v1.ErrPramaterError, nil)
		return
	}

	id, err := strconv.Atoi(ctx.Param("id"))

	if err != nil {
		v1.HandleError(ctx, http.StatusOK, v1.ErrPramaterError, err)
		return
	}
	if err := h.adminService.EditAdmin(ctx, id, req); err != nil {
		v1.HandleError(ctx, http.StatusOK, v1.ErrUpdateError, err)
		return
	}
	v1.HandleSuccess(ctx, nil, nil)

}

func (h *AdminHandler) GetList(ctx *gin.Context) {
	var req v1.AdminListRequest
	if err := ctx.ShouldBindQuery(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, err)
		return
	}
	total, adminList, err := h.adminService.GetList(ctx, req)

	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	v1.HandleSuccess(ctx, nil, v1.PagingResponse{
		PagingData: *v1.Paging(total, req.PagingRequest),
		Data:       adminList,
	})

}
func (h *AdminHandler) DeleteAdmin(ctx *gin.Context) {
	id, _ := strconv.Atoi(ctx.Param("id")) // 从路由参数中获取id--
	err := h.adminService.DeleteAdmin(ctx, id)
	if err != nil {
		v1.HandleError(ctx, http.StatusOK, v1.ErrBadRequest, err)
		return
	}
	v1.HandleSuccess(ctx, nil, nil)

}
func (h *AdminHandler) EditUsing(ctx *gin.Context) {
	var req v1.UpdateAdminUsingRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusOK, v1.ErrBadRequest, err)
		return
	}
	if err := h.adminService.EditUsing(ctx, req); err != nil {
		v1.HandleError(ctx, http.StatusOK, v1.ErrUpdateError, err)
		return
	}
	v1.HandleSuccess(ctx, nil, nil)

}

func (h *AdminHandler) Login(ctx *gin.Context) {
	var req v1.LoginRequest

	if err := ctx.ShouldBind(&req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrBadRequest, nil)
		return
	}

	token, err := h.adminService.Login(ctx, &req)
	if err != nil {
		v1.HandleError(ctx, http.StatusUnauthorized, err, nil)
		return
	}
	// v1.HandleSuccess(ctx, nil, v1.LoginResponseData{
	// 	Data:       adminList,
	// })
	v1.HandleSuccess(ctx, nil, token)
}

func (h *AdminHandler) Sub(ctx *gin.Context) {

	// fmt.Printf("SDFDSSD"+msg.Msg)
	// fmt.Printf("Subscribed to top
	// idsfsdfsdfsdsdsdfsdfsdfc: %s", topic)

}

// func (h *AdminHandler) Pub(ctx *gin.Context) {

// 	h.logger.Info("test", zap.String("test", "1"))
// 	str := `{"msg": "BBB"}`

// 	res := h.mqtt.Publish("youta", 0, true, str)

// 	if res.Wait() {
// 		if err := res.Error(); err != nil {
// 			h.logger.Error("push", zap.Error(err))
// 		}

// 	}
// }
