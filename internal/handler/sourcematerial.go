package handler

import (
	"net/http"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	v1 "esop/api/v1"
	"esop/internal/service"
	"io"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type SourceMaterialHandler struct {
	*Handler
	sourceMaterialService service.SourceMaterialService
}

func NewSourceMaterialHandler(handler *<PERSON>ler, sourceMaterialService service.SourceMaterialService) *SourceMaterialHandler {
	return &SourceMaterialHandler{
		Handler:               handler,
		sourceMaterialService: sourceMaterialService,
	}
}

func (h *SourceMaterialHandler) AddSourceMaterial(ctx *gin.Context) {
	var req v1.SaveSourceMaterialRequest
	if err := ctx.ShouldBind(&req); err != nil {
		v1.HandleError(ctx, http.StatusOK, v1.ErrPramaterError, nil)
		return
	}

	if err := h.sourceMaterialService.AddSourceMaterial(ctx, &req); err != nil {
		v1.HandleError(ctx, http.StatusOK, err, err)
		return
	}
	v1.HandleSuccess(ctx, nil, nil)
}

func (h *SourceMaterialHandler) EditSourceMaterialInfo(ctx *gin.Context) {
	var req v1.SaveSourceMaterialRequest
	id, err := strconv.Atoi(ctx.Param("id"))

	if err != nil {
		v1.HandleError(ctx, http.StatusOK, v1.ErrPramaterError, err)
		return
	}
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusOK, v1.ErrBadRequest, err)
		return
	}
	if err := h.sourceMaterialService.EditSourceMaterialInfo(ctx, id, req); err != nil {
		v1.HandleError(ctx, http.StatusOK, v1.ErrUpdateError, err)
		return
	}
	v1.HandleSuccess(ctx, nil, nil)
}

// Upload 方法用于处理素材上传
func (h *SourceMaterialHandler) Upload(ctx *gin.Context) {
	// 获取上传的文件
	file, header, err := ctx.Request.FormFile("uploadFile")
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, err)
		return
	}
	defer file.Close()

	// 从表单获取 source_width 和 source_height
	sourceWidthStr := ctx.PostForm("source_width")
	sourceHeightStr := ctx.PostForm("source_height")
	sourceWidth, _ := strconv.Atoi(sourceWidthStr)
	sourceHeight, _ := strconv.Atoi(sourceHeightStr)

	// 确定当前目录
	dir, err := os.Getwd()
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, err)
		return
	}

	// 确定 media 目录路径
	mediaDir := filepath.Join(dir, "assets/media")
	if _, err := os.Stat(mediaDir); os.IsNotExist(err) {
		err = os.MkdirAll(mediaDir, 0755)
		if err != nil {
			h.logger.Error("创建目录失败", zap.Error(err))
			v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, err)
			return
		}
	}

	// 创建文件
	name := strconv.FormatInt(time.Now().Unix(), 10) + "_" + header.Filename
	outPath := filepath.Join(mediaDir, name)
	out, err := os.Create(outPath)
	if err != nil {
		h.logger.Error("创建文件失败", zap.Error(err))
		v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, err)
		return
	}
	defer out.Close()

	// 将上传的文件内容复制到新创建的文件中
	_, err = io.Copy(out, file)
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, err)
		return
	}

	// 准备数据写入数据库
	fileType := 1 // 默认为图片
	contentType := header.Header.Get("Content-Type")
	if strings.HasPrefix(contentType, "video") {
		fileType = 2
	}

	req := &v1.SaveSourceMaterialRequest{
		Name:         header.Filename,
		Type:         fileType,
		Path:         name, // 保存到数据库的相对路径
		SourceWidth:  sourceWidth,
		SourceHeight: sourceHeight,
		ContentType:  contentType,
	}

	if err := h.sourceMaterialService.AddSourceMaterial(ctx, req); err != nil {
		// 如果数据库写入失败，删除已上传的文件
		os.Remove(outPath)
		v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, err)
		return
	}

	data := make(map[string]interface{})
	outPath = strings.Replace(outPath, "\\", "/", -1)
	data["file_path"] = outPath
	data["file_name"] = name

	ctx.JSON(http.StatusOK, gin.H{
		"code": 0,
		"msg":  "success",
		"data": data,
	})
}

func (h *SourceMaterialHandler) GetList(ctx *gin.Context) {
	var req v1.SourceMaterialListRequest
	if err := ctx.ShouldBindQuery(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, err)
		return
	}
	total, Template, err := h.sourceMaterialService.GetSourceMaterialList(ctx, req)

	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	v1.HandleSuccess(ctx, nil, v1.PagingResponse{
		PagingData: *v1.Paging(total, req.PagingRequest),
		Data:       Template,
	})

}

func (h *SourceMaterialHandler) GetDetail(ctx *gin.Context) {
	id, _ := strconv.Atoi(ctx.Query("id"))
	detail, err := h.sourceMaterialService.GetDetail(ctx, id)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrPramaterError, err)
		return
	}
	v1.HandleSuccess(ctx, nil, detail)

}
func (h *SourceMaterialHandler) DeleteSourceMaterial(ctx *gin.Context) {
	var req v1.BatchDeleteRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if len(req.IDs) == 0 {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, "no ids provided")
		return
	}

	// Convert []int64 to []int
	var ids []int
	for _, id := range req.IDs {
		ids = append(ids, int(id))
	}

	err := h.sourceMaterialService.DeleteSourceMaterial(ctx, ids...)
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil, nil)
}



func (h *SourceMaterialHandler) BatchUpload(ctx *gin.Context) {
	form, err := ctx.MultipartForm()
	groupId, err := strconv.Atoi(ctx.PostForm("groupId"))
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, err)
		return
	}

	files := form.File["files"]
	if len(files) == 0 {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	sourceWidths := ctx.PostFormArray("source_widths")
	sourceHeights := ctx.PostFormArray("source_heights")
	sizes := ctx.PostFormArray("sizes")

	successCount, failureCount, failedFiles, err := h.sourceMaterialService.BatchUpload(ctx, files, groupId, sourceWidths, sourceHeights,sizes)
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, err)
		return
	}

	v1.HandleSuccess(ctx, nil, v1.BatchUploadResponse{
		SuccessCount: successCount,
		FailureCount: failureCount,
		FailedFiles:  failedFiles,
	})
}

func (h *SourceMaterialHandler) UploadToEquipment(ctx *gin.Context) {
	// 解析表单数据
	form, err := ctx.MultipartForm()
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, err)
		return
	}

	// 检查是否有上传文件
	files := form.File["files"]
	if len(files) == 0 {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, "no files uploaded")
		return
	}

	// 解析设备ID
	equipmentIdStr := ctx.PostForm("equipment_id")
	equipmentId, err := strconv.Atoi(equipmentIdStr)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrPramaterError, "invalid equipment_id")
		return
	}

	// 调用服务层处理文件上传
	successCount, failureCount, err := h.sourceMaterialService.UploadToEquipment(ctx, files, equipmentId)
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, err)
		return
	}

	v1.HandleSuccess(ctx, nil, gin.H{
		"success_count": successCount,
		"failure_count": failureCount,
	})
}

// BatchUploadNewWithGroup 新的批量上传接口，支持分组
func (h *SourceMaterialHandler) BatchUploadNewWithGroup(ctx *gin.Context) {
	form, err := ctx.MultipartForm()
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, err)
		return
	}

	files := form.File["files"]
	if len(files) == 0 {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	sourceWidths := ctx.PostFormArray("source_widths")
	sourceHeights := ctx.PostFormArray("source_heights")
	sizes := ctx.PostFormArray("sizes")

	// 获取分组ID，默认为0（未分组）
	groupIdStr := ctx.PostForm("group_id")
	groupId := 0
	if groupIdStr != "" {
		groupId, _ = strconv.Atoi(groupIdStr)
	}

	// 调用支持分组的批量上传服务
	successCount, failureCount, failedFiles, err := h.sourceMaterialService.BatchUploadNewWithGroup(ctx, files, sourceWidths, sourceHeights, sizes, groupId)
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, err)
		return
	}

	v1.HandleSuccess(ctx, nil, v1.BatchUploadResponse{
		SuccessCount: successCount,
		FailureCount: failureCount,
		FailedFiles:  failedFiles,
	})
}

// BatchUploadNew 新的批量上传接口，支持自动识别素材名称和类型
func (h *SourceMaterialHandler) BatchUploadNew(ctx *gin.Context) {
	form, err := ctx.MultipartForm()
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, err)
		return
	}

	files := form.File["files"]
	if len(files) == 0 {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	sourceWidths := ctx.PostFormArray("source_widths")
	sourceHeights := ctx.PostFormArray("source_heights")
	sizes := ctx.PostFormArray("sizes")

	// 调用新的批量上传服务
	successCount, failureCount, failedFiles, err := h.sourceMaterialService.BatchUploadNew(ctx, files, sourceWidths, sourceHeights, sizes)
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, err)
		return
	}

	v1.HandleSuccess(ctx, nil, v1.BatchUploadResponse{
		SuccessCount: successCount,
		FailureCount: failureCount,
		FailedFiles:  failedFiles,
	})
}

// UpdateMaterialsGroup 批量更新素材分组
func (h *SourceMaterialHandler) UpdateMaterialsGroup(ctx *gin.Context) {
	var req v1.MoveMaterialsToGroupRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrPramaterError, err)
		return
	}

	if err := h.sourceMaterialService.UpdateMaterialsGroup(ctx, req.MaterialIds, req.GroupId); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, err)
		return
	}

	v1.HandleSuccess(ctx, nil, nil)
}


