package handler

import (
	v1 "esop/api/v1"
	"esop/internal/service"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

type EquipmentHandler struct {
	*Handler
	equipmentService service.EquipmentService
}

func NewEquipmentHandler(handler *Handler, equipmentService service.EquipmentService) *EquipmentHandler {
	return &EquipmentHandler{
		Handler:          handler,
		equipmentService: equipmentService,
	}
}

func (h *EquipmentHandler) GetEquipment(ctx *gin.Context) {
	macAddress := ctx.Param("macAddress")
	equipment, err := h.equipmentService.GetEquipment(ctx, macAddress)
	if err != nil {
		v1.HandleError(ctx, http.StatusOK, v1.ErrBadRequest, err)
		return
	}
	v1.HandleSuccess(ctx, nil, equipment)
}

func (h *EquipmentHandler) GetGroupList(ctx *gin.Context) {
	var req v1.EquipmentListGroupRequest
	if err := ctx.ShouldBindQuery(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, err)
		return
	}
	total, equipmentGroupList, err := h.equipmentService.GetEquipmentGroupList(ctx, req)

	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	v1.HandleSuccess(ctx, nil, v1.PagingResponse{
		PagingData: *v1.Paging(total, req.PagingRequest),
		Data:       equipmentGroupList,
	})

}
func (h *EquipmentHandler) GetList(ctx *gin.Context) {
	var req v1.EquipmentListRequest
	if err := ctx.ShouldBindQuery(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, err)
		return
	}
	total, equipmentList, err := h.equipmentService.GetEquipmentList(ctx, req)

	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	v1.HandleSuccess(ctx, nil, v1.PagingResponse{
		PagingData: *v1.Paging(total, req.PagingRequest),
		Data:       equipmentList,
	})

}

func (h *EquipmentHandler) AddEquipment(ctx *gin.Context) {
	var req v1.SaveEquipmentListRequest
	if err := ctx.ShouldBind(&req); err != nil {
		v1.HandleError(ctx, http.StatusOK, v1.ErrPramaterError, nil)
		return
	}

	if err := h.equipmentService.AddEquipment(ctx, &req); err != nil {
		v1.HandleError(ctx, http.StatusOK, err, nil)
		return
	}
	v1.HandleSuccess(ctx, nil, nil)
}

func (h *EquipmentHandler) DeleteEquipment(ctx *gin.Context) {
	id, _ := strconv.Atoi(ctx.Param("id")) // 从路由参数中获取id
	err := h.equipmentService.DeleteEquipment(ctx, id)
	if err != nil {
		v1.HandleError(ctx, http.StatusOK, v1.ErrBadRequest, err)
		return
	}
	v1.HandleSuccess(ctx, nil, nil)

}

// 检查该设备是否已经注册激活
func (h *EquipmentHandler) CheckEquipment(ctx *gin.Context) {
	macAddress := ctx.Query("mac_address")
	err := h.equipmentService.CheckEquipment(ctx, macAddress)
	if err != nil {
		v1.HandleError(ctx, http.StatusOK, err, err)
		return
	}
	v1.HandleSuccess(ctx, nil, nil)

}

func (h *EquipmentHandler) LifeEquipment(ctx *gin.Context) {
	var req v1.SaveEquipmentListRequest
	if err := ctx.ShouldBind(&req); err != nil {
		v1.HandleError(ctx, http.StatusOK, v1.ErrPramaterError, nil)
		return
	}
	data, err := h.equipmentService.LifeEquipment(ctx, req.MacAddress)
	if err != nil {
		v1.HandleError(ctx, http.StatusOK, err, err)
		return
	}
	if data.MacAddress == "" {
		v1.HandleError(ctx, http.StatusOK, v1.NewError(-1, "设备不存在"), nil)
		return
	}

	v1.HandleSuccess(ctx, nil, data)
}

func (h *EquipmentHandler) GetTreeList(ctx *gin.Context) {
	var req v1.EquipmentListRequest
	if err := ctx.ShouldBindQuery(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, err)
		return
	}
	equipmentTreeList, err := h.equipmentService.GetEquipmentTreeList(ctx, req)

	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, err, err)
		return
	}

	v1.HandleSuccess(ctx, nil, equipmentTreeList)
}

func (h *EquipmentHandler) EditEquipment(ctx *gin.Context) {
	var req v1.SaveEquipmentListRequest
	if err := ctx.ShouldBind(&req); err != nil {
		v1.HandleError(ctx, http.StatusOK, v1.ErrPramaterError, nil)
	}
	if err := h.equipmentService.EditEquipment(ctx, req); err != nil {
		v1.HandleError(ctx, http.StatusOK, err, err)
		return
	}
	v1.HandleSuccess(ctx, nil, nil)
}

func (h *EquipmentHandler) GroupNameList(ctx *gin.Context) {

	list, err := h.equipmentService.GroupNameList(ctx)
	if err != nil {
		v1.HandleError(ctx, http.StatusOK, err, err)
		return
	}
	v1.HandleSuccess(ctx, nil, list)
}

func (h *EquipmentHandler) GetOTA(ctx *gin.Context) {
	v1.HandleSuccess(ctx, nil, map[string]string{
		"version": "v1.0.0",
		"ota_url": "ota/download/v1.0.0/app.zip",
	})
}

func (h *EquipmentHandler) EquipmentLog(ctx *gin.Context) {

}

func (h *EquipmentHandler) DeleteGroup(ctx *gin.Context) {

	groud_id, err := strconv.Atoi(ctx.Param("groud_id"))
	if err != nil {
		v1.HandleError(ctx, http.StatusOK, v1.ErrBadRequest, err)
		return
	}
	if err := h.equipmentService.DeleteGroup(ctx, groud_id); err != nil {
		v1.HandleError(ctx, http.StatusOK, err, err)
		return
	}
	v1.HandleSuccess(ctx, nil, nil)
}

func (h *EquipmentHandler) AddGroup(ctx *gin.Context) {
	var req v1.GroupRequest
	if err := ctx.ShouldBind(&req); err != nil {
		v1.HandleError(ctx, http.StatusOK, v1.ErrPramaterError, nil)
		return
	}
	if err := h.equipmentService.AddGroup(ctx, req); err != nil {
		v1.HandleError(ctx, http.StatusOK, err, err)
		return
	}
	v1.HandleSuccess(ctx, nil, nil)
}

// 设备上报数据处理
func (h *EquipmentHandler) EquipmentReported(ctx *gin.Context) {
	var req v1.ReportedData
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.NewError(400, "Bad Request"), err)
		return
	}

	// 调用服务层处理上报数据
	err := h.equipmentService.ProcessEquipmentReport(ctx, req)
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, v1.NewError(500, "Internal Server Error"), err)
		return
	}

	v1.HandleSuccess(ctx, nil, v1.ReportedResponse{
		ReceivedCount: 1, // 表示接收了一个上报数据
		ProcessedCount: 1, // 表示处理了一个上报数据
	})
}

// 批量设备上报数据处理
func (h *EquipmentHandler) EquipmentReportedBatch(ctx *gin.Context) {
	var req v1.ReportedBatchRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.NewError(400, "Bad Request"), err)
		return
	}

	// 调用服务层处理批量上报数据
	err := h.equipmentService.ProcessEquipmentReportBatch(ctx, req.Reports)
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, v1.NewError(500, "Internal Server Error"), err)
		return
	}

	v1.HandleSuccess(ctx, nil, v1.ReportedResponse{
		ReceivedCount: len(req.Reports), // 接收的上报数据数量
		ProcessedCount: len(req.Reports), // 处理的上报数据数量
	})
}

func (h *EquipmentHandler) SendCommand(ctx *gin.Context) {
	var req v1.SendCommandRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, err)
		return
	}


	taskId, err := h.equipmentService.SendCommand(ctx, req)
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, err)
		return
	}

	v1.HandleSuccess(ctx, nil, gin.H{
		"task_id": taskId,
	})
}

func (h *EquipmentHandler) GetMaterial(ctx *gin.Context) {
	id, _ := strconv.Atoi(ctx.Param("id"))
	material, err := h.equipmentService.GetMaterialByEquipmentId(ctx, id)
	if err != nil {
		v1.HandleError(ctx, http.StatusOK, v1.ErrBadRequest, err)
		return
	}
	v1.HandleSuccess(ctx, nil, material)
}

func (h *EquipmentHandler) AssociateMaterial(ctx *gin.Context) {
	var req v1.AssociateMaterialRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, err)
		return
	}

	if err := h.equipmentService.AssociateMaterial(ctx, req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, err)
		return
	}

	v1.HandleSuccess(ctx, nil, nil)
}

func (h *EquipmentHandler) PushMaterial(ctx *gin.Context) {
	var req v1.PushMaterialRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, err)
		return
	}

	if err := h.equipmentService.PushMaterial(ctx, req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, err)
		return
	}

	v1.HandleSuccess(ctx, nil, nil)
}

func (h *EquipmentHandler) DisassociateMaterial(ctx *gin.Context) {
	var req v1.AssociateMaterialRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, err)
		return
	}

	if err := h.equipmentService.DisassociateMaterial(ctx, req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, err)
		return
	}

	v1.HandleSuccess(ctx, nil, nil)
}

func (h *EquipmentHandler) UploadScreenshot(ctx *gin.Context) {
	// 1. 解析参数
	groupName := ctx.PostForm("group_name")
	equipmentAliasName := ctx.PostForm("equipment_alias_name")
	taskId := ctx.PostForm("task_id") // 新增
	file, err := ctx.FormFile("file")
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.NewError(400, "Invalid request parameters"), err)
		return
	}

	// 2. 调用服务层处理
	imageURL, err := h.equipmentService.UploadScreenshot(ctx, file, groupName, equipmentAliasName, taskId)
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, v1.NewError(500, "Failed to upload screenshot"), err)
		return
	}

	// 3. 返回成功响应
	v1.HandleSuccess(ctx, nil, gin.H{
		"imageUrl": imageURL,
	})
}

func (h *EquipmentHandler) GetLatestScreenshot(ctx *gin.Context) {
	// 1. 解析参数
	groupName := ctx.Query("group_name")
	equipmentAliasName := ctx.Query("equipment_alias_name")

	if groupName == "" || equipmentAliasName == "" {
		v1.HandleError(ctx, http.StatusBadRequest, v1.NewError(400, "Missing required parameters"), nil)
		return
	}

	// 2. 调用服务层处理
	screenshot, err := h.equipmentService.GetLatestScreenshotByEquipment(ctx, groupName, equipmentAliasName)
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, v1.NewError(500, "Failed to get latest screenshot"), err)
		return
	}

	// 3. 返回成功响应
	if screenshot != nil {
		v1.HandleSuccess(ctx, nil, screenshot)
	} else {
		// 如果没有5秒内的截图，返回空数据
		v1.HandleSuccess(ctx, nil, nil)
	}
}

func (h *EquipmentHandler) BatchPushMaterial(ctx *gin.Context) {
	var req v1.BatchPushMaterialRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, err)
		return
	}

	if err := h.equipmentService.BatchPushMaterial(ctx, req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, err)
		return
	}

	v1.HandleSuccess(ctx, nil, nil)
}

func (h *EquipmentHandler) GetScreenshotByTaskId(ctx *gin.Context) {
	taskId := ctx.Query("task_id")
	if taskId == "" {
		v1.HandleError(ctx, http.StatusBadRequest, v1.NewError(400, "Missing task_id"), nil)
		return
	}

	screenshot, err := h.equipmentService.GetScreenshotByTaskId(ctx, taskId)
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, v1.NewError(500, "Failed to get screenshot"), err)
		return
	}

	if screenshot != nil {
		v1.HandleSuccess(ctx, nil, screenshot)
	} else {
		v1.HandleSuccess(ctx, nil, nil) // No screenshot found yet
	}
}

func (h *EquipmentHandler) DeleteScreenshot(ctx *gin.Context) {
	var req v1.DeleteScreenshotRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, err)
		return
	}

	if err := h.equipmentService.DeleteEquipmentScreenshot(ctx, req.ID); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, err)
		return
	}

	v1.HandleSuccess(ctx, nil, nil)
}

func (h *EquipmentHandler) GetEquipmentLog(ctx *gin.Context) {
	var req v1.EquipmentLogRequest
	if err := ctx.ShouldBindQuery(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, err)
		return
	}

	total, logs, err := h.equipmentService.GetEquipmentLog(ctx, req)
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, err)
		return
	}

	v1.HandleSuccess(ctx, nil, v1.PagingResponse{
		PagingData: *v1.Paging(total, req.PagingRequest),
		Data:       logs,
	})
}

func (h *EquipmentHandler) GetLatestDeviceReport(ctx *gin.Context) {
	var req v1.LatestDeviceReportRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, err)
		return
	}

	reports, err := h.equipmentService.GetLatestDeviceReport(ctx, req)
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, err)
		return
	}

	v1.HandleSuccess(ctx, nil, reports)
}

func (h *EquipmentHandler) ClearEquipmentLog(ctx *gin.Context) {
	var req v1.ClearEquipmentLogRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, err)
		return
	}

	if err := h.equipmentService.ClearEquipmentLog(ctx, req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, err)
		return
	}

	v1.HandleSuccess(ctx, nil, nil)
}

func (h *EquipmentHandler) SaveEquipmentPowerSchedules(ctx *gin.Context) {
	var req v1.SaveSchedulePowerRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, err)
		return
	}

	if err := h.equipmentService.SaveEquipmentPowerSchedules(ctx, req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, err)
		return
	}

	v1.HandleSuccess(ctx, nil, nil)
}

func (h *EquipmentHandler) GetEquipmentPowerSchedules(ctx *gin.Context) {
	equipmentId, err := strconv.Atoi(ctx.Param("id"))
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, err)
		return
	}

	schedules, err := h.equipmentService.GetEquipmentPowerSchedules(ctx, equipmentId)
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, err)
		return
	}

	v1.HandleSuccess(ctx, nil, schedules)
}

func (h *EquipmentHandler) BatchDeleteFiles(ctx *gin.Context) {
	var req v1.BatchDeleteFilesRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, err)
		return
	}

	if err := h.equipmentService.BatchDeleteFiles(ctx, req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, err)
		return
	}

	v1.HandleSuccess(ctx, nil, nil)
}
