package handler

import (
	"errors"
	v1 "esop/api/v1"
	"esop/internal/service"
	"io"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
)

// POST /admin/license/server/license/generate
func (h *LicenseHandler) GenerateServerLicense(ctx *gin.Context) {
	var req v1.GenerateServerLicenseRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, err); return
	}
	b, name, err := h.svc.GenerateServerLicense(ctx, req.MachineCode, req.ExpireAt)
	if err != nil { v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, err); return }
	ctx.Writer.Header().Set("Content-Disposition", "attachment; filename="+name)
	ctx.Data(http.StatusOK, "application/json", b)
}

type LicenseHandler struct { *Handler; svc service.LicenseService }

func NewLicenseHandler(h *Handler, s service.LicenseService) *LicenseHandler { return &LicenseHandler{Handler: h, svc: s} }

// GET /admin/license/server/machine_code/export -> file
func (h *LicenseHandler) ExportServerMachineCode(ctx *gin.Context) {
	b, name, err := h.svc.ExportServerMachineCode(ctx)
	if err != nil { v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, err); return }
	ctx.Writer.Header().Set("Content-Disposition", "attachment; filename="+name)
	ctx.Data(http.StatusOK, "application/json", b)
}

// POST /admin/license/server/license/import (multipart)
func (h *LicenseHandler) ImportServerLicense(ctx *gin.Context) {
	file, err := ctx.FormFile("file")
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, err)
		return
	}
	// check file extension
	if !strings.HasSuffix(file.Filename, ".dat") {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, errors.New("invalid file type, only .dat is allowed"))
		return
	}
	f, _ := file.Open()
	defer f.Close()
	b, _ := io.ReadAll(f)
	isDat := strings.HasSuffix(file.Filename, ".dat")
	if err := h.svc.ImportServerLicense(ctx, b, isDat); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, err)
		return
	}
	v1.HandleSuccess(ctx, nil, gin.H{"ok": true})
}

// GET /admin/license/server/status
func (h *LicenseHandler) GetServerStatus(ctx *gin.Context) {
	res, err := h.svc.GetServerStatus(ctx)
	if err != nil { v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, err); return }
	v1.HandleSuccess(ctx, nil, res)
}

// GET /admin/license/terminal/status
func (h *LicenseHandler) GetTerminalStatus(ctx *gin.Context) {
	var req v1.TerminalStatusListRequest
	if err := ctx.ShouldBindQuery(&req); err != nil { v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, err); return }
	total, list, err := h.svc.GetTerminalStatus(ctx, req)
	if err != nil { v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, err); return }
	v1.HandleSuccess(ctx, nil, v1.TerminalStatusListResponse{ Total: total, List: list })
}

// GET /admin/license/terminal/machine_codes/export
// GET /admin/license/terminal/machine_codes/export_unregistered
func (h *LicenseHandler) ExportTerminalMachineCodes(ctx *gin.Context) {
	onlyUnreg := ctx.Query("only_unregistered") == "1"
	b, name, err := h.svc.ExportTerminalMachineCodes(ctx, onlyUnreg)
	if err != nil { v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, err); return }
	ctx.Writer.Header().Set("Content-Disposition", "attachment; filename="+name)
	ctx.Data(http.StatusOK, "application/json", b)
}

// POST /admin/license/terminal/registration_codes/import
func (h *LicenseHandler) ImportTerminalRegistrationCodes(ctx *gin.Context) {
	file, err := ctx.FormFile("file")
	if err != nil { v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, err); return }
	// check file extension
	if !strings.HasSuffix(file.Filename, ".dat") {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, errors.New("invalid file type, only .dat is allowed"))
		return
	}
	f, _ := file.Open(); defer f.Close()
	b, _ := io.ReadAll(f)
	if err := h.svc.ImportTerminalRegistrationCodes(ctx, b); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, err); return
	}
	v1.HandleSuccess(ctx, nil, gin.H{"ok": true})
}

