package handler

import (
	v1 "esop/api/v1"
	"esop/internal/service"
	"github.com/gin-gonic/gin"
	"net/http"
	"strconv"
)

type TemplateHandler struct {
	*Handler
	templateService service.TemplateService
}

func NewTemplateHandler(handler *Handler, templateService service.TemplateService) *TemplateHandler {
	return &TemplateHandler{
		Handler:         handler,
		templateService: templateService,
	}
}

func (h *TemplateHandler) GetTemplate(ctx *gin.Context) {

}

func (h *TemplateHandler) GetList(ctx *gin.Context) {
	var req v1.TemplateListRequest
	if err := ctx.ShouldBindQuery(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, err)
		return
	}
	total, Template, err := h.templateService.GetTemplateList(ctx, req)

	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	v1.HandleSuccess(ctx, nil, v1.PagingResponse{
		PagingData: *v1.Paging(total, req.PagingRequest),
		Data:       Template,
	})

}

func (h *TemplateHandler) DeleteTemplate(ctx *gin.Context) {
	id, _ := strconv.Atoi(ctx.Param("id")) // 从路由参数中获取id
	err := h.templateService.DeleteTemplate(ctx, id)
	if err != nil {
		v1.HandleError(ctx, http.StatusOK, v1.ErrBadRequest, err)
		return
	}
	v1.HandleSuccess(ctx, nil, nil)

}
func (h *TemplateHandler) AddTemplate(ctx *gin.Context) {
	var req v1.SaveTemplateListRequest
	if err := ctx.ShouldBind(&req); err != nil {
		v1.HandleError(ctx, http.StatusOK, v1.ErrPramaterError, nil)
		return
	}

	if err := h.templateService.AddTemplate(ctx, &req); err != nil {
		v1.HandleError(ctx, http.StatusOK, err, err)
		return
	}
	v1.HandleSuccess(ctx, nil, nil)
}
func (h *TemplateHandler) GetDetail(ctx *gin.Context) {
	id, _ := strconv.Atoi(ctx.Query("id"))
	detail, err := h.templateService.GetDetail(ctx, id)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrPramaterError, err)
		return
	}
	v1.HandleSuccess(ctx, nil, detail)

}
func (h *TemplateHandler) EditTemplateInfo(ctx *gin.Context) {
	var req v1.SaveTemplateListRequest
	id, err := strconv.Atoi(ctx.Param("id"))

	if err != nil {
		v1.HandleError(ctx, http.StatusOK, v1.ErrPramaterError, err)
		return
	}
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusOK, v1.ErrBadRequest, err)
		return
	}
	if err := h.templateService.EditTemplateInfo(ctx, id, req); err != nil {
		v1.HandleError(ctx, http.StatusOK, v1.ErrUpdateError, err)
		return
	}
	v1.HandleSuccess(ctx, nil, nil)

}
func (h *TemplateHandler) AddPack(ctx *gin.Context) {
	var req v1.SavePackRequest
	if err := ctx.ShouldBind(&req); err != nil {
		v1.HandleError(ctx, http.StatusOK, v1.ErrPramaterError, err)
		return
	}

	if err := h.templateService.AddPack(ctx, &req); err != nil {
		v1.HandleError(ctx, http.StatusOK, err, err)
		return
	}
	v1.HandleSuccess(ctx, nil, nil)
}
