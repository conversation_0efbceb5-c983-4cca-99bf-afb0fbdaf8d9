package model

// 说明:发送日志
// 表名:send_log
// version:2024-08-07
type SendLog struct {
	ID          int    `gorm:"column:id;primaryKey" json:"id"`                     // 编号
	SendContent string `gorm:"column:send_content" json:"send_content"`            // 发送内容
	CreatedAt   int64  `gorm:"column:created_at;autoCreateTime" json:"created_at"` // 创建时间
	UpdatedAt   int64  `gorm:"column:updated_at" json:"updated_at"`                // 更新时间
}

func (m *SendLog) TableName() string {
	return "send_log"
}
