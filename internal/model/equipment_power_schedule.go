package model

import "time"

// EquipmentPowerSchedule 说明:设备定时开关机时间表
// 表名:equipment_power_schedules
// version:2024-07-22
type EquipmentPowerSchedule struct {
	ID           int    `gorm:"column:id;primaryKey" json:"id"`
	EquipmentID  int    `gorm:"column:equipment_id;index" json:"equipment_id"` // 外键,关联equipment表
	PowerOnTime  string `gorm:"column:power_on_time;type:varchar(5)" json:"power_on_time"`
	PowerOffTime string `gorm:"column:power_off_time;type:varchar(5)" json:"power_off_time"`
	Weekdays     string `gorm:"column:weekdays;type:text" json:"weekdays"` // 存储星期几的JSON字符串
	Enabled      bool   `gorm:"column:enabled;default:true" json:"enabled"`
	CreatedAt    int64  `gorm:"column:created_at" json:"created_at"`
	UpdatedAt    int64  `gorm:"column:updated_at" json:"updated_at"`
}

func (m *EquipmentPowerSchedule) TableName() string {
	return "equipment_power_schedules"
}

// BeforeCreate GORM a hook that is called before creating a record
func (m *EquipmentPowerSchedule) BeforeCreate() error {
	m.CreatedAt = time.Now().Unix()
	m.UpdatedAt = time.Now().Unix()
	return nil
}

// BeforeUpdate GORM a hook that is called before updating a record
func (m *EquipmentPowerSchedule) BeforeUpdate() error {
	m.UpdatedAt = time.Now().Unix()
	return nil
}