package model

// 说明:资源包
// 表名:resource_pack
// version:2024-08-07
type ResourcePack struct {
	ID         int    `gorm:"column:id;primaryKey" json:"id"`                     // 编号
	Name       string `gorm:"column:name" json:"name"`                            // 资源包别名
	PackName   string `gorm:"column:pack_name" json:"pack_name"`                  // 资源包名称
	Hash       string `gorm:"column:hash" json:"hash"`                            // 资源包hash
	TemplateId int    `gorm:"column:template_id" json:"template_id"`              // 模板id
	CreatedAt  int64  `gorm:"column:created_at;" json:"created_at"`               // 创建时间
	UpdatedAt  int64  `gorm:"column:updated_at;autoCreateTime" json:"updated_at"` // 更新时间
	IsDeleted  int    `gorm:"column:is_deleted" json:"is_deleted"`                // 是否删除  1--未删 2--删除
}

func (m *ResourcePack) TableName() string {
	return "resource_pack"
}
