package model

// 说明:素材中心
// 表名:template
// version:2024-07-120
type SourceMaterial struct {
	ID           int    `gorm:"column:id;primaryKey" json:"id"`            // 编号
	Name         string `gorm:"column:name" json:"name"`                   // 模板名称
	Type         int    `gorm:"column:type" json:"type"`                   // 类型  1--图片 2--视频
	Path         string `gorm:"column:path" json:"path"`                   // 素材路径
	SourceWidth  int    `gorm:"column:source_width" json:"source_width"`   // 素材宽度
	SourceHeight int    `gorm:"column:source_height" json:"source_height"` // 素材高度
	Size         int64  `gorm:"column:size" json:"size"`                   // 文件大小
	CreatedAt    int64  `gorm:"column:created_at" json:"created_at"`       // 创建时间
	UpdatedAt    int64  `gorm:"column:updated_at" json:"updated_at"`       // 更新时间
	GroupName    string `gorm:"column:group_name" json:"group_name"`       // 素材分组名称
	AliasName    string `gorm:"column:alias_name" json:"alias_name"`       // 素材别名
	ContentType  string `gorm:"column:content_type" json:"content_type"`
	IsDeleted    int    `gorm:"column:is_deleted" json:"is_deleted"` // 是否删除  1--未删 2--删除
	EquipmentId  int    `gorm:"column:equipment_id" json:"equipment_id"`
	GroupId      int    `gorm:"column:group_id" json:"group_id"`         // 分组ID，关联material_group表
	Hash         string `gorm:"column:hash" json:"hash"`
}

func (m *SourceMaterial) TableName() string {
	return "source_material"
}
