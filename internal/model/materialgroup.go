package model

// MaterialGroup 素材分组模型
type MaterialGroup struct {
	ID          int    `gorm:"column:id;primaryKey" json:"id"`                // 分组ID
	Name        string `gorm:"column:name" json:"name"`                       // 分组名称
	Description string `gorm:"column:description" json:"description"`         // 分组描述
	Color       string `gorm:"column:color" json:"color"`                     // 分组颜色标识
	SortOrder   int    `gorm:"column:sort_order" json:"sort_order"`           // 排序
	CreatedAt   int64  `gorm:"column:created_at" json:"created_at"`           // 创建时间
	UpdatedAt   int64  `gorm:"column:updated_at" json:"updated_at"`           // 更新时间
	IsDeleted   int    `gorm:"column:is_deleted" json:"is_deleted"`           // 是否删除 1--未删 2--删除
}

func (m *MaterialGroup) TableName() string {
	return "material_group"
}