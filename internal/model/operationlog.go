package model

// 说明:操作日志
// 表名:operation_log
// version:2024-08-12
type OperationLog struct {
	ID           int    `gorm:"column:id;primaryKey" json:"id"`            // 编号
	AdminName    string `gorm:"column:admin_name" json:"admin_name"`       // 操作人名称
	AdminAccount string `gorm:"column:admin_account" json:"admin_account"` // 操作人账号
	Module       string `gorm:"column:module" json:"module"`               // 操作模块
	Action       string `gorm:"column:action" json:"action"`               // 操作动作
	ModuleId     int    `gorm:"column:module_id" json:"module_id"`         // 模块Id
	CreatedAt    int64  `gorm:"column:created_at;" json:"created_at"`      // 创建时间
	UpdatedAt    int64  `gorm:"column:updated_at" json:"updated_at"`       // 更新时间
}

func (m *OperationLog) TableName() string {
	return "operation_log"
}
