package model

// 说明:账号中心
// 表名:admin
// version:2024-07-120
type Admin struct {
	ID          int    `gorm:"column:id;primaryKey" json:"id"`            // 编号
	Name        string `gorm:"column:name" json:"name"`                   // 账号名称
	Account     string `gorm:"column:account" json:"account"`             // 账号
	Password    string `gorm:"column:password" json:"password"`           // 密码
	Level       int8   `gorm:"column:level" json:"level"`                 // 账号级别 0--管理员 1--用户
	IsStopUsing int8   `gorm:"column:is_stop_using" json:"is_stop_using"` // 是否停用 0--否 1--是
	CreatedAt   int64  `gorm:"column:created_at" json:"created_at"`       // 创建时间
	UpdatedAt   int64  `gorm:"column:updated_at" json:"updated_at"`       // 更新时间
	IsDeleted   int8   `gorm:"column:is_deleted" json:"is_deleted"`       // 是否删除

}

func (m *Admin) TableName() string {
	return "admin"
}
