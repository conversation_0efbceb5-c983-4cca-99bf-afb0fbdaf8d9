package model

import (
	"gorm.io/gorm"
)

// 说明: 设备上报数据表
// 表名: equipment_report
type EquipmentReport struct {
	ID               int64  `gorm:"column:id;primaryKey" json:"id"`
	MacAddress       string `gorm:"column:mac_address;index" json:"mac_address"`       // 设备MAC地址
	DeviceAlias      string `gorm:"column:device_alias" json:"device_alias"`           // 设备别名
	GroupName        string `gorm:"column:group_name" json:"group_name"`               // 组名
	Timestamp        string `gorm:"column:timestamp" json:"timestamp"`                 // 上报时间戳(ISO 8601格式)
	ReportType       string `gorm:"column:report_type" json:"report_type"`             // 上报类型
	OperationID      string `gorm:"column:operation_id" json:"operation_id"`           // 操作唯一标识
	Data             string `gorm:"column:data" json:"data"`                           // 上报数据，JSON格式
	CreatedAt        int64  `gorm:"column:created_at" json:"created_at"`               // 创建时间
}

func (m *EquipmentReport) TableName() string {
	return "equipment_report"
}

// AutoMigrateEquipmentReport 创建/更新设备上报数据表
func AutoMigrateEquipmentReport(db *gorm.DB) error {
	return db.AutoMigrate(&EquipmentReport{})
}
