package model

// 说明:模板中心。
// 表名:template
// version:2024-07-17
type Template struct {
	ID              int    `gorm:"column:id;primaryKey" json:"id"`                     // 编号
	Name            string `gorm:"column:name" json:"name"`                            // 模板名称
	ResolutionRatio string `gorm:"column:resolution_ratio" json:"resolution_ratio"`    // 分辨率
	CanvasRatio     string `gorm:"column:canvas_ratio" json:"canvas_ratio"`            // 画布尺寸
	Width           int    `gorm:"column:width" json:"width"`                          // 宽度
	Height          int    `gorm:"column:height" json:"height"`                        // 高度
	SwipterTime     int    `gorm:"column:swipter_time" json:"swipter_time"`            // 轮播时间
	CreatedAt       int64  `gorm:"column:created_at;autoCreateTime" json:"created_at"` // 创建时间
	Type            int    `gorm:"column:type" json:"type"`                            // 类型 1正常模板 2文件模板
	UpdatedAt       int64  `gorm:"column:updated_at" json:"updated_at"`                // 更新时间
	IsDeleted       int    `gorm:"column:is_deleted;" json:"is_deleted"`               // 是否删除
}

func (m *Template) TableName() string {
	return "template"
}
