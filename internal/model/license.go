package model

// ServerLicense stores server license info
// table: server_license (singleton row)

type ServerLicense struct {
	ID           int   `gorm:"column:id;primaryKey" json:"id"`
	MachineCode  string `gorm:"column:machine_code" json:"machine_code"`
	LicenseData string `gorm:"column:license_data" json:"license_data"` // raw license file content or encoded json
	ExpireAt    int64  `gorm:"column:expire_at" json:"expire_at"`
	CreatedAt   int64  `gorm:"column:created_at" json:"created_at"`
	UpdatedAt    int64  `gorm:"column:updated_at" json:"updated_at"`
}

func (m *ServerLicense) TableName() string { return "server_license" }

