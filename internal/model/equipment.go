package model

// 说明:设备中心
// 表名:equipment
// version:2024-07-19
type Equipment struct {
	ID               int    `gorm:"column:id;primaryKey" json:"id"`                                    // 编号
	DeviceNumber     string `gorm:"column:device_number" json:"device_number"`                         // 设备编号
	MacAddress       string `gorm:"uniqueIndex:idx_mac_address,column:mac_address" json:"mac_address"` // mac地址
	RegistrationCode string `gorm:"column:registration_code" json:"registration_code"`                 // 注册码
	AliasName        string `gorm:"column:alias_name" json:"alias_name"`                               //设备别名
	IpAddr           string `gorm:"column:ip_addr" json:"ip_addr"`                                     // ip地址
	Status           int    `gorm:"index:idx_status,column:status" json:"status"`                      // 设备状态   1 在线 2 离线
	IsDeleted        int    `gorm:"column:is_deleted" json:"is_deleted"`                               // 是否删除
	GroupName        string `gorm:"column:group_name" json:"group_name"`                               //分组名称
	GroupId          int    `gorm:"column:group_id" json:"group_id"`                                   //分组ID
	System           string `gorm:"column:system" json:"system"`                                       //系统名称
	SystemVersion    string `gorm:"column:system_version" json:"system_version"`                       //系统版本
	HardwareInfo     string `gorm:"column:hardware_info" json:"hardware_info"`                         // 硬件信息
	Manufacturer     string `gorm:"column:manufacturer" json:"manufacturer"`                           // 制造商
	Model            string `gorm:"column:model" json:"model"`                                         // CPU型号
	Brand            string `gorm:"column:brand" json:"brand"`                                         // 品牌
	Device           string `gorm:"column:device" json:"device"`                                       // 设备名称
	Product          string `gorm:"column:product" json:"product"`                                     // 产品名称
	AndroidId        string `gorm:"column:android_id" json:"android_id"`
	Hardware         string `gorm:"column:hardware" json:"hardware"`           // 硬件
	SerialNumber     string `gorm:"column:serial_number" json:"serial_number"` // 序列号
	ScreenWidth      int    `gorm:"column:screen_width" json:"screen_width"`   // 屏幕宽度
	ScreenHeight     int    `gorm:"column:screen_height" json:"screen_height"` // 屏幕高度
	AppVersion       string `gorm:"column:app_version" json:"app_version"` // app版本
	CreatedAt        int64  `gorm:"column:created_at" json:"created_at"`       // 创建时间
	UpdatedAt        int64  `gorm:"column:updated_at" json:"updated_at"`       // 更新时间

}

func (m *Equipment) TableName() string {
	return "equipment"
}
