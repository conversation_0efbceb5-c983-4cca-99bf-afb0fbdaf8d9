package model

// 说明:设备分组
// 表名:equipmentGroup
// version:2024-07-19
type EquipmentGroup struct {
	ID        int    `gorm:"column:id;primaryKey" json:"id"`      // 编号
	Name      string `gorm:"column:name" json:"name"`             // 设备名称
	Number    int    `gorm:"column:number" json:"number"`         //设备数量
	CreatedAt int64  `gorm:"column:created_at" json:"created_at"` // 创建时间
	UpdatedAt int64  `gorm:"column:updated_at" json:"updated_at"` // 更新时间

}

func (m *EquipmentGroup) TableName() string {
	return "equipment_group"
}
