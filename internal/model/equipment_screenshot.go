package model

import "time"

type EquipmentScreenshot struct {
	ID                 int64     `gorm:"primary_key;AUTO_INCREMENT" json:"id"`
	GroupName          string    `gorm:"type:varchar(255);not null;default:''" json:"group_name"`
	EquipmentAliasName string    `gorm:"type:varchar(255);not null;default:''" json:"equipment_alias_name"`
	ImageURL           string    `gorm:"type:varchar(255);not null;default:''" json:"image_url"`
	TaskID             string    `gorm:"type:varchar(255);not null;default:''" json:"task_id"`
	CreatedAt          time.Time `gorm:"default:CURRENT_TIMESTAMP" json:"created_at"`
}

func (EquipmentScreenshot) TableName() string {
	return "equipment_screenshot"
}
