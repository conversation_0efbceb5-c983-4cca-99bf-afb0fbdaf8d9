package server

import (
	"context"
	"esop/internal/model"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

type Migrate struct {
	db  *gorm.DB
	log *zap.Logger
}

func NewMigrate(db *gorm.DB, log *zap.Logger) *Migrate {
	return &Migrate{
		db:  db,
		log: log,
	}
}
func (m *Migrate) Start(ctx context.Context) error {
	if err := m.db.AutoMigrate(
		&model.User{},
		&model.Admin{},
		&model.Equipment{},
		&model.SourceMaterial{},
		&model.Template{},
		&model.TemplateSm{},
		&model.EquipmentGroup{},
		&model.OperationLog{},
		&model.SourceMaterial{},
		&model.ResourcePack{},
		&model.SendLog{},
		&model.EquipmentReport{},
		&model.EquipmentSM{},
		&model.EquipmentScreenshot{},
		&model.MaterialGroup{},
		&model.EquipmentPowerSchedule{},
	); err != nil {
		m.log.Error("user migrate error", zap.Error(err))
		return err
	}
	m.log.Info("AutoMigrate success")
	return nil
}
func (m *Migrate) Stop(ctx context.Context) error {
	m.log.Info("AutoMigrate stop")
	return nil
}
