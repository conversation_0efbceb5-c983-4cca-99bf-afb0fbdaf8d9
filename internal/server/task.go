package server

import (
	"context"
	"esop/internal/service"
	"fmt"
	"time"

	"github.com/go-co-op/gocron/v2"
	"go.uber.org/zap"
)

type Task struct {
	log       *zap.Logger
	scheduler gocron.Scheduler
	equipment service.EquipmentService
}

func NewTask(log *zap.Logger, equipment service.EquipmentService) *Task {
	return &Task{
		log:       log,
		equipment: equipment,
	}
}
func (t *Task) Start(ctx context.Context) error {

	var (
		err error
	)
	t.scheduler, err = gocron.NewScheduler()
	if err != nil {
		fmt.Println(err, "scheduler error")
	}
	t.log.Info("Task start...")
	_, err = t.scheduler.NewJob(gocron.DurationJob(7*time.Second), gocron.NewTask(func() {
		t.log.Info("Task1")
		if err := t.equipment.CheckEquipmentStatus(); err != nil {
			return
		}
	}))
	t.scheduler.Start() //开启任务
	return nil
}
func (t *Task) Stop(ctx context.Context) error {
	if err := t.scheduler.Shutdown(); err != nil {
		t.log.Info("Task stop...Error")
		return err
	}
	t.log.Info("Task stop...")
	return nil
}
