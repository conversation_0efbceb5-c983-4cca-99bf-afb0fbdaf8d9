package service

import (
	"context"
	v1 "esop/api/v1"
	"esop/internal/model"
	"esop/internal/repository"

	"github.com/gin-gonic/gin"
)

type MaterialGroupService interface {
	GetMaterialGroupList(ctx context.Context, req v1.MaterialGroupListRequest) (int64, []*v1.MaterialGroupData, error)
	GetDetail(ctx context.Context, id int) (v1.MaterialGroupData, error)
	CreateMaterialGroup(ctx *gin.Context, req *v1.SaveMaterialGroupRequest) error
	EditMaterialGroup(ctx *gin.Context, id int, req v1.SaveMaterialGroupRequest) error
	DeleteMaterialGroup(ctx *gin.Context, id int) error
	GetMaterialGroup(id int64) (*model.MaterialGroup, error)
	GetAllGroups(ctx context.Context) ([]*v1.MaterialGroupData, error)
	MoveMaterialsToGroup(ctx *gin.Context, req *v1.MoveMaterialsToGroupRequest) error
}

func NewMaterialGroupService(service *Service, materialGroupRepository repository.MaterialGroupRepository, sourceMaterialRepository repository.SourceMaterialRepository) MaterialGroupService {
	return &materialGroupService{
		Service:                  service,
		materialGroupRepository:  materialGroupRepository,
		sourceMaterialRepository: sourceMaterialRepository,
	}
}

type materialGroupService struct {
	*Service
	materialGroupRepository  repository.MaterialGroupRepository
	sourceMaterialRepository repository.SourceMaterialRepository
}

func (s *materialGroupService) GetMaterialGroupList(ctx context.Context, req v1.MaterialGroupListRequest) (int64, []*v1.MaterialGroupData, error) {
	return s.materialGroupRepository.GetMaterialGroupList(ctx, req)
}

func (s *materialGroupService) GetDetail(ctx context.Context, id int) (v1.MaterialGroupData, error) {
	return s.materialGroupRepository.GetMaterialGroupDetail(ctx, id)
}

func (s *materialGroupService) CreateMaterialGroup(ctx *gin.Context, req *v1.SaveMaterialGroupRequest) error {
	return s.materialGroupRepository.Create(ctx, req)
}

func (s *materialGroupService) EditMaterialGroup(ctx *gin.Context, id int, req v1.SaveMaterialGroupRequest) error {
	return s.materialGroupRepository.Edit(ctx, id, req)
}

func (s *materialGroupService) DeleteMaterialGroup(ctx *gin.Context, id int) error {
	return s.materialGroupRepository.Delete(ctx, id)
}

func (s *materialGroupService) GetMaterialGroup(id int64) (*model.MaterialGroup, error) {
	return s.materialGroupRepository.FirstById(id)
}

func (s *materialGroupService) GetAllGroups(ctx context.Context) ([]*v1.MaterialGroupData, error) {
	return s.materialGroupRepository.GetAllGroups(ctx)
}

func (s *materialGroupService) MoveMaterialsToGroup(ctx *gin.Context, req *v1.MoveMaterialsToGroupRequest) error {
	// 验证分组是否存在（如果不是移动到未分组）
	if req.GroupId > 0 {
		if _, err := s.materialGroupRepository.FirstById(int64(req.GroupId)); err != nil {
			return err
		}
	}

	// 批量更新素材的分组ID
	return s.sourceMaterialRepository.UpdateMaterialsGroup(ctx, req.MaterialIds, req.GroupId)
}