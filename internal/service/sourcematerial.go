package service

import (
	"context"
	v1 "esop/api/v1"
	"esop/internal/model"
	"esop/internal/repository"
	"fmt"
	"io"
	"mime/multipart"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

type SourceMaterialService interface {
	AddSourceMaterial(ctx *gin.Context, v *v1.SaveSourceMaterialRequest) error
	EditSourceMaterialInfo(ctx *gin.Context, id int, req v1.SaveSourceMaterialRequest) error
	GetSourceMaterial(id int64) (*model.SourceMaterial, error)
	GetSourceMaterialList(ctx context.Context, req v1.SourceMaterialListRequest) (int64, []*v1.SourceMaterialData, error)
	GetDetail(ctx context.Context, id int) (v1.SourceMaterialData, error)
	DeleteSourceMaterial(ctx *gin.Context, ids ...int) error
	BatchUpload(ctx *gin.Context, files []*multipart.FileHeader, groupId int, sourceWidths []string, sourceHeights []string,sizes []string) (int, int, []string, error)
	BatchUploadNew(ctx *gin.Context, files []*multipart.FileHeader, sourceWidths []string, sourceHeights []string, sizes []string) (int, int, []string, error)
	BatchUploadNewWithGroup(ctx *gin.Context, files []*multipart.FileHeader, sourceWidths []string, sourceHeights []string, sizes []string, groupId int) (int, int, []string, error)
	UpdateMaterialsGroup(ctx *gin.Context, materialIds []int, groupId int) error
	UploadToEquipment(ctx *gin.Context, files []*multipart.FileHeader, equipmentId int) (int, int, error)
}

func NewSourceMaterialService(service *Service, sourceMaterialRepository repository.SourceMaterialRepository, equipmentRepository repository.EquipmentRepository) SourceMaterialService {
	return &sourceMaterialService{
		Service:                  service,
		sourceMaterialRepository: sourceMaterialRepository,
		equipmentRepository:      equipmentRepository,
	}
}

type sourceMaterialService struct {
	*Service
	sourceMaterialRepository repository.SourceMaterialRepository
	equipmentRepository      repository.EquipmentRepository
}

func (s *sourceMaterialService) GetSourceMaterial(id int64) (*model.SourceMaterial, error) {
	return s.sourceMaterialRepository.FirstById(id)
}

func (s *sourceMaterialService) GetSourceMaterialList(ctx context.Context, req v1.SourceMaterialListRequest) (int64, []*v1.SourceMaterialData, error) {
	total, info, err := s.sourceMaterialRepository.GetSourceMaterialList(ctx, req)
	if err != nil {
		return 0, nil, err
	}
	return total, info, nil
}

func (s *sourceMaterialService) GetDetail(ctx context.Context, id int) (v1.SourceMaterialData, error) {
	return s.sourceMaterialRepository.GetSourceMaterialDetail(ctx, id)
}

func (s *sourceMaterialService) DeleteSourceMaterial(ctx *gin.Context, ids ...int) error {
	materials, err := s.sourceMaterialRepository.Delete(ctx, ids...)
	if err != nil {
		return err
	}

	for _, material := range materials {
		if material.Path != "" {
			// Assuming "assets/media" is the base directory for material files.
			// You might need to adjust this path based on your project structure.
			filePath := filepath.Join("assets", "media", material.Path)
			if err := os.Remove(filePath); err != nil {
				// Log the error but continue trying to delete other files.
				// You might want to use a more sophisticated logger.
				fmt.Printf("failed to delete material file: %s, error: %v\n", filePath, err)
			}
		}
	}

	return nil
}

func (s *sourceMaterialService) AddSourceMaterial(ctx *gin.Context, v *v1.SaveSourceMaterialRequest) error {
	return s.sourceMaterialRepository.Create(ctx, v)
}

func (s *sourceMaterialService) EditSourceMaterialInfo(ctx *gin.Context, id int, req v1.SaveSourceMaterialRequest) error {
	return s.sourceMaterialRepository.Edit(ctx, id, req)
}

func (s *sourceMaterialService) BatchUpload(ctx *gin.Context, files []*multipart.FileHeader, groupId int, sourceWidths []string, sourceHeights []string,sizes []string) (int, int, []string, error) {
	var successCount, failureCount int
	var failedFiles []string
	var materialsToCreate []*model.SourceMaterial

	if len(files) != len(sourceWidths) || len(files) != len(sourceHeights) {
		return 0, len(files), filesToStrings(files), fmt.Errorf("mismatched file and dimension counts")
	}

	for i, file := range files {
		groupName := filepath.Dir(file.Filename)
		if groupName == "." {
			groupName = ""
		}

		fileNameWithExt := filepath.Base(file.Filename)
		fileExt := filepath.Ext(fileNameWithExt)
		fileNameWithoutExt := strings.TrimSuffix(fileNameWithExt, fileExt)

		parts := strings.SplitN(fileNameWithoutExt, "-", 2)
		if len(parts) < 2 {
			failureCount++
			failedFiles = append(failedFiles, file.Filename)
			continue
		}

		aliasName := parts[0]
		originalFileName := parts[1]
		fullOriginalFileName := originalFileName + fileExt


		equipment, err := s.equipmentRepository.GetEquipmentByAliasNameAndGroupId(ctx, aliasName, groupId)
		if err != nil || equipment == nil {
			failureCount++
			failedFiles = append(failedFiles, file.Filename)
			continue
		}

		// Physical path for saving the file
		dir, _ := os.Getwd()
		mediaDir := filepath.Join(dir, "assets/media", groupName)
		if _, err := os.Stat(mediaDir); os.IsNotExist(err) {
			os.MkdirAll(mediaDir, 0755)
		}
		physicalFilePath := filepath.Join(mediaDir, fileNameWithExt)

		// Relative path for database
		relativeFilePath := filepath.ToSlash(filepath.Join("", groupName, fileNameWithExt))


		src, err := file.Open()
		if err != nil {
			failureCount++
			failedFiles = append(failedFiles, file.Filename)
			continue
		}
		defer src.Close()

		dst, err := os.Create(physicalFilePath)
		if err != nil {
			failureCount++
			failedFiles = append(failedFiles, file.Filename)
			continue
		}
		defer dst.Close()

		if _, err := io.Copy(dst, src); err != nil {
			failureCount++
			failedFiles = append(failedFiles, file.Filename)
			continue
		}

		width, _ := strconv.Atoi(sourceWidths[i])
		height, _ := strconv.Atoi(sourceHeights[i])
size, _ := strconv.ParseInt(sizes[i], 10, 64)
		fileType := 1 // Default to image
		contentType := file.Header.Get("Content-Type")
		if strings.HasPrefix(contentType, "video") {
			fileType = 2
		}

		now := time.Now().Unix()
		material := &model.SourceMaterial{
			Name:         fullOriginalFileName,
			Type:         fileType,
			Path:         relativeFilePath,
			SourceWidth:  width,
			SourceHeight: height,
			GroupName:    groupName,
			Size:         size,
			AliasName:    aliasName,
			ContentType:  contentType,
			IsDeleted:    1,
			CreatedAt:    now,
			UpdatedAt:    now,
			EquipmentId:  equipment.ID,
		}
		materialsToCreate = append(materialsToCreate, material)
		successCount++
	}

	if len(materialsToCreate) > 0 {
		if err := s.sourceMaterialRepository.BatchCreate(ctx, materialsToCreate); err != nil {
			return 0, len(files), filesToStrings(files), fmt.Errorf("failed to create materials in db: %w", err)
		}
	}

	return successCount, failureCount, failedFiles, nil
}

func filesToStrings(files []*multipart.FileHeader) []string {
	var fileNames []string
	for _, file := range files {
		fileNames = append(fileNames, file.Filename)
	}
	return fileNames
}

// BatchUploadNew 新的批量上传方法，支持自动识别素材名称和类型
func (s *sourceMaterialService) BatchUploadNew(ctx *gin.Context, files []*multipart.FileHeader, sourceWidths []string, sourceHeights []string, sizes []string) (int, int, []string, error) {
	var successCount, failureCount int
	var failedFiles []string
	var materialsToCreate []*model.SourceMaterial

	if len(files) != len(sourceWidths) || len(files) != len(sourceHeights) || len(files) != len(sizes) {
		return 0, len(files), filesToStrings(files), fmt.Errorf("mismatched file and dimension counts")
	}

	for i, file := range files {
		// 自动识别文件名（去除扩展名）
		originalFileName := file.Filename
		fileNameWithoutExt := strings.TrimSuffix(originalFileName, filepath.Ext(originalFileName))

		// 确定当前目录
		dir, err := os.Getwd()
		if err != nil {
			failureCount++
			failedFiles = append(failedFiles, file.Filename)
			continue
		}

		// 确定 media 目录路径
		mediaDir := filepath.Join(dir, "assets/media")
		if _, err := os.Stat(mediaDir); os.IsNotExist(err) {
			err = os.MkdirAll(mediaDir, 0755)
			if err != nil {
				failureCount++
				failedFiles = append(failedFiles, file.Filename)
				continue
			}
		}

		// 生成唯一文件名
		timestamp := strconv.FormatInt(time.Now().UnixNano(), 10)
		newFileName := timestamp + "_" + originalFileName
		physicalFilePath := filepath.Join(mediaDir, newFileName)

		// 保存文件
		src, err := file.Open()
		if err != nil {
			failureCount++
			failedFiles = append(failedFiles, file.Filename)
			continue
		}
		defer src.Close()

		dst, err := os.Create(physicalFilePath)
		if err != nil {
			failureCount++
			failedFiles = append(failedFiles, file.Filename)
			continue
		}
		defer dst.Close()

		if _, err := io.Copy(dst, src); err != nil {
			failureCount++
			failedFiles = append(failedFiles, file.Filename)
			continue
		}

		// 自动检测文件类型
		contentType := file.Header.Get("Content-Type")
		fileType := detectFileTypeByContentType(contentType)

		// 获取尺寸
		width, _ := strconv.Atoi(sourceWidths[i])
		height, _ := strconv.Atoi(sourceHeights[i])

		now := time.Now().Unix()
		size, _ := strconv.ParseInt(sizes[i], 10, 64)

		material := &model.SourceMaterial{
			Name:         fileNameWithoutExt, // 使用自动识别的名称（去除扩展名）
			Type:         fileType,           // 自动识别的类型
			Path:         newFileName,        // 保存到数据库的文件名
			SourceWidth:  width,
			SourceHeight: height,
			Size:         size,
			GroupName:    "",                 // 新接口不使用分组
			AliasName:    "",                 // 新接口不使用别名
			ContentType:  contentType,
			IsDeleted:    1,
			CreatedAt:    now,
			UpdatedAt:    now,
			EquipmentId:  0, // 新接口不关联设备
		}

		materialsToCreate = append(materialsToCreate, material)
		successCount++
	}

	// 批量创建数据库记录
	if len(materialsToCreate) > 0 {
		if err := s.sourceMaterialRepository.BatchCreate(ctx, materialsToCreate); err != nil {
			return 0, len(files), filesToStrings(files), fmt.Errorf("failed to create materials in db: %w", err)
		}
	}

	return successCount, failureCount, failedFiles, nil
}

// BatchUploadNewWithGroup 新的批量上传方法，支持指定分组
func (s *sourceMaterialService) BatchUploadNewWithGroup(ctx *gin.Context, files []*multipart.FileHeader, sourceWidths []string, sourceHeights []string, sizes []string, groupId int) (int, int, []string, error) {
	var successCount, failureCount int
	var failedFiles []string
	var materialsToCreate []*model.SourceMaterial

	if len(files) != len(sourceWidths) || len(files) != len(sourceHeights) || len(files) != len(sizes) {
		return 0, len(files), filesToStrings(files), fmt.Errorf("mismatched file and dimension counts")
	}

	for i, file := range files {
		// 自动识别文件名（去除扩展名）
		originalFileName := file.Filename
		fileNameWithoutExt := strings.TrimSuffix(originalFileName, filepath.Ext(originalFileName))

		// 确定当前目录
		dir, err := os.Getwd()
		if err != nil {
			failureCount++
			failedFiles = append(failedFiles, file.Filename)
			continue
		}

		// 确定 media 目录路径
		mediaDir := filepath.Join(dir, "assets/media")
		if _, err := os.Stat(mediaDir); os.IsNotExist(err) {
			err = os.MkdirAll(mediaDir, 0755)
			if err != nil {
				failureCount++
				failedFiles = append(failedFiles, file.Filename)
				continue
			}
		}

		// 生成唯一文件名
		timestamp := strconv.FormatInt(time.Now().UnixNano(), 10)
		newFileName := timestamp + "_" + originalFileName
		physicalFilePath := filepath.Join(mediaDir, newFileName)

		// 保存文件
		src, err := file.Open()
		if err != nil {
			failureCount++
			failedFiles = append(failedFiles, file.Filename)
			continue
		}
		defer src.Close()

		dst, err := os.Create(physicalFilePath)
		if err != nil {
			failureCount++
			failedFiles = append(failedFiles, file.Filename)
			continue
		}
		defer dst.Close()

		if _, err := io.Copy(dst, src); err != nil {
			failureCount++
			failedFiles = append(failedFiles, file.Filename)
			continue
		}

		// 自动检测文件类型
		contentType := file.Header.Get("Content-Type")
		fileType := detectFileTypeByContentType(contentType)

		// 获取尺寸
		width, _ := strconv.Atoi(sourceWidths[i])
		height, _ := strconv.Atoi(sourceHeights[i])

		now := time.Now().Unix()
		size, _ := strconv.ParseInt(sizes[i], 10, 64)

		material := &model.SourceMaterial{
			Name:         fileNameWithoutExt, // 使用自动识别的名称（去除扩展名）
			Type:         fileType,           // 自动识别的类型
			Path:         newFileName,        // 保存到数据库的文件名
			SourceWidth:  width,
			SourceHeight: height,
			Size:         size,
			GroupName:    "",                 // 新接口不使用GroupName字段
			AliasName:    "",                 // 新接口不使用别名
			ContentType:  contentType,
			IsDeleted:    1,
			CreatedAt:    now,
			UpdatedAt:    now,
			EquipmentId:  0,      // 新接口不关联设备
			GroupId:      groupId, // 指定分组ID
		}

		materialsToCreate = append(materialsToCreate, material)
		successCount++
	}

	// 批量创建数据库记录
	if len(materialsToCreate) > 0 {
		if err := s.sourceMaterialRepository.BatchCreate(ctx, materialsToCreate); err != nil {
			return 0, len(files), filesToStrings(files), fmt.Errorf("failed to create materials in db: %w", err)
		}
	}

	return successCount, failureCount, failedFiles, nil
}

// UpdateMaterialsGroup 批量更新素材分组
func (s *sourceMaterialService) UpdateMaterialsGroup(ctx *gin.Context, materialIds []int, groupId int) error {
	return s.sourceMaterialRepository.UpdateMaterialsGroup(ctx, materialIds, groupId)
}

func detectFileTypeByContentType(contentType string) int {
	switch {
	case strings.HasPrefix(contentType, "image/"):
		return 1 // 图片
	case strings.HasPrefix(contentType, "video/"):
		return 2 // 视频
	case contentType == "application/pdf":
		return 3 // PDF
	case strings.Contains(contentType, "powerpoint"), strings.Contains(contentType, "presentationml"):
		return 4 // PPT
	case strings.Contains(contentType, "msword"), strings.Contains(contentType, "wordprocessingml"):
		return 5 // Word
	case strings.Contains(contentType, "ms-excel"), strings.Contains(contentType, "spreadsheetml"):
		return 6 // Excel
	case contentType == "text/plain":
		return 7 // TXT
	default:
		return 8 // 其他
	}
}

func (s *sourceMaterialService) UploadToEquipment(ctx *gin.Context, files []*multipart.FileHeader, equipmentId int) (int, int, error) {
	// 确定 media 目录路径
	dir, err := os.Getwd()
	if err != nil {
		return 0, 0, err
	}
	mediaDir := filepath.Join(dir, "assets/media")
	if _, err := os.Stat(mediaDir); os.IsNotExist(err) {
		if err := os.MkdirAll(mediaDir, 0755); err != nil {
			return 0, 0, err
		}
	}

	var successCount, failureCount int
	for _, header := range files {
		if err := s.processEquipmentFile(ctx, header, mediaDir, equipmentId); err == nil {
			successCount++
		} else {
			// 可以在这里记录详细的错误日志
			failureCount++
		}
	}

	return successCount, failureCount, nil
}

// processEquipmentFile 处理设备上传的单个文件
func (s *sourceMaterialService) processEquipmentFile(ctx *gin.Context, header *multipart.FileHeader, mediaDir string, equipmentId int) error {
	// 打开上传的文件
	file, err := header.Open()
	if err != nil {
		return err
	}
	defer file.Close()

	// 创建文件
	name := strconv.FormatInt(time.Now().UnixNano(), 10) + "_" + header.Filename
	outPath := filepath.Join(mediaDir, name)
	out, err := os.Create(outPath)
	if err != nil {
		return err
	}
	defer out.Close()

	// 将上传的文件内容复制到新创建的文件中
	_, err = io.Copy(out, file)
	if err != nil {
		os.Remove(outPath)
		return err
	}

	// 准备数据写入数据库
	contentType := header.Header.Get("Content-Type")
	fileType := detectFileTypeByContentType(contentType)

	req := &v1.SaveSourceMaterialRequest{
		Name:         header.Filename,
		Type:         fileType,
		Path:         name, // 保存到数据库的相对路径
		SourceWidth:  0,    // 前端未提供
		SourceHeight: 0,    // 前端未提供
		ContentType:  contentType,
		Size:         header.Size,
		EquipmentId:  equipmentId,
	}

	// 写入数据库
	if err := s.AddSourceMaterial(ctx, req); err != nil {
		// 如果数据库写入失败，删除已上传的文件
		os.Remove(outPath)
		return err
	}

	return nil
}


