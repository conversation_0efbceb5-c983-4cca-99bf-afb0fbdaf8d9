package service

import (
	"esop/internal/model"
	"esop/internal/repository"
	// "context"
	v1 "esop/api/v1"
	"github.com/gin-gonic/gin"
	// "golang.org/x/crypto/bcrypt"
	// "time"
)

type OperationLogService interface {
	GetOperationLog(id int64) (*model.OperationLog, error)
	GetList(ctx *gin.Context, req v1.OperationLogRequest) (int64, []*v1.OperationLogData, error)
}

func NewOperationLogService(service *Service, operationLogRepository repository.OperationLogRepository) OperationLogService {
	return &operationLogService{
		Service:        service,
		operationLogRepository: operationLogRepository,
	}
}

type operationLogService struct {
	*Service
	operationLogRepository repository.OperationLogRepository
}

func (s *operationLogService) GetOperationLog(id int64) (*model.OperationLog, error) {
	return s.operationLogRepository.FirstById(id)
}

func (s *operationLogService) GetList(ctx *gin.Context, req v1.OperationLogRequest) (int64, []*v1.OperationLogData, error) {

	total, info, err := s.operationLogRepository.GetOperationLogList(ctx, req)
	if err != nil {
		return 0, nil, err
	}

	return total, info, nil
}
