package service

import (
	"context"
	v1 "esop/api/v1"
	"esop/internal/model"
	"esop/internal/repository"
	"time"

	"github.com/gin-gonic/gin"
)

type AdminService interface {
	AddAdmin(ctx *gin.Context, v *v1.SaveAdminListRequest) error
	GetAdmin(id int64) (*model.Admin, error)
	GetList(ctx *gin.Context, req v1.AdminListRequest) (int64, []*v1.AdminData, error)
	DeleteAdmin(ctx *gin.Context, id int) error
	EditUsing(ctx context.Context, req v1.UpdateAdminUsingRequest) error
	EditAdmin(ctx *gin.Context,id int ,req v1.SaveAdminListRequest) error
	Login(ctx context.Context, req *v1.LoginRequest) (v1.AdminToken, error)
}

func NewAdminService(service *Service, adminRepository repository.AdminRepository) AdminService {
	return &adminService{
		Service:        service,
		adminRepository: adminRepository,
	}
}

type adminService struct {
	*Service
	adminRepository repository.AdminRepository
}

func (s *adminService) GetAdmin(id int64) (*model.Admin, error) {
	return s.adminRepository.FirstById(id)
}

func (s *adminService) GetList(ctx *gin.Context, req v1.AdminListRequest) (int64, []*v1.AdminData, error) {

	total, info, err := s.adminRepository.GetAdminList(ctx, req)
	if err != nil {
		return 0, nil, err
	}

	return total, info, nil
}
func (s *adminService) DeleteAdmin(ctx *gin.Context, id int) error {
	return s.adminRepository.Delete(ctx, id)
}

func (s *adminService) EditUsing(ctx context.Context, req v1.UpdateAdminUsingRequest) error {
	return s.adminRepository.EditUsing(ctx, req)
}

func (s *adminService) AddAdmin(ctx *gin.Context, req *v1.SaveAdminListRequest) error {
	//查看账号是否存在
	admin,err := s.adminRepository.GetByAccount(ctx,req.Account)
	if err != nil || admin != nil {
		return v1.ErrUpdateError
	}
	return s.adminRepository.Create(ctx, req)
}
func (s *adminService) EditAdmin(ctx *gin.Context, id int ,req v1.SaveAdminListRequest) error {
	//查看账号是否存在
	admin,err := s.adminRepository.GetByNameAndId(ctx,id,req.Name)
	if err != nil || admin != nil {
		return v1.ErrUpdateError
	}
	return s.adminRepository.Edit(ctx,id, req)
}
func (s *adminService) Login(ctx context.Context, req *v1.LoginRequest) (v1.AdminToken, error) {

	admin, err := s.adminRepository.GetByAccount(ctx, req.Account)
	// fmt.Println(admin, "admin")
	if err != nil || admin == nil {
		return v1.AdminToken{}, v1.ErrAccountNotFound
	}

	// err = bcrypt.CompareHashAndPassword([]byte(admin.Password), []byte(req.Password))
	// if err != nil {
	// 	return v1.AdminToken{}, v1.ErrPasswordError
	// }
	token, err := s.jwt.GenToken(admin.Name,admin.Account, time.Now().Add(time.Hour*24*30))
	if err != nil {
		return v1.AdminToken{}, err
	}
	adminToken := v1.AdminToken{
        Admin: admin,
        Token: token,
    }
    // adminTokens = append(adminTokens, adminToken)


	return adminToken, nil
}


