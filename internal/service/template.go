package service

import (
	"context"
	"encoding/json"

	v1 "esop/api/v1"
	"esop/internal/model"
	"esop/internal/repository"

	"github.com/gin-gonic/gin"
)

type TemplateService interface {
	AddTemplate(ctx *gin.Context, v *v1.SaveTemplateListRequest) error
	GetTemplate(id int64) (*model.Template, error)
	GetTemplateList(ctx context.Context, req v1.TemplateListRequest) (int64, []*v1.TemplateData, error)
	DeleteTemplate(ctx context.Context, id int) error
	GetDetail(ctx context.Context, id int) (v1.TemplateData, error)
	EditTemplateInfo(ctx context.Context, id int, req v1.SaveTemplateListRequest) error
	AddPack(ctx *gin.Context, v *v1.SavePackRequest) error
}

func NewTemplateService(service *Service, templateRepository repository.TemplateRepository) TemplateService {
	return &templateService{
		Service:            service,
		templateRepository: templateRepository,
	}
}

type templateService struct {
	*Service
	templateRepository repository.TemplateRepository
}

func (s *templateService) GetTemplate(id int64) (*model.Template, error) {
	return s.templateRepository.FirstById(id)
}

func (s *templateService) GetTemplateList(ctx context.Context, req v1.TemplateListRequest) (int64, []*v1.TemplateData, error) {
	total, info, err := s.templateRepository.GetTemplateList(ctx, req)
	if err != nil {
		return 0, nil, err
	}

	return total, info, nil
}

func (s *templateService) DeleteTemplate(ctx context.Context, id int) error {
	return s.templateRepository.Delete(ctx, id)
}

func (s *templateService) AddTemplate(ctx *gin.Context, v *v1.SaveTemplateListRequest) error {
	return s.templateRepository.Create(ctx, v)
}

func (s *templateService) GetDetail(ctx context.Context, id int) (v1.TemplateData, error) {
	templateData, err := s.templateRepository.GetTemplateDetail(ctx, id)
	if err != nil {
		return templateData, err
	}
	for i := range templateData.TemplateSmDetail {
		var multiFiles []v1.MultiFiles
		if templateData.TemplateSmDetail[i].MultiFilesStr != "" {
			if err := json.Unmarshal([]byte(templateData.TemplateSmDetail[i].MultiFilesStr), &multiFiles); err != nil {
				// Handle error, maybe log it or set a default value
				multiFiles = []v1.MultiFiles{}
			}
		} else {
			multiFiles = []v1.MultiFiles{}
		}
		templateData.TemplateSmDetail[i].MultiFiles = multiFiles
	}
	return templateData, nil
}

func (s *templateService) EditTemplateInfo(ctx context.Context, id int, req v1.SaveTemplateListRequest) error {
	return s.templateRepository.Edit(ctx, id, req)
}

func (s *templateService) AddPack(ctx *gin.Context, v *v1.SavePackRequest) error {
	return s.templateRepository.AddPack(ctx, v)
}
