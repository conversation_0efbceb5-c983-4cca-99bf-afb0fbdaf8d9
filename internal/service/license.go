package service

import (
	"context"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"errors"
	v1 "esop/api/v1"
	"esop/internal/model"
	"esop/internal/repository"
	"esop/pkg/helper/decode"
	"fmt"
	"os"
	"strconv"
	"strings"
	"time"

	"sort"

	"github.com/shirou/gopsutil/v3/cpu"
	"github.com/shirou/gopsutil/v3/host"
	"go.uber.org/zap"
)

type LicenseService interface {
	ExportServerMachineCode(ctx context.Context) ([]byte, string, error)
	ImportServerLicense(ctx context.Context, fileBytes []byte, isDat bool) error
	GenerateServerLicense(ctx context.Context, machineCode string, expireAt int64) ([]byte, string, error)
	GetServerStatus(ctx context.Context) (*v1.ServerLicenseStatus, error)
	GetTerminalStatus(ctx context.Context, req v1.TerminalStatusListRequest) (int64, []v1.TerminalStatusItem, error)
	ExportTerminalMachineCodes(ctx context.Context, onlyUnregistered bool) ([]byte, string, error)
	ImportTerminalRegistrationCodes(ctx context.Context, fileBytes []byte) error
}

type licenseService struct {
	*Service
	licenseRepository repository.LicenseRepository
	equipmentRepository repository.EquipmentRepository
}

func NewLicenseService(s *Service, lr repository.LicenseRepository, er repository.EquipmentRepository) LicenseService {
	return &licenseService{Service: s, licenseRepository: lr, equipmentRepository: er}
}

func (s *licenseService) GenerateServerLicense(ctx context.Context, machineCode string, expireAt int64) ([]byte, string, error) {
	if machineCode == "" {
		// default to current server machine code
		mc, _ := s.buildServerMachineCode(ctx)
		machineCode = mc
	}
	lf := serverLicenseFile{ MachineCode: machineCode, ExpireAt: expireAt }
	b, _ := json.MarshalIndent(lf, "", "  ")
	name := fmt.Sprintf("server_license_%s_%d.json", machineCode, time.Now().Unix())
	return b, name, nil
}

// buildServerMachineCode generates a unique machine code from various hardware identifiers.
// buildServerMachineCode generates a unique and stable machine code for both Windows and Linux.
// It relies on the most permanent hardware identifiers to ensure the code does not change
// unless the physical hardware is replaced.
func (s *licenseService) buildServerMachineCode(ctx context.Context) (string, error) {
	var parts []string

	// 1. Host ID (Cross-platform stable identifier)
	// On Windows, this corresponds to the MachineGuid in the registry.
	// On Linux, this corresponds to /etc/machine-id, which is tied to the installation but very stable.
	// This is the primary and most stable cross-platform identifier.
	if hostInfo, err := host.InfoWithContext(ctx); err == nil {
		if hostInfo.HostID != "" {
			parts = append(parts, hostInfo.HostID)
			s.logger.Debug("Added HostID to machine code", zap.String("HostID", hostInfo.HostID))
		}
	} else {
		s.logger.Warn("Could not get host info for HostID", zap.Error(err))
	}

	// 2. CPU Information (Stable across all platforms)
	// We use VendorID, Model, and Core count as a composite, stable identifier for the CPU.
	if cpuInfos, err := cpu.InfoWithContext(ctx); err == nil && len(cpuInfos) > 0 {
		cpuInfo := cpuInfos[0]
		if cpuInfo.VendorID != "" {
			parts = append(parts, cpuInfo.VendorID)
		}
		parts = append(parts, cpuInfo.ModelName, fmt.Sprintf("%d", cpuInfo.Cores))
		s.logger.Debug("Added CPU Info to machine code",
			zap.String("VendorID", cpuInfo.VendorID),
			zap.String("Model", cpuInfo.ModelName),
			zap.Int32("Cores", cpuInfo.Cores))
	} else {
		s.logger.Warn("Could not get CPU info", zap.Error(err))
	}

	// Critical check: If no hardware identifiers could be gathered, we cannot generate a stable code.
	if len(parts) == 0 {
		err := errors.New("failed to gather any stable hardware identifiers (HostID, CPU Info)")
		s.logger.Error("CRITICAL: Cannot generate stable machine code.", zap.Error(err))
		// Fallback to hostname to prevent total failure, but this is not a stable solution.
		hostname, hostErr := os.Hostname()
		if hostErr != nil {
			return "", fmt.Errorf("all stable identifiers failed, and fallback to hostname also failed: %w", hostErr)
		}
		parts = append(parts, hostname)
	}

	// Sort the collected parts to ensure the final string is always in the same order.
	sort.Strings(parts)

	// Combine all parts into a single, consistent string.
	combinedInfo := strings.Join(parts, "|")
	s.logger.Info("Generating machine code from final combined hardware info", zap.String("combined", combinedInfo))

	// Hash the combined string with a salt to generate the final machine code.
	sum := sha256.Sum256([]byte("esop-server-stable-v2-" + combinedInfo))

	// Use the full hash for maximum uniqueness.
	return strings.ToUpper(hex.EncodeToString(sum[:])), nil
}

func (s *licenseService) ExportServerMachineCode(ctx context.Context) ([]byte, string, error) {
	code, _ := s.buildServerMachineCode(ctx)
	data := map[string]string{"machine_code": code}
	b, _ := json.MarshalIndent(data, "", "  ")
	return b, fmt.Sprintf("server_machine_code_%d.json", time.Now().Unix()), nil
}

type serverLicenseFile struct {
	MachineCode  string `json:"machine_code"`
	ExpireAt     int64  `json:"expire_at"`
}

func (s *licenseService) ImportServerLicense(ctx context.Context, fileBytes []byte, isDat bool) error {
	if isDat {

		data,isOk,err:=decode.VerifyServerLicense(string(fileBytes))

if err!=nil{
	return err
}

if !isOk{
	return errors.New("invalid license")
}
		expireAt,err:=strconv.Atoi(data[1])
		fmt.Println(expireAt,"expireAtexpireAtexpireAtexpireAt")
		m := &model.ServerLicense{
			MachineCode: data[0], // Placeholder
			LicenseData: string(fileBytes),
			ExpireAt:  int64(expireAt)  , // Or parse from file if possible
			UpdatedAt:   time.Now().Unix(),
			CreatedAt:   time.Now().Unix(),
		}
		return s.licenseRepository.UpsertServerLicense(ctx, m)
	}




	return nil
	var lic serverLicenseFile
	if err := json.Unmarshal(fileBytes, &lic); err != nil {
		return errors.New("无效的许可证文件")
	}
	code, _ := s.buildServerMachineCode(ctx)
	if lic.MachineCode == "" || lic.MachineCode != code {
		return errors.New("许可证与当前服务器不匹配")
	}
	m := &model.ServerLicense{
		MachineCode: lic.MachineCode,
		LicenseData: string(fileBytes),
		ExpireAt:    lic.ExpireAt,
		UpdatedAt:   time.Now().Unix(),
		CreatedAt:   time.Now().Unix(),
	}
	return s.licenseRepository.UpsertServerLicense(ctx, m)
}

func (s *licenseService) GetServerStatus(ctx context.Context) (*v1.ServerLicenseStatus, error) {
	lic, err := s.licenseRepository.GetServerLicense(ctx)
	if err != nil { return nil, err }
	if lic == nil { return &v1.ServerLicenseStatus{Valid:false}, nil }
	valid := lic.ExpireAt > time.Now().Unix()

	if lic.ExpireAt == -999999 {
		return &v1.ServerLicenseStatus{Valid: true, ExpireAt: 0}, nil
	}
	if valid {
		return &v1.ServerLicenseStatus{Valid: true, ExpireAt: lic.ExpireAt}, nil
	}
	return &v1.ServerLicenseStatus{Valid: false, ExpireAt: lic.ExpireAt}, nil
}

func (s *licenseService) GetTerminalStatus(ctx context.Context, req v1.TerminalStatusListRequest) (int64, []v1.TerminalStatusItem, error) {
	total, rows, err := s.licenseRepository.GetTerminalStatus(ctx, req.Keyword, req.Page, req.PageSize)
	if err != nil { return 0, nil, err }
	items := make([]v1.TerminalStatusItem, 0, len(rows))
	for _, r := range rows {
		items = append(items, v1.TerminalStatusItem{
			AliasName: toString(r["alias_name"]),
			MacAddress: toString(r["mac_address"]),
			Registered: toString(r["registration_code"]) != "",
			RegistrationCode: toString(r["registration_code"]),
			CreatedAt: toInt64(r["created_at"]),
		})
	}
	return total, items, nil
}

func (s *licenseService) ExportTerminalMachineCodes(ctx context.Context, onlyUnregistered bool) ([]byte, string, error) {
	rows, err := s.licenseRepository.FetchTerminalCodes(ctx, onlyUnregistered)
	if err != nil {
		return nil, "", err
	}
	b, _ := json.MarshalIndent(rows, "", "  ")
	name := fmt.Sprintf("terminal_machine_codes_%s_%d.json", ternary(onlyUnregistered, "unregistered", "all"), time.Now().Unix())
	return b, name, nil
}

func (s *licenseService) ImportTerminalRegistrationCodes(ctx context.Context, fileBytes []byte) error {
	// parse csv/json lines for registration mapping: support headers

	data,err:=decode.VerifyAndParseTerminalLicense(string(fileBytes))

		if err!=nil{
			return err
		}

	return s.licenseRepository.UpdateTerminalRegistrationCodes(ctx, data)
}

func toString(v interface{}) string {
	if v == nil { return "" }
	switch t := v.(type) {
	case []byte:
		return string(t)
	default:
		return fmt.Sprintf("%v", v)
	}
}
func toInt64(v interface{}) int64 { switch t := v.(type) { case int64: return t; case int: return int64(t); case float64: return int64(t); default: return 0 } }
func ternary(b bool, x, y string) string { if b { return x }; return y }
