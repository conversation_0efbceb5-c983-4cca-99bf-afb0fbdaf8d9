package mqtt

import (
	"context"
	"log"

	mqtt "github.com/mochi-mqtt/server/v2"
)

// MqttServerAdapter is an adapter to make the mochi-mqtt server compatible with the app.Server interface.
type MqttServerAdapter struct {
	*mqtt.Server
}

// NewMqttServerAdapter creates a new adapter.
func NewMqttServerAdapter(s *mqtt.Server) *MqttServerAdapter {
	return &MqttServerAdapter{Server: s}
}

// Start starts the MQTT server. This is a non-blocking call.
func (a *MqttServerAdapter) Start(ctx context.Context) error {
	go func() {
		log.Println("Starting MQTT server...")
		if err := a.Serve(); err != nil {
			log.Printf("MQTT server error: %v", err)
		} else {
			log.Println("MQTT server stopped")
		}
	}()
	return nil
}

// Stop stops the MQTT server.
func (a *MqttServerAdapter) Stop(ctx context.Context) error {
	log.Println("Stopping MQTT server...")
	return a.Close()
}