package mqtt

import (
	"fmt"
	"sync"
	"time"

	mqtt "github.com/mochi-mqtt/server/v2"
	"github.com/mochi-mqtt/server/v2/packets"
	"github.com/spf13/viper"
	"gorm.io/gorm"

	sqlcipher "github.com/gdanko/gorm-sqlcipher"
	"gorm.io/driver/mysql"
)

type licenseAuthHook struct {
	mqtt.HookBase
	conf  *viper.Viper
	db    *gorm.DB
	once  sync.Once
	dbErr error
}

func newLicenseAuthHook(conf *viper.Viper) *licenseAuthHook {
	return &licenseAuthHook{conf: conf}
}

func (h *licenseAuthHook) ID() string { return "license-auth" }

func (h *licenseAuthHook) Provides(b byte) bool {
	return b == mqtt.OnConnectAuthenticate ||
		b == mqtt.OnACLCheck ||
		b == mqtt.OnDisconnect
}

func (h *licenseAuthHook) initDB() {
	// Minimal DB open using config; mirrors repository.NewDb but simplified (no logger)
	driver := h.conf.GetString("data.db.driver")
	dsn := h.conf.GetString("data.db.dsn")
	switch driver {
	case "mysql":
		h.db, h.dbErr = gorm.Open(mysql.Open(dsn), &gorm.Config{})
	case "sqlite":
		// dsn may include fmt placeholders for key/page size
		key := "MufongEsop666"
		pageSize := 4096
		dbname := fmt.Sprintf(dsn, key, pageSize)
		h.db, h.dbErr = gorm.Open(sqlcipher.Open(dbname), &gorm.Config{})
	default:
		h.dbErr = fmt.Errorf("unsupported db driver: %s", driver)
	}
}

func (h *licenseAuthHook) OnConnectAuthenticate(cl *mqtt.Client, pk packets.Packet) bool {
	username := string(pk.Connect.Username)
	password := string(pk.Connect.Password)

	// optional: strict per-device check. Expect client.Username: MAC, client.Password: registration_code
	if username == "" || password == "" {
		return false
	}

	// Hardcoded credentials for admin access
	if username == "admin" && password == "MufongEsop666" {
		return true
	}
	if username == "fsmufong" && password == "fsmufong" {
		return true
	}
	fmt.Println("OnConnectAuthenticate",username,password)
	h.once.Do(h.initDB)
	if h.dbErr != nil || h.db == nil {
		return false
	}
	// read server license (assume single row)
	type serverLicense struct {

		ExpireAt     int64
	}
	var lic serverLicense
	res := h.db.Table("server_license").Limit(1).Select(" expire_at").Scan(&lic)
	if res.Error != nil || res.RowsAffected == 0 {
		return false
	}
	if lic.ExpireAt != -999999 && lic.ExpireAt <= time.Now().Unix() {
		return false
	}



	// Authenticate based on credentials only. The is_deleted flag is used inconsistently
	// throughout the project (0, 1, and 2 seem to have different meanings in different places),
	// making it an unreliable check for authentication. A device with valid credentials
	// should be allowed to connect. Its operational status should be handled by business logic,
	// not at the connection level.
	if err := h.db.Table("equipment").Where("mac_address = ? AND registration_code = ?", username, password).Error; err != nil  {
		return false
	}


	return true
}

func (h *licenseAuthHook) OnConnected(cl *mqtt.Client) {
	h.once.Do(h.initDB)
	if h.dbErr != nil || h.db == nil {
		return
	}
	macAddress := cl.ID
	// In some cases, the client ID might have a prefix, e.g., from a library.
	// Here we assume the MAC address is the client ID.
	// If not, this part needs to be adjusted based on the actual client ID format.
	h.db.Table("equipment").Where("mac_address = ?", macAddress).Update("status", 1)
}

func (h *licenseAuthHook) OnDisconnect(cl *mqtt.Client, err error, fromRemote bool) {
	h.once.Do(h.initDB)
	if h.dbErr != nil || h.db == nil {
		return
	}
	macAddress := cl.ID
	h.db.Table("equipment").Where("mac_address = ?", macAddress).Update("status", 2)
}

func (h *licenseAuthHook) OnACLCheck(cl *mqtt.Client, topic string, write bool) bool {
	// Allow all authenticated clients to subscribe/publish to any topic.
	// A more granular ACL check can be implemented here if needed.
	return true
}
